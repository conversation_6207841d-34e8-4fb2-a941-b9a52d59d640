# Learning Resources

These resources are aimed at giving new members in the QMK community more understanding to the information provided in the Newbs docs.

### QMK resources

* [<PERSON>'s QMK Basics Blog](https://thomasbaart.nl/category/mechanical-keyboards/firmware/qmk/qmk-basics/) – A user-created blog covering the basics of how to use QMK Firmware, as seen from a new user's perspective.

### Command Line resources

* [Good General Tutorial on Command Line](https://www.codecademy.com/learn/learn-the-command-line)
* [Must Know Linux Commands](https://www.guru99.com/must-know-linux-commands.html)<br>
* [Some Basic Unix Commands](https://www.tjhsst.edu/~dhyatt/superap/unixcmd.html)

### Text Editor resources

Not sure which text editor to use?
* [a great introduction to the subject](https://learntocodewith.me/programming/basics/text-editors/)

Editors specifically made for code:
* [Sublime Text](https://www.sublimetext.com/)
* [VS Code](https://code.visualstudio.com/)

### Git resources

* [Great General <PERSON>](https://www.codecademy.com/learn/learn-git)
* [Flight Rules For Git](https://github.com/k88hudson/git-flight-rules)
* [Git Game To Learn From Examples](https://learngitbranching.js.org/)
