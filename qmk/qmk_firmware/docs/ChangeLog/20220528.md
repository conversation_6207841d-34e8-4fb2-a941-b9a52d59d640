# QMK Breaking Changes - 2022 May 28 Changelog

## Notable Features {#notable-features}

### Caps Word ([#16588](https://github.com/qmk/qmk_firmware/pull/16588)) {#caps-word}

This is a new feature that allows for capslock-like functionality that turns itself off at the end of the word.

For instance, if you wish to type "QMK" without holding shift the entire time, you can either tap both left and right shift, or double-tap shift, to turn on _Caps Word_ -- then type `qmk` (lowercase) without holding shift. Once you hit any key other than `a`--`z`, `0`--`9`, `-`, `_`, delete, or backspace, this will go back to normal typing!

There are other activation mechanisms as well as configurable options like timeout and the like -- see the [Caps Word documentation](../features/caps_word) for more information.

### Quantum Painter ([#10174](https://github.com/qmk/qmk_firmware/pull/10174)) {#quantum-painter}

QMK has had support for small OLED displays for some time now, but hasn't really gained too much ability to draw to panels other than the SSD1306 or SH1106 panels.

Quantum Painter is a new drawing subsystem available to suitable ARM and RISC-V boards that is capable of drawing to large panel RGB LCDs and RGB OLEDs. It also allows for a lot more flexibility with a larger set of drawing APIs -- lines, rectangles, circles, ellipses, text, images, and even animations.

The QMK CLI has new commands added to be able to generate images and fonts for Quantum Painter to digest -- it's even capable of converting animated gifs for display on screen.

See the [Quantum Painter documentation](../quantum_painter) for more information on how to set up the displays as well as how to convert images and fonts.

::: warning
Quantum Painter is not supported on AVR due to complexity and size constraints. Boards based on AVR such as ProMicro or Elite-C builds will not be able to leverage Quantum Painter.
:::

### Encoder Mapping ([#13286](https://github.com/qmk/qmk_firmware/pull/13286)) {#encoder-mapping}

One of the long-standing complaints with Encoders is that there has been no easy way to configure them in user keymaps. [#13286](https://github.com/qmk/qmk_firmware/pull/13286) added support for [Encoder Mapping](../features/encoders#encoder-map), which allows users to define encoder functionality in a similar way to their normal keymap.

::: warning
This is not yet supported by QMK Configurator. It is also unlikely to ever be supported by VIA.
:::

## Changes Requiring User Action {#changes-requiring-user-action}

### `RESET` => `QK_BOOT` ([#17037](https://github.com/qmk/qmk_firmware/pull/17037)) {#reset-2-qk_boot}

QMK is always in the process of picking up support for new hardware platforms. One of the side-effects for future integrations has shown that QMK's usage of `RESET` as a keycode is causing naming collisions. As a result, [#17037](https://github.com/qmk/qmk_firmware/pull/17037) changed usages of `RESET` to the new keycode `QK_BOOT` in the majority of default-like keymaps. At this stage the old keycode is still usable but will likely be removed in the next breaking changes cycle. Users with keymaps containing `RESET` should also move to `QK_BOOT`.

### Sendstring keycode overhaul ([#16941](https://github.com/qmk/qmk_firmware/pull/16941)) {#sendstring-keycodes}

Some keycodes used with `SEND_STRING` and its relatives have been deprecated and may have their old keycode usages removed at a later date. The list of [deprecated keycodes](https://github.com/qmk/qmk_firmware/blob/ebd402788346aa6e88bde1486b2a835684d40d39/quantum/send_string_keycodes.h#L456-L505) should be consulted to determine if you're using one of the older names (the first identifier after `#define`) -- you should swap to the newer variant (the second identifier on the same line).

### Pillow Installation ([#17133](https://github.com/qmk/qmk_firmware/pull/17133)) {#pillow-install}

The merge of Quantum Painter added some new dependencies in the QMK CLI, most notably _Pillow_, which requires some installation in order for the CLI to function. If you've got an existing installation, you'll need to run some commands in order to get things working:

On Windows, if using _QMK MSYS_ or _msys2_, you'll need to run the following command:

```sh
pacman --needed --noconfirm --disable-download-timeout -S mingw-w64-x86_64-python-pillow
python3 -m pip install --upgrade qmk
```

On macOS:

```sh
brew update
brew upgrade qmk/qmk/qmk
```

On Linux or WSL:

```sh
python3 -m pip install --user --upgrade qmk
```

### Updated Keyboard Codebases {#updated-keyboard-codebases}

The following keyboards have had their source moved within QMK:

| Old Keyboard Name    | New Keyboard Name  |
|----------------------|--------------------|
| absinthe             | keyhive/absinthe   |
| amj40                | amjkeyboard/amj40  |
| amj60                | amjkeyboard/amj60  |
| amj96                | amjkeyboard/amj96  |
| amjpad               | amjkeyboard/amjpad |
| at101_bh             | viktus/at101_bh    |
| ergosaurus           | keyhive/ergosaurus |
| gmmk/pro/ansi        | gmmk/pro/rev1/ansi |
| gmmk/pro/iso         | gmmk/pro/rev1/iso  |
| honeycomb            | keyhive/honeycomb  |
| lattice60            | keyhive/lattice60  |
| melody96             | ymdk/melody96      |
| mt40                 | mt/mt40            |
| mt64rgb              | mt/mt64rgb         |
| mt84                 | mt/mt84            |
| mt980                | mt/mt980           |
| navi10               | keyhive/navi10     |
| omnikey_bh           | viktus/omnikey_bh  |
| opus                 | keyhive/opus       |
| smallice             | keyhive/smallice   |
| southpole            | keyhive/southpole  |
| uno                  | keyhive/uno        |
| ut472                | keyhive/ut472      |
| wheatfield/blocked65 | mt/blocked65       |
| wheatfield/split75   | mt/split75         |
| z150_bh              | viktus/z150_bh     |

---

## Full changelist {#full-changelist}

Core:
* Quantum Painter ([#10174](https://github.com/qmk/qmk_firmware/pull/10174))
* Add support for encoder mapping. ([#13286](https://github.com/qmk/qmk_firmware/pull/13286))
* Add support for multiple switchs/solenoids to Haptic Feedback engine ([#15657](https://github.com/qmk/qmk_firmware/pull/15657))
* Add compile/make macro to core ([#15959](https://github.com/qmk/qmk_firmware/pull/15959))
* Add Reboot keycode to core ([#15990](https://github.com/qmk/qmk_firmware/pull/15990))
* Add support for multiple sensors to pmw3360 ([#15996](https://github.com/qmk/qmk_firmware/pull/15996))
* Asymmetric encoders, encoder tests. ([#16068](https://github.com/qmk/qmk_firmware/pull/16068))
* Add hacky via support for RGB Matrix ([#16086](https://github.com/qmk/qmk_firmware/pull/16086))
* Allow usage of AVRs minimal printf library ([#16266](https://github.com/qmk/qmk_firmware/pull/16266))
* Squeeze AVR some more with `-mrelax` and `-mcall-prologues` ([#16269](https://github.com/qmk/qmk_firmware/pull/16269))
* Heatmap incorrect matrix effect workaround ([#16315](https://github.com/qmk/qmk_firmware/pull/16315))
* Add SN74x154 driver and convert AL1 custom matrix ([#16331](https://github.com/qmk/qmk_firmware/pull/16331))
* Add customizable snake and knight animation increments ([#16337](https://github.com/qmk/qmk_firmware/pull/16337))
* Chibios USB protocol: allow overriding RAW Capacity ([#16339](https://github.com/qmk/qmk_firmware/pull/16339))
* HD44780 driver rework ([#16370](https://github.com/qmk/qmk_firmware/pull/16370))
* Update wb32-dfu ([#16438](https://github.com/qmk/qmk_firmware/pull/16438))
* Remove `send_unicode_hex_string()` ([#16518](https://github.com/qmk/qmk_firmware/pull/16518))
* Add :flash target for UF2 bootloaders ([#16525](https://github.com/qmk/qmk_firmware/pull/16525))
* Move `has_mouse_report_changed` function to `report.c` ([#16543](https://github.com/qmk/qmk_firmware/pull/16543))
* Move Doxygen docs to subdirectory ([#16561](https://github.com/qmk/qmk_firmware/pull/16561))
* Add Caps Word feature to core ([#16588](https://github.com/qmk/qmk_firmware/pull/16588))
* Add non blackpill F4x1 config files ([#16600](https://github.com/qmk/qmk_firmware/pull/16600))
* Force platform pin defs to be included ([#16611](https://github.com/qmk/qmk_firmware/pull/16611))
* Refactor CTPC logic to allow future converters ([#16621](https://github.com/qmk/qmk_firmware/pull/16621))
* Use a mutex guard for split shared memory ([#16647](https://github.com/qmk/qmk_firmware/pull/16647))
* Rename TICK to TICK_EVENT ([#16649](https://github.com/qmk/qmk_firmware/pull/16649))
* Add GET_TAPPING_TERM macro to reduce duplicate code ([#16681](https://github.com/qmk/qmk_firmware/pull/16681))
* add the ability to change the pwm frequency for the IS31FL3737B ([#16718](https://github.com/qmk/qmk_firmware/pull/16718))
* Joystick feature updates ([#16732](https://github.com/qmk/qmk_firmware/pull/16732))
* Add emulated eeprom support for STM32F303xE ([#16737](https://github.com/qmk/qmk_firmware/pull/16737))
* Refactor writePin to work with statements ([#16738](https://github.com/qmk/qmk_firmware/pull/16738))
* Add mechanism to limit available converters ([#16783](https://github.com/qmk/qmk_firmware/pull/16783))
* Implement XAP 'secure' core requirements ([#16843](https://github.com/qmk/qmk_firmware/pull/16843))
* rgblight: Add functions to stop blinking one or all but one layer ([#16859](https://github.com/qmk/qmk_firmware/pull/16859))
* Expose API for hardware unique ID ([#16869](https://github.com/qmk/qmk_firmware/pull/16869))
* Added support for Wb32fq95 ([#16871](https://github.com/qmk/qmk_firmware/pull/16871))
* Provide better config defaults for bluepill boards ([#16909](https://github.com/qmk/qmk_firmware/pull/16909))
* Joystick: Simplify report descriptor and clean up error messages ([#16926](https://github.com/qmk/qmk_firmware/pull/16926))
* Rename keymap_extras headers for consistency ([#16939](https://github.com/qmk/qmk_firmware/pull/16939))
* Sendstring keycode overhaul ([#16941](https://github.com/qmk/qmk_firmware/pull/16941))
* Move disable_jtag to platforms ([#16960](https://github.com/qmk/qmk_firmware/pull/16960))
* Remove ARM pgm_read_word workaround in rgblight ([#16961](https://github.com/qmk/qmk_firmware/pull/16961))
* Warn about LTO with arm_atsam, not ChibiOS. ([#17106](https://github.com/qmk/qmk_firmware/pull/17106))

CLI:
* Rework generate-api CLI command to use .build directory ([#16441](https://github.com/qmk/qmk_firmware/pull/16441))
* Change data driven "str" type to represent a quoted string literal ([#16516](https://github.com/qmk/qmk_firmware/pull/16516))
* Bump the 'jsonschema' version ([#16635](https://github.com/qmk/qmk_firmware/pull/16635))
* Add frameworking for development board presets ([#16637](https://github.com/qmk/qmk_firmware/pull/16637))
* Extend 'qmk info' to handle keymap level overrides ([#16702](https://github.com/qmk/qmk_firmware/pull/16702))
* Data driven `g_led_config` ([#16728](https://github.com/qmk/qmk_firmware/pull/16728))
* Allow new-keyboard to use development_board presets ([#16785](https://github.com/qmk/qmk_firmware/pull/16785))
* Also format *.hpp files. ([#16997](https://github.com/qmk/qmk_firmware/pull/16997))

Submodule updates:
* ChibiOS 21.11.1 update. ([#16251](https://github.com/qmk/qmk_firmware/pull/16251))
* Update ChibiOS-Contrib ([#16915](https://github.com/qmk/qmk_firmware/pull/16915))

Keyboards:
* chore: Add personal GMMK Pro keymap ([#15320](https://github.com/qmk/qmk_firmware/pull/15320))
* move melody96 to ymdk vendor folder ([#15680](https://github.com/qmk/qmk_firmware/pull/15680))
* move amj keyboards into amjkeyboard vendor folder ([#15733](https://github.com/qmk/qmk_firmware/pull/15733))
* move z150_bh at101_bh omnikey_bh to viktus/ ([#16004](https://github.com/qmk/qmk_firmware/pull/16004))
* MS Sculpt Mobile refactor ([#16038](https://github.com/qmk/qmk_firmware/pull/16038))
* move keyhive exclusive boards into /keyhive ([#16084](https://github.com/qmk/qmk_firmware/pull/16084))
* move 麦田 boards into /mt ([#16095](https://github.com/qmk/qmk_firmware/pull/16095))
* Convert Wasdat Code custom matrix to SN74x138 driver ([#16257](https://github.com/qmk/qmk_firmware/pull/16257))
* Move GMMK Pro to allow for multiple revisions ([#16423](https://github.com/qmk/qmk_firmware/pull/16423))
* Updated pin mapping and readme. ([#16505](https://github.com/qmk/qmk_firmware/pull/16505))
* Map data driven `DESCRIPTION` as string literal ([#16523](https://github.com/qmk/qmk_firmware/pull/16523))
* remove unecessary layers ([#16559](https://github.com/qmk/qmk_firmware/pull/16559))
* Helix/rev2 move to split common ([#16723](https://github.com/qmk/qmk_firmware/pull/16723))
* Remove some layout exceptions ([#16957](https://github.com/qmk/qmk_firmware/pull/16957))
* Refactor legacy quantum keycodes in default-ish keymaps ([#17037](https://github.com/qmk/qmk_firmware/pull/17037))
* Refactor legacy quantum keycodes in default-ish keymaps ([#17150](https://github.com/qmk/qmk_firmware/pull/17150))

Keyboard fixes:
* gboards/gergoplex: move `COMBO_ENABLE` to keymap level ([#16667](https://github.com/qmk/qmk_firmware/pull/16667))
* usb-usb converter: community layout support ([#16773](https://github.com/qmk/qmk_firmware/pull/16773))
* Fix build of `keyhive/uno`. ([#16891](https://github.com/qmk/qmk_firmware/pull/16891))
* Fix uno ([#16892](https://github.com/qmk/qmk_firmware/pull/16892))
* converter/usb_usb: remove surplus commas ([#17024](https://github.com/qmk/qmk_firmware/pull/17024))
* Various fixes for g_led_config lint warnings ([#17104](https://github.com/qmk/qmk_firmware/pull/17104))

Others:
* Add warning for CTPC/CONVERT_TO_PROTON_C. ([#16782](https://github.com/qmk/qmk_firmware/pull/16782))
* Add bluepill/blackpill development board presets ([#16806](https://github.com/qmk/qmk_firmware/pull/16806))
* Recommend pillow as part of manual MSYS install ([#17133](https://github.com/qmk/qmk_firmware/pull/17133))

Bugs:
* Fix one-shot locked modifiers ([#16114](https://github.com/qmk/qmk_firmware/pull/16114))
* Fix missing definition for non-encoder case. ([#16593](https://github.com/qmk/qmk_firmware/pull/16593))
* Fixup builds. ([#16596](https://github.com/qmk/qmk_firmware/pull/16596))
* Missed some erroneous prints. ([#16597](https://github.com/qmk/qmk_firmware/pull/16597))
* Workaround for pin_def errors on KINETIS based builds ([#16614](https://github.com/qmk/qmk_firmware/pull/16614))
* Fix flipped logic bug with One Shot `OS_ON` / `OS_OFF` keys ([#16617](https://github.com/qmk/qmk_firmware/pull/16617))
* Redo workaround for pin_def errors on KINETIS ([#16620](https://github.com/qmk/qmk_firmware/pull/16620))
* Fix oneshot toggle logic ([#16630](https://github.com/qmk/qmk_firmware/pull/16630))
* Mousekeys fix ([#16640](https://github.com/qmk/qmk_firmware/pull/16640))
* Ignore transport defaults if SPLIT_KEYBOARD is unset ([#16706](https://github.com/qmk/qmk_firmware/pull/16706))
* Fixes #16705 : digital rain follows val ([#16716](https://github.com/qmk/qmk_firmware/pull/16716))
* Fix AVR backlight breathing: low brightness limit & exceeding breathing table max index ([#16770](https://github.com/qmk/qmk_firmware/pull/16770))
* Fixed usb read loops not reading until timeout ([#16827](https://github.com/qmk/qmk_firmware/pull/16827))
* [QP] Check BPP capabilities before loading the palette ([#16863](https://github.com/qmk/qmk_firmware/pull/16863))
* Fix #16859. ([#16865](https://github.com/qmk/qmk_firmware/pull/16865))
* Preinstall python dependencies before executing `qmk`. ([#16874](https://github.com/qmk/qmk_firmware/pull/16874))
* Fixup AVR builds. ([#16875](https://github.com/qmk/qmk_firmware/pull/16875))
* Fix kinetic mouse mode ([#16951](https://github.com/qmk/qmk_firmware/pull/16951))
* Enhancement and fixes of "Secure" feature ([#16958](https://github.com/qmk/qmk_firmware/pull/16958))
* Check for ongoing transfers on the OUT endpoint ([#16974](https://github.com/qmk/qmk_firmware/pull/16974))
* MSYS2 install: add some Python dependencies through Pacman ([#17025](https://github.com/qmk/qmk_firmware/pull/17025))
* Revert "Fix kinetic mouse mode (#16951)" ([#17095](https://github.com/qmk/qmk_firmware/pull/17095))
* Workaround for recent -Werror=array-bounds AVR issues ([#17136](https://github.com/qmk/qmk_firmware/pull/17136))
* Bug fix: Continue Caps Word when AltGr (right Alt) is held. ([#17156](https://github.com/qmk/qmk_firmware/pull/17156))
