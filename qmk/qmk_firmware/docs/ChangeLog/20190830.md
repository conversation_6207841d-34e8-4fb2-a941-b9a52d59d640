# QMK Breaking Change - 2019 Aug 30

Four times a year QMK runs a process for merging Breaking Changes. A Breaking Change is any change which modifies how QMK behaves in a way that is incompatible or potentially dangerous. We limit these changes to 4 times per year so that users can have confidence that updating their QMK tree will not break their keymaps.

This document marks the inaugural Breaking Change merge. A list of changes follows.

## Core code formatting with clang-format

* All core files (`drivers/`, `quantum/`, `tests/`, and `tmk_core/`) have been formatted with clang-format
* A travis process to reformat PRs on merge has been instituted
* You can use the new CLI command `qmk cformat` to format before submitting your PR if you wish.

## LUFA USB descriptor cleanup

* Some code cleanups related to the USB HID descriptors on AVR keyboards, to make them easier to read and understand
* More information: see https://github.com/qmk/qmk_firmware/pull/4871
* No behaviour changes anticipated and no keymaps modified

## Migrating `ACTION_LAYER_MOMENTARY()` entries in `fn_actions` to `MO()` keycodes

* `fn_actions` is deprecated, and its functionality has been superseded by direct keycodes and `process_record_user()`
* The end result of removing this obsolete feature should result in a decent reduction in firmware size and code complexity
* All keymaps affected are recommended to switch away from `fn_actions` in favour of the [custom keycode](../custom_quantum_functions) and [macro](../feature_macros) features

## Update Atreus to current code conventions

* Duplicate include guards have bypassed the expected header processing behavior
* All keymaps affected are recommended to remove duplication of `<keyboard>/config.h` to `<keyboard>/keymaps/<user>/config.h` and only provide overrides at the keymap level

## Backport changes to keymap language files from ZSA fork

* Fixes an issue in the `keymap_br_abnt2.h` file that includes the wrong source (`keymap_common.h` instead of `keymap.h`)
* Updates the `keymap_swedish.h` file to be specific to swedish, and not just "nordic" in general.
* Any keymaps using this will need to remove `NO_*` and replace it with `SE_*`.

## Update repo to use LUFA as a git submodule

* `/lib/LUFA` removed from the repo
* LUFA set as a submodule, pointing to qmk/lufa
* This should allow more flexibility with LUFA, and allow us to keep the sub-module up to date, a lot more easily.  It was ~2 years out of date with no easy path to fix that.  This prevents that from being an issue in the future

## Migrating `ACTION_BACKLIGHT_*()` entries in `fn_actions` to `BL_` keycodes

* `fn_actions` is deprecated, and its functionality has been superseded by direct keycodes and `process_record_user()`
* All keymaps using these actions have had the relevant `KC_FN*` keys replaced with the equivalent `BL_*` keys
* If you currently use `KC_FN*` you will need to replace `fn_actions` with the [custom keycode](../custom_quantum_functions) and [macro](../feature_macros) features

## Remove `KC_DELT` alias in favor of `KC_DEL`

* `KC_DELT` was a redundant, undocumented alias for `KC_DELETE`
* It has been removed and all its uses replaced with the more common `KC_DEL` alias
* Around 90 keymaps (mostly for ErgoDox boards) have been modified as a result
