# QMK Breaking Change - 2020 May 30 Changelog

Four times a year QMK runs a process for merging Breaking Changes. A Breaking Change is any change which modifies how QMK behaves in a way that is incompatible or potentially dangerous. We limit these changes to 4 times per year so that users can have confidence that updating their QMK tree will not break their keymaps.

The list of changes follows.


## Core Changes

### Converting V-USB usbdrv to a submodule

[#8321](https://github.com/qmk/qmk_firmware/pull/8321) and [qmk_compiler#62](https://github.com/qmk/qmk_compiler/pull/62).

These PRs move the V-USB driver code out of the qmk_firmware repository and into a submodule pointed at https://github.com/obdev/v-usb. This will make it easier to update the codebase if needed, while applying any potential QMK-specific modifications by forking it to the QMK GitHub organization.

### Unify Tap Hold functions and documentation

[#8348](https://github.com/qmk/qmk_firmware/pull/8348)

Updates all of the per key tap-hold functions to pass the `keyrecord_t` structure, and include documentation changes.

Any remaining versions or code outside of the main repo will need to be converted: 
| Old function                                         | New Function                                                              |
|------------------------------------------------------|---------------------------------------------------------------------------|
|`uint16_t get_tapping_term(uint16_t keycode)`         |`uint16_t get_tapping_term(uint16_t keycode, keyrecord_t *record)`         |
|`bool get_ignore_mod_tap_interrupt(uint16_t keycode)` |`bool get_ignore_mod_tap_interrupt(uint16_t keycode, keyrecord_t *record)` |

### Python Required In The Build Process

[#9000](https://github.com/qmk/qmk_firmware/pull/9000)

This is the last release of QMK that will work without having Python 3.6 (or later) installed. If your environment is not fully setup you will get a warning instructing you to set it up.

After the next breaking change you will not be able to build if `bin/qmk hello` does not work.

### Upgrade from tinyprintf to mpaland/printf

[#8269](https://github.com/qmk/qmk_firmware/pull/8269)

- Provides debug functionality on ChibiOS/ARM that is more compliant than previous integrations.
- Less maintenence, fewer QMK customisations, and allows QMK to sidestep previous compile and runtime issues.
- A `make git-submodule` may be required after pulling the latest QMK Firmware code to update to the new dependency.

### Fixed RGB_DISABLE_AFTER_TIMEOUT to be seconds based & small internals cleanup

[#6480](https://github.com/qmk/qmk_firmware/pull/6480)

- Changes `RGB_DISABLE_AFTER_TIMEOUT` to be based on milliseconds instead of ticks.
- Includes a code cleanup, resulting in a savings of 100 bytes, depending on features used.
- Fixed issues with timeouts / suspending at the wrong time not turning off all LEDs in some cases.

The `RGB_DISABLE_AFTER_TIMEOUT` definition is now deprecated, and has been superseded by `RGB_DISABLE_TIMEOUT`. To use the new definition, rename `RGB_DISABLE_AFTER_TIMEOUT` to `RGB_DISABLE_TIMEOUT` in your `config.h` file, and multiply the value set by 1200.

Before: `#define RGB_DISABLE_AFTER_TIMEOUT 100`  
After: `#define RGB_DISABLE_TIMEOUT 120000`

### Switch to qmk forks for everything

[#9019](https://github.com/qmk/qmk_firmware/pull/9019)

Fork all QMK submodules to protect against upstream repositories disappearing.

### code cleanup regarding deprecated macro PLAY_NOTE_ARRAY by replacing it with PLAY_SONG

[#8484](https://github.com/qmk/qmk_firmware/pull/8484)

Removes the deprecated `PLAY_NOTE_ARRAY` macro. References to it are replaced with `PLAY_SONG`, which references the same function.

### fixing wrong configuration of AUDIO feature

[#8903](https://github.com/qmk/qmk_firmware/pull/8903) and [#8974](https://github.com/qmk/qmk_firmware/pull/8974)

`audio_avr.c` does not default to any pin; there has to be a #define XX_AUDIO in config.h at some level for Audio to actually work. Otherwise, the Audio code ends up cluttering the firmware, possibly breaking builds because the maximum allowed firmware size is exceeded.

These changes fix this by disabling Audio on keyboards that have the feature misconfigured, and therefore non-functional.

Also, add a compile-time error to alert the user to a missing pin-configuration (on AVR boards) when `AUDIO_ENABLE = yes` is set.


## Keyboard Refactors

### Migrating Lily58 to use split_common

[#6260](https://github.com/qmk/qmk_firmware/pull/6260)

Modifies the default firmware for Lily58 to use the `split_common` library, instead of including and depending on its own set of libraries for the following functionality:

- SSD1306 display
- i2c for OLED
- Serial Communication

This allows current lily58 firmware to advance with updates to the `split_common` library, which is shared with many other split keyboards.

#### To migrate existing Lily58 firmware:

[Changes to `config.h`](https://github.com/qmk/qmk_firmware/pull/6260/files#diff-445ac369c8717dcd6fc6fc3630836fc1):
- Remove `#define SSD1306OLED` from config.h


[Changes to `keymap.c`](https://github.com/qmk/qmk_firmware/pull/6260/files#diff-20943ea59856e9bdf3d99ecb2eee40b7):
- Find/Replace each instance of `#ifdef SSD1306OLED` with `#ifdef OLED_DRIVER_ENABLE`
- The following changes are for compatibility with the OLED driver. If you don't use the OLED driver you may safely delete [this section](https://github.com/qmk/qmk_firmware/blob/e6b9980bd45c186f7360df68c24b6e05a80c10dc/keyboards/lily58/keymaps/default/keymap.c#L144-L190)
- Alternatively, if you did not change the OLED code from that in `default`, you may find it easier to simply copy the [relevant section](https://github.com/qmk/qmk_firmware/blob/4ac310668501ae6786c711ecc8f01f62ddaa1c0b/keyboards/lily58/keymaps/default/keymap.c#L138-L172). Otherwise, the changes you need to make are as follows (sample change [here](https://github.com/qmk/qmk_firmware/pull/6260/files#diff-20943ea59856e9bdf3d99ecb2eee40b7R138-R173))
- [Remove](https://github.com/qmk/qmk_firmware/pull/6260/files#diff-20943ea59856e9bdf3d99ecb2eee40b7L138-L141) the block
```c
#ifdef SSD1306OLED	
  iota_gfx_init(!has_usb());   // turns on the display	
#endif
```
- Within the block bounded by `#ifdef OLED_DRIVER_ENABLE` and `#endif // OLED_DRIVER_ENABLE`, add the following block to ensure that your two OLEDs are rotated correctly across the left and right sides:
```c
oled_rotation_t oled_init_user(oled_rotation_t rotation) {
  if (!is_keyboard_master())
    return OLED_ROTATION_180;  // flips the display 180 degrees if offhand
  return rotation;
}
```
- Remove the functions `matrix_scan_user`, `matrix_update` and `iota_gfx_task_user`
- Find/Replace `matrix_render_user(struct CharacterMatrix *matrix)` with `iota_gfx_task_user(void)`
- Find/Replace `is_master` with `is_keyboard_master()`
- For each instance of `matrix_write_ln(matrix, display_fn())`, rewrite it as `oled_write_ln(read_layer_state(), false);`
- For each instance of `matrix_write(matrix, read_logo());`, replace with `oled_write(read_logo(), false);`

### Refactor zinc to use split_common

[#7114](https://github.com/qmk/qmk_firmware/pull/7114) and [#9171](https://github.com/qmk/qmk_firmware/pull/9171)

* Refactor to use split_common and remove split codes under the zinc/revx/
* Add - backlight RGB LED and/or underglow RGB LED option
* Add - continuous RGB animations feature (between L and R halves) 
* Fix - keymap files to adapt to changes
    * all authors of keymaps confirmed this PR
* Update - documents and rules.mk

### Refactor of TKC1800 to use common OLED code

[#8472](https://github.com/qmk/qmk_firmware/pull/8472)

Modifies the default firmware for TKC1800 to use the in-built I2C and OLED drivers, instead of including and depending on its own set of libraries for the following functionality:

- SSD1306 display
- i2c for OLED

This allows current TKC1800 firmware to advance with updates to those drivers, which are shared with other keyboards.

#### To migrate existing TKC1800 firmware:

[Changes to `config.h`](https://github.com/qmk/qmk_firmware/pull/8472/files#diff-d10b26e676b4a55cbb00d71955116526):
- Remove `#define SSD1306OLED` from config.h

[Changes to `tkc1800.c`](https://github.com/qmk/qmk_firmware/pull/8472/files#diff-3b35bd30abe89c8110717c6972cd2cc5):
- Add the following to avoid debug errors on HID_listen if the screen is not present
```c
void keyboard_pre_init_kb(void) {
  setPinInputHigh(D0);
  setPinInputHigh(D1);

  keyboard_pre_init_user();
}
```

[Changes to `keymap.c`](https://github.com/qmk/qmk_firmware/pull/8472/files#diff-05a2a344ce27e4d045fe68520ccd4771):
- Find/Replace each instance of `#ifdef SSD1306OLED` with `#ifdef OLED_DRIVER_ENABLE`
- The following changes are for compatibility with the OLED driver. If you don't use the OLED driver you may safely delete [this section](https://github.com/qmk/qmk_firmware/blob/e6b9980bd45c186f7360df68c24b6e05a80c10dc/keyboards/lily58/keymaps/default/keymap.c#L144-L190)
- [Remove](https://github.com/qmk/qmk_firmware/pull/6260/files#diff-20943ea59856e9bdf3d99ecb2eee40b7L91-L158) the block
```c
#ifdef SSD1306OLED	
  iota_gfx_init(!has_usb());   // turns on the display	
#endif
```
- Within the block bounded by `#ifdef OLED_DRIVER_ENABLE` and `#endif // OLED_DRIVER_ENABLE`, add the following block to ensure that your two OLEDs are rotated correctly across the left and right sides:
```c
oled_rotation_t oled_init_user(oled_rotation_t rotation) {
  if (!is_keyboard_master())
    return OLED_ROTATION_180;  // flips the display 180 degrees if offhand
  return rotation;
}
```
- Remove the function `iota_gfx_task_user`

### Split HHKB to ANSI and JP layouts and Add VIA support for each

[#8582](https://github.com/qmk/qmk_firmware/pull/8582)

- Splits the HHKB codebase into two separate folders `keyboards/hhkb/ansi` and `keyboards/hhkb/jp`.
- Adds VIA Configurator support for both versions.

#### Migrating existing HHKB keymaps

- Remove any checks for the `HHKB_JP` definition
  - All checks for this definition have been removed, and each version uses the source that is appropriate to that version.
- Move the directory for your keymap into the appropriate `keymaps` directory
  - `keyboards/hhkb/ansi/keymaps/` for ANSI HHKBs
  - `keyboards/hhkb/jp/keymaps/` for HHKB JPs
- Compile with the new keyboard names
  - This PR changes the compilation instructions for the HHKB Alternate Controller. To compile firmware for this controller moving forward, use:
    - `make hhkb/ansi` for ANSI-layout HHKBs
    - `make hhkb/jp` for HHKB JP keyboards


## Keyboard Moves

- [#8412](https://github.com/qmk/qmk_firmware/pull/8412 "Changing board names to prevent confusion") by blindassassin111
- [#8499](https://github.com/qmk/qmk_firmware/pull/8499 "Move the Keyboardio Model01 to a keyboardio/ subdir") by algernon
- [#8830](https://github.com/qmk/qmk_firmware/pull/8830 "Move spaceman keyboards") by Spaceman (formerly known as Rionlion100)
- [#8537](https://github.com/qmk/qmk_firmware/pull/8537 "Organizing my keyboards (plaid, tartan, ergoinu)") by hsgw

Keyboards by Keyboardio, Spaceman, and hsgw move to vendor folders, while PCBs designed by blindassassin111 are renamed.

Old Name           | New Name
:----------------- | :-----------------
2_milk             | spaceman/2_milk
at101_blackheart   | at101_bh
ergoinu            | dm9records/ergoinu
model01            | keyboardio/model01
omnikey_blackheart | omnikey_bh
pancake            | spaceman/pancake
plaid              | dm9records/plaid
tartan             | dm9records/tartan
z150_blackheart    | z150_bh

If you own one of these PCBs, please use the new names to compile your firmware moving forward.


## Keycode Migration PRs

[#8954](https://github.com/qmk/qmk_firmware/pull/8954 "Migrate `ACTION_LAYER_TOGGLE` to `TG()`"), [#8957](https://github.com/qmk/qmk_firmware/pull/8957 "Migrate `ACTION_MODS_ONESHOT` to `OSM()`"), [#8958](https://github.com/qmk/qmk_firmware/pull/8958 "Migrate `ACTION_DEFAULT_LAYER_SET` to `DF()`"), [#8959](https://github.com/qmk/qmk_firmware/pull/8959 "Migrate `ACTION_LAYER_MODS` to `LM()`"), [#8968](https://github.com/qmk/qmk_firmware/pull/8968 "Migrate `ACTION_MODS_TAP_KEY` to `MT()`"), [#8977](https://github.com/qmk/qmk_firmware/pull/8977 "Migrate miscellaneous `fn_actions` entries"), and [#8979](https://github.com/qmk/qmk_firmware/pull/8979 "Migrate `ACTION_MODS_KEY` to chained mod keycodes")

Authored by fauxpark, these pull requests remove references to deprecated TMK macros that have been superseded by native QMK keycodes.

Old `fn_actions` action | New QMK keycode
:---------------------- | :--------------
`ACTION_DEFAULT_LAYER_SET(layer)` | `DF(layer)`
`ACTION_LAYER_MODS(layer, mod)` | `LM(layer, mod)`
`ACTION_LAYER_ONESHOT(mod)` | `OSL(mod)`
`ACTION_LAYER_TOGGLE(layer)` | `TG(layer)`
`ACTION_MODS_ONESHOT(mod)` | `OSM(mod)`
`ACTION_MODS_TAP_KEY(mod, kc)` | `MT(mod, kc)`
`ACTION_MODS_KEY(mod, kc)`<br>e.g. `ACTION_MODS_KEY(MOD_LCTL, KC_0)` | `MOD(kc)`<br>e.g. `LCTL(KC_0)`
