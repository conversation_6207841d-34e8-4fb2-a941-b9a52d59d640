# QMK Breaking Change - 2020 Feb 29 Changelog

Four times a year QMK runs a process for merging Breaking Changes. A Breaking Change is any change which modifies how QMK behaves in a way that is incompatible or potentially dangerous. We limit these changes to 4 times per year so that users can have confidence that updating their QMK tree will not break their keymaps.


## Update ChibiOS/ChibiOS-Contrib/uGFX submodules

* General Notes
    * A `make git-submodule` may be required after pulling the latest QMK firmware code to update affected submodules to the upgraded revisions
    * Enabling link-time-optimization (`LINK_TIME_OPTIMIZATION_ENABLE = yes`) should work on a lot more boards
* Upgrade to ChibiOS ver19.1.3
    * This will allow QMK to update to upstream ChibiOS a lot easier -- the old version was ~2 years out of date. Automated update scripts have been made available to simplify future upgrades.
    * Includes improved MCU support and bugfixes
    * ChibiOS revision is now included in Command output
    * Timers should now be more accurate
* Upgrade to newer ChibiOS-Contrib
    * Also includes improved MCU support and bugfixes
    * ChibiOS-Contrib revision is now included in Command output
* Upgrade to newer uGFX
    * Required in order to support updated ChibiOS


## Fix ChibiOS timer overflow for 16-bit SysTick devices

* On 16-bit SysTick devices, the timer subsystem in QMK was incorrectly dealing with overflow.
    * When running at a 100000 SysTick frequency (possible on 16-bit devices, but uncommon), this overflow would occur after 0.65 seconds.
* Timers are now correctly handling this overflow case and timing should now be correct on ChibiOS/ARM.


## Update LUFA submodule

* Updates the LUFA submodule to include updates from upstream (abcminiuser/lufa)
* Includes some cleanup for QMK DFU generation


## Encoder flip

* Flips the encoder direction so that `clockwise == true` is for actually turning the knob clockwise
* Adds `ENCODER_DIRECTION_FLIP` define, so that reversing the expected dirction is simple for users.
* Cleans up documentation page for encoders


## Adding support for `BACKLIGHT_ON_STATE` for hardware PWM backlight

* Previously, the define only affected software PWM, and hardware PWM always assumed an N-channel MOSFET.
* The hardware PWM backlight setup has been updated to respect this option.
* The default "on" state has been changed to `1` - **this impacts all keyboards using software PWM backlight that do not define it explicitly**. If your keyboard's backlight is acting strange, it may have a P-channel MOSFET, and will need to have `#define BACKLIGHT_ON_STATE 0` added to the keyboard-level `config.h`. Please see the PR for more detailed information.


## Migrating `ACTION_LAYER_TAP_KEY()` entries in `fn_actions` to `LT()` keycodes

* `fn_actions` is deprecated, and its functionality has been superseded by direct keycodes and `process_record_user()`
* The end result of removing this obsolete feature should result in a decent reduction in firmware size and code complexity
* All keymaps affected are recommended to switch away from `fn_actions` in favour of the [custom keycode](../custom_quantum_functions) and [macro](../feature_macros) features


## Moving backlight keycode handling to `process_keycode/`

* This refactors the backlight keycode logic to be clearer and more modular.
* All backlight-related keycodes are now actioned in a single file.
* The `ACTION_BACKLIGHT_*` macros have also been deleted. If you are still using these in a `fn_actions[]` block, please switch to using the backlight keycodes or functions directly.


## Refactor Planck keymaps to use Layout Macros

* Refactor Planck keymaps to use layout macros instead of raw matrix assignments
* Makes keymaps revision-agnostic
* Should reduce noise and errors in Travis CI logs


## GON NerD codebase refactor

* Splits the codebase for GON NerD 60 and NerdD TKL PCBs into two separate directories.
* If your keymap is for a NerD 60 PCB, your `make` command is now `make gon/nerd60:<keymap>`.
* If your keymap is for a NerD TKL PCB, your `make` command is now `make gon/nerdtkl:<keymap>`.
