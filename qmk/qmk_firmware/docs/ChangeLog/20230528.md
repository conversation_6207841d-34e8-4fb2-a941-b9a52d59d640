# QMK Breaking Changes - 2023 May 28 Changelog

## Notable Changes {#notable-changes}

As per last breaking changes cycle, there has been _a lot_ of emphasis on behind-the-scenes changes, mainly around migration of configurables into `info.json` files, cleanup of `info.json` files, additional layout definitions for keyboards, adding support for general community layouts to keyboards, as well as addressing technical debt.

Of note for keyboard designers:

* Layout and matrix definitions in `info.json` are now _mandatory_ for merge into QMK.
    * Layout macros in `<yourkeyboard>.h` are no longer accepted into QMK Firmware.
    * Existing keyboards have been meticulously converted by the QMK collaborators
    * Layouts missing from keyboard definitions have been added in the process
    * Keys within layouts should not specify `"w":1` or `"h":1` if the key size is 1 -- `w`/`h` should only be present for sizes other than 1
* `config_common.h` has been removed and should not be present anywhere in your keyboard code.
* `RGB_DI_PIN` will now cause an error during build:
    * For WS2812-like LEDs, this should be moved to `info.json`: `"ws2812": { "pin": "xxx" }`
    * For APA102 LEDs, this should be moved to `info.json`: `"apa102": { "data_pin": "xxx" }`
* Other mandatory data-driven changes should be automatically flagged during build
* Keymaps with `encoder_map` should now have the following change made:
    * `encoder_map[][NUM_ENCODERS][2]` => `encoder_map[][NUM_ENCODERS][NUM_DIRECTIONS]`
    * Users assumed the `2` referred to the number of encoders, rather than the number of directions (which is always 2)

### Repeat last key ([#19700](https://github.com/qmk/qmk_firmware/pull/19700)) {#repeat-last-key}

A new pair of keys has been added to QMK -- namely `QK_REPEAT_KEY` and `QK_ALT_REPEAT_KEY` (shortened: `QK_REP`/`QK_AREP`). These allow you to repeat the last key pressed, or in the case of the alternate key, press the "opposite" of the last key. For example, if you press `KC_LEFT`, pressing `QK_REPEAT_KEY` afterwards repeats `KC_LEFT`, but pressing `QK_ALT_REPEAT_KEY` instead sends `KC_RIGHT`.

The full list of default alternate keys is available on the [Repeat Key](../features/repeat_key) documentation.

To enable these keys, in your keymap's `rules.mk`, add:

```make
REPEAT_KEY_ENABLE = yes
```

...and add them to your keymap.

### User callback for pre process record ([#20584](https://github.com/qmk/qmk_firmware/pull/20584)) {#user-callback-for-pre-process-record}

Two new boolean callback functions, `pre_process_record_kb` and `pre_process_record_user`, have been added. They are called at the beginning of `process_record`, right before `process_combo`.

Similar to existing `*_kb` and `*_user` callback functions, returning `false` will halt further processing of key events. The `pre_process_record_user` function will allow user space opportunity to handle or capture an input before it undergoes quantum processing. For example, while action tapping is still resolving the tap or hold output of a mod-tap key, `pre_process_record_user` can capture the next key record of an input event that follows. That key record can be used to influence the [decision of the mod-tap](../tap_hold) key that is currently undergoing quantum processing.

### Consolidate modelm ([#14996](https://github.com/qmk/qmk_firmware/pull/14996) {#consolidate-modelm}

Several build targets for the IBM Model M were cluttered in different folders. The maintainers of several Model M replacement controller projects agreed to consolidate them under one common folder.

The list of all moved keyboard locations is listed [below](20230528#updated-keyboard-codebases).

## Changes Requiring User Action {#changes-requiring-user-action}

### `IGNORE_MOD_TAP_INTERRUPT` behaviour changes ([#20211](https://github.com/qmk/qmk_firmware/pull/20211)) {#i-m-t-i}

Following up from the last breaking changes cycle, `IGNORE_MOD_TAP_INTERRUPT` has been removed and if present in keymap code, will now fail to build. The previous functionality for `IGNORE_MOD_TAP_INTERRUPT` is now default, and should you wish to revert to the old behaviour, you can use `HOLD_ON_OTHER_KEY_PRESS` instead.

For more information, you are invited to read the section on [HOLD_ON_OTHER_KEY_PRESS](../tap_hold#hold-on-other-key-press) in the page on [Tap-Hold configuration options](../tap_hold).

### Updated Keyboard Codebases {#updated-keyboard-codebases}

| Old Keyboard Name               | New Keyboard Name                   |
|---------------------------------|-------------------------------------|
| ashpil/modelm_usbc              | ibm/model_m/ashpil_usbc             |
| binepad/bn009r2                 | binepad/bn009/r2                    |
| converter/modelm101             | ibm/model_m/teensypp                |
| converter/modelm101_teensy2     | ibm/model_m/teensy2                 |
| converter/modelm_ssk            | ibm/model_m_ssk/teensypp_ssk        |
| durgod/dgk6x/hades              | durgod/dgk6x/hades_ansi             |
| handwired/ibm122m               | ibm/model_m_122/ibm122m             |
| jacky_studio/piggy60/hotswap    | jacky_studio/piggy60/rev1/hotswap   |
| jacky_studio/piggy60/solder     | jacky_studio/piggy60/rev1/solder    |
| kamigakushi                     | jaykeeb/kamigakushi                 |
| massdrop/thekey                 | drop/thekey/v1                      |
| massdrop/thekey_v2              | drop/thekey/v2                      |
| mschwingen/modelm               | ibm/model_m/mschwingen              |
| tronguylabs/m122_3270           | ibm/model_m_122/m122_3270           |
| tronguylabs/m122_3270/blackpill | ibm/model_m_122/m122_3270/blackpill |
| tronguylabs/m122_3270/bluepill  | ibm/model_m_122/m122_3270/bluepill  |
| tronguylabs/m122_3270/teensy    | ibm/model_m_122/m122_3270/teensy    |
| yugo_m/model_m_101              | ibm/model_m/yugo_m                  |

## Notable core changes {#notable-core}

### Encoder functionality fallback ([#20320](https://github.com/qmk/qmk_firmware/pull/20320)) {#encoder-functionality-fallback}

For keyboards who have not yet been migrated to encoder map, a default set of encoder functionality is now enabled, gracefully degrading functionality depending on which flags are enabled by the keyboard:

* If `EXTRAKEY_ENABLE` is enabled by the keyboard, the encoder will be mapped to `KC_VOLU`/`KC_VOLD`
* If `MOUSEKEY_ENABLE` is enabled by the keyboard, the encoder will be mapped to `KC_MS_WH_UP`/`KC_MS_WH_DOWN`
* Otherwise, `KC_PGDN`/`KC_PGUP` will be used

Additionally, this ensures that builds on QMK Configurator produce some sort of usable encoder mapping.

### OLED Driver Improvements ([#20331](https://github.com/qmk/qmk_firmware/pull/20331)) {#oled-driver-improvements}

The "classic" OLED driver picked up support for additional sizes of OLED displays, support for the SH1107 controller, and SPI-based OLED support.

Other configurable items are available and can be found on the [OLED Driver page](../features/oled_driver).

## Full changelist {#full-changelist}

Core:
* Refactor `keyevent_t` for 1ms timing resolution ([#15847](https://github.com/qmk/qmk_firmware/pull/15847))
* PS/2 PIO Driver for RP2040 ([#17893](https://github.com/qmk/qmk_firmware/pull/17893))
* Relocate various modifier defines ([#18638](https://github.com/qmk/qmk_firmware/pull/18638))
* Added PMW3320 driver ([#19543](https://github.com/qmk/qmk_firmware/pull/19543))
* Keymap introspection for combos. ([#19670](https://github.com/qmk/qmk_firmware/pull/19670))
* Add direction to dynamic_macro_record_start_user ([#19689](https://github.com/qmk/qmk_firmware/pull/19689))
* Add Repeat Key ("repeat last key") as a core feature. ([#19700](https://github.com/qmk/qmk_firmware/pull/19700))
* [Cleanup] Quantum Painter ([#19825](https://github.com/qmk/qmk_firmware/pull/19825))
* Improve robustness of AW20216 driver ([#19849](https://github.com/qmk/qmk_firmware/pull/19849))
* Make "detected_host_os()" available on the SLAVE side of the split keyboard ([#19854](https://github.com/qmk/qmk_firmware/pull/19854))
* Add RP2040 Community Edition alias for splitkb.com's Liatris controller ([#19966](https://github.com/qmk/qmk_firmware/pull/19966))
* Remove some use of keymap.h ([#19980](https://github.com/qmk/qmk_firmware/pull/19980))
* Merge upstream changes to uf2conv ([#19993](https://github.com/qmk/qmk_firmware/pull/19993))
* Remove keymap.h ([#20004](https://github.com/qmk/qmk_firmware/pull/20004))
* Remove some use of keymap.h ([#20006](https://github.com/qmk/qmk_firmware/pull/20006))
* Quantum Painter QoL enhancements -- auto-poweroff, auto-flush, buffer sizing ([#20013](https://github.com/qmk/qmk_firmware/pull/20013))
* Make Pointing Device Auto Layer more configurable ([#20061](https://github.com/qmk/qmk_firmware/pull/20061))
* Add last activity functions for pointing device ([#20079](https://github.com/qmk/qmk_firmware/pull/20079))
* Caps Word "Invert on shift" option:  pressing Shift inverts the shift state. ([#20092](https://github.com/qmk/qmk_firmware/pull/20092))
* Remove bootloader logic from `mcu_selection.mk` ([#20150](https://github.com/qmk/qmk_firmware/pull/20150))
* Update qmk_cli container references ([#20154](https://github.com/qmk/qmk_firmware/pull/20154))
* Clean up APA102 config and add DD mapping ([#20159](https://github.com/qmk/qmk_firmware/pull/20159))
* Sync activity timestamps between sides. ([#20192](https://github.com/qmk/qmk_firmware/pull/20192))
* Update Doxygen comments for some headers ([#20194](https://github.com/qmk/qmk_firmware/pull/20194))
* Make IGNORE_MOD_TAP_INTERRUPT the default behaviour for mod-taps ([#20211](https://github.com/qmk/qmk_firmware/pull/20211))
* Add some helpers to tidy up XAP ([#20235](https://github.com/qmk/qmk_firmware/pull/20235))
* Tidy up duplication of MIN/MAX fallback implementations ([#20236](https://github.com/qmk/qmk_firmware/pull/20236))
* Optionally keep intermediate file listings in order to do comparisons between builds. ([#20237](https://github.com/qmk/qmk_firmware/pull/20237))
* Add basic profiler. ([#20238](https://github.com/qmk/qmk_firmware/pull/20238))
* WS2812 driver improvements ([#20262](https://github.com/qmk/qmk_firmware/pull/20262))
* typing_heatmap: Add macro to configure increase steps ([#20300](https://github.com/qmk/qmk_firmware/pull/20300))
* Migrate `rgblight.pin` and `RGB_DI_PIN` to `ws2812.pin` ([#20303](https://github.com/qmk/qmk_firmware/pull/20303))
* Delete config_common.h ([#20312](https://github.com/qmk/qmk_firmware/pull/20312))
* Allow EEPROM_DRIVER from info.json ([#20313](https://github.com/qmk/qmk_firmware/pull/20313))
* rp2040: *_PAL_MODE overridable for this platform too ([#20314](https://github.com/qmk/qmk_firmware/pull/20314))
* Add core/fallback encoder behaviour ([#20320](https://github.com/qmk/qmk_firmware/pull/20320))
* OLED Driver improvements ([#20331](https://github.com/qmk/qmk_firmware/pull/20331))
* [Chore] Remove stray mod tap interrupt defines and per key functions ([#20347](https://github.com/qmk/qmk_firmware/pull/20347))
* Add swap hands toggle functions ([#20381](https://github.com/qmk/qmk_firmware/pull/20381))
* Prevent Tri-Layer keys from stopping caps word ([#20398](https://github.com/qmk/qmk_firmware/pull/20398))
* quantum/action_util.c: Use uint8_t for oneshot_layer_data ([#20423](https://github.com/qmk/qmk_firmware/pull/20423))
* Encoder map direction define. ([#20454](https://github.com/qmk/qmk_firmware/pull/20454))
* Realign and size check EECONFIG structures ([#20541](https://github.com/qmk/qmk_firmware/pull/20541))
* Clean up ISSI drivers, Add IS31FL3736 support ([#20572](https://github.com/qmk/qmk_firmware/pull/20572))
* Add a user callback for pre process record ([#20584](https://github.com/qmk/qmk_firmware/pull/20584))
* Disable debug on QP's internal task ([#20623](https://github.com/qmk/qmk_firmware/pull/20623))
* Add required string header file ([#20638](https://github.com/qmk/qmk_firmware/pull/20638))
* Add Develop is31fl3736 multi drivers ([#20642](https://github.com/qmk/qmk_firmware/pull/20642))
* Support PS/2 mouse 9-bit output with MOUSE_EXTENDED_REPORT ([#20734](https://github.com/qmk/qmk_firmware/pull/20734))
* BIOI G60/Morgan65: use custom Bluetooth driver ([#20897](https://github.com/qmk/qmk_firmware/pull/20897))
* Move `pre_process_record_kb()` before `process_combo()` ([#20969](https://github.com/qmk/qmk_firmware/pull/20969))
* Implement UF2 device type id extension tag ([#21029](https://github.com/qmk/qmk_firmware/pull/21029))

CLI:
* Add force support to 'qmk git-submodule' ([#19705](https://github.com/qmk/qmk_firmware/pull/19705))
* JSON encoder: improve sorting of layout dict keys ([#19974](https://github.com/qmk/qmk_firmware/pull/19974))
* Increase verbosity of make command ([#20172](https://github.com/qmk/qmk_firmware/pull/20172))
* Append user variables to the end of make command ([#20177](https://github.com/qmk/qmk_firmware/pull/20177))
* Strip API specific output from `qmk info` ([#20234](https://github.com/qmk/qmk_firmware/pull/20234))
* `qmk find`: usability improvements ([#20440](https://github.com/qmk/qmk_firmware/pull/20440))
* `qmk format-json`: Expose full key path and respect `sort_keys` ([#20836](https://github.com/qmk/qmk_firmware/pull/20836))
* Update json2c to use dump_lines ([#21013](https://github.com/qmk/qmk_firmware/pull/21013))

Submodule updates:
* Update ChibiOS to latest stable 21.11.x ([#20470](https://github.com/qmk/qmk_firmware/pull/20470))

Keyboards:
* Allow a larger int for the idle timeout for urbanvanilla keymap ([#19738](https://github.com/qmk/qmk_firmware/pull/19738))
* Change aidansmithdotdev/fine40 to use Encoder Map ([#19912](https://github.com/qmk/qmk_firmware/pull/19912))
* Custom keycodes in JSON ([#19925](https://github.com/qmk/qmk_firmware/pull/19925))
* Remove `"w":1` and `"h":1` from info.json ([#19961](https://github.com/qmk/qmk_firmware/pull/19961))
* Move matrix config to info.json, part 1 ([#19985](https://github.com/qmk/qmk_firmware/pull/19985))
* Move matrix config to info.json, part 2 ([#19987](https://github.com/qmk/qmk_firmware/pull/19987))
* Move matrix config to info.json, part 3 ([#19991](https://github.com/qmk/qmk_firmware/pull/19991))
* Move matrix config to info.json, part 4 ([#20001](https://github.com/qmk/qmk_firmware/pull/20001))
* Move matrix config to info.json, part 5 ([#20003](https://github.com/qmk/qmk_firmware/pull/20003))
* Move matrix config to info.json, part 6 ([#20019](https://github.com/qmk/qmk_firmware/pull/20019))
* Move matrix config to info.json, part 7 ([#20020](https://github.com/qmk/qmk_firmware/pull/20020))
* Move matrix config to info.json, part 8 ([#20030](https://github.com/qmk/qmk_firmware/pull/20030))
* Remove empty rules.mk from keymaps ([#20056](https://github.com/qmk/qmk_firmware/pull/20056))
* Adjust offset for some layouts ([#20075](https://github.com/qmk/qmk_firmware/pull/20075))
* Remove useless "ifdef KEYBOARD_*" ([#20078](https://github.com/qmk/qmk_firmware/pull/20078))
* Remove pointless `USE_I2C` blocks in keyboard headers ([#20084](https://github.com/qmk/qmk_firmware/pull/20084))
* Add support for ISO version of Durgod Hades ([#20110](https://github.com/qmk/qmk_firmware/pull/20110))
* Consolidate Binepad BN009 R1 and R2 into common folder ([#20113](https://github.com/qmk/qmk_firmware/pull/20113))
* Remove more empty headers ([#20155](https://github.com/qmk/qmk_firmware/pull/20155))
* Remove trailing zeroes in info.json layouts ([#20156](https://github.com/qmk/qmk_firmware/pull/20156))
* Clean up usage of `QMK_KEYBOARD_H` ([#20167](https://github.com/qmk/qmk_firmware/pull/20167))
* Move Keychron Q0 and Q0 Plus data-driven configuration; `keychron` keymap `rules.mk` cleanup ([#20168](https://github.com/qmk/qmk_firmware/pull/20168))
* Move ortho & numpad layouts to data driven ([#20183](https://github.com/qmk/qmk_firmware/pull/20183))
* Remove `RGB_DI_PIN` ifdefs ([#20218](https://github.com/qmk/qmk_firmware/pull/20218))
* Add the KJ-Modify RS40 PCB keyboard ([#20243](https://github.com/qmk/qmk_firmware/pull/20243))
* Move `WS2812_DRIVER` to data driven ([#20248](https://github.com/qmk/qmk_firmware/pull/20248))
* [jacky_studio/piggy60] move AVR PCB under rev1 ([#20253](https://github.com/qmk/qmk_firmware/pull/20253))
* Move 75% and 96% layouts to data driven ([#20289](https://github.com/qmk/qmk_firmware/pull/20289))
* Move split layouts to data driven ([#20290](https://github.com/qmk/qmk_firmware/pull/20290))
* Move 66% and 68% layouts to data driven ([#20293](https://github.com/qmk/qmk_firmware/pull/20293))
* add jacky_studio/piggy60/rev2 ([#20297](https://github.com/qmk/qmk_firmware/pull/20297))
* Move 65% layouts to data driven ([#20308](https://github.com/qmk/qmk_firmware/pull/20308))
* Move TKL F13 and FRL layouts to data driven ([#20310](https://github.com/qmk/qmk_firmware/pull/20310))
* Remove some use of keymap.h ([#20316](https://github.com/qmk/qmk_firmware/pull/20316))
* Move fullsize layouts to data driven ([#20317](https://github.com/qmk/qmk_firmware/pull/20317))
* Add 36-key layout for Beekeeb Piantor ([#20328](https://github.com/qmk/qmk_firmware/pull/20328))
* Add sriwedari70 and move kamigakushi to new folder ([#20334](https://github.com/qmk/qmk_firmware/pull/20334))
* Move TKL layouts to data driven ([#20337](https://github.com/qmk/qmk_firmware/pull/20337))
* Move Alice and Ergodox layouts to data driven ([#20340](https://github.com/qmk/qmk_firmware/pull/20340))
* Move small macropad-ish layouts to data driven ([#20341](https://github.com/qmk/qmk_firmware/pull/20341))
* Move `default` layouts to data driven ([#20349](https://github.com/qmk/qmk_firmware/pull/20349))
* Move `RGB_MATRIX_DRIVER` to data driven ([#20350](https://github.com/qmk/qmk_firmware/pull/20350))
* Move split space/backspace layouts to data driven ([#20356](https://github.com/qmk/qmk_firmware/pull/20356))
* Move single `LAYOUT`s to data driven ([#20365](https://github.com/qmk/qmk_firmware/pull/20365))
* Add encoder map for Iris Rev. 5 VIA ([#20412](https://github.com/qmk/qmk_firmware/pull/20412))
* Move remaining `LAYOUT`s to data driven ([#20422](https://github.com/qmk/qmk_firmware/pull/20422))
* Move single `LAYOUT_all`s to data driven ([#20430](https://github.com/qmk/qmk_firmware/pull/20430))
* 4pplet/yakiimo Layout Macro Conversion and Additions ([#20436](https://github.com/qmk/qmk_firmware/pull/20436))
* Move single `60_ansi`, `60_hhkb` and `60_iso` layouts to data driven ([#20438](https://github.com/qmk/qmk_firmware/pull/20438))
* Update brauner preonic layout ([#20439](https://github.com/qmk/qmk_firmware/pull/20439))
* AEBoards Satellite Rev1 Layout Macro Conversion ([#20442](https://github.com/qmk/qmk_firmware/pull/20442))
* Acheron Austin Layout Macro Conversion and Additions ([#20443](https://github.com/qmk/qmk_firmware/pull/20443))
* Move remaining `LAYOUT_all`s to data driven ([#20463](https://github.com/qmk/qmk_firmware/pull/20463))
* Update lotus58 RGB config ([#20468](https://github.com/qmk/qmk_firmware/pull/20468))
* Cleanup `ekow/akira` ([#20474](https://github.com/qmk/qmk_firmware/pull/20474))
* Move 60% layouts to data driven ([#20477](https://github.com/qmk/qmk_firmware/pull/20477))
* Move DZ60 and MJ6XY layouts to data driven ([#20478](https://github.com/qmk/qmk_firmware/pull/20478))
* AEBoards Constellation Layout Macro Updates ([#20487](https://github.com/qmk/qmk_firmware/pull/20487))
* AI03 Equinox Layout Macro Additions ([#20488](https://github.com/qmk/qmk_firmware/pull/20488))
* AI03 Vega Layout Macro Additions ([#20489](https://github.com/qmk/qmk_firmware/pull/20489))
* AKB OGR Layout Macro Additions ([#20490](https://github.com/qmk/qmk_firmware/pull/20490))
* AKB Vero Layout Macro Additions ([#20491](https://github.com/qmk/qmk_firmware/pull/20491))
* Alf DC60 Layout Macro Additions ([#20494](https://github.com/qmk/qmk_firmware/pull/20494))
* Alf X2 Layout Macro Additions ([#20495](https://github.com/qmk/qmk_firmware/pull/20495))
* Koolertron AMAG23 Touch-Up ([#20496](https://github.com/qmk/qmk_firmware/pull/20496))
* BIOI G60 Layout Macro Additions ([#20498](https://github.com/qmk/qmk_firmware/pull/20498))
* BIOI Morgan65 Layout Macro Additions ([#20499](https://github.com/qmk/qmk_firmware/pull/20499))
* BIOI S65 Layout Macro Additions ([#20500](https://github.com/qmk/qmk_firmware/pull/20500))
* Boston Layout Macro Additions ([#20504](https://github.com/qmk/qmk_firmware/pull/20504))
* Potato65S Layout Macro Additions ([#20508](https://github.com/qmk/qmk_firmware/pull/20508))
* Move miscellaneous layouts to data driven ([#20516](https://github.com/qmk/qmk_firmware/pull/20516))
* Cable Car Designs Cypher rev6 Layout Additions and Touch-Up ([#20518](https://github.com/qmk/qmk_firmware/pull/20518))
* Caffeinated Studios Serpent65 Layout Macro Additions ([#20519](https://github.com/qmk/qmk_firmware/pull/20519))
* CannonKeys Adelie Layout Macro Additions ([#20546](https://github.com/qmk/qmk_firmware/pull/20546))
* CannonKeys Aella Layout Macro Additions ([#20547](https://github.com/qmk/qmk_firmware/pull/20547))
* CannonKeys Balance Layout Macro Additions and Touch-Up ([#20548](https://github.com/qmk/qmk_firmware/pull/20548))
* CannonKeys Brutal v2 1800 Layout Macro Additions ([#20549](https://github.com/qmk/qmk_firmware/pull/20549))
* CannonKeys Brutal v2 65 Layout Macro Additions ([#20552](https://github.com/qmk/qmk_firmware/pull/20552))
* CannonKeys Cloudline Layout Macro Additions ([#20553](https://github.com/qmk/qmk_firmware/pull/20553))
* CannonKeys Crin Layout Macro Additions ([#20554](https://github.com/qmk/qmk_firmware/pull/20554))
* CannonKeys DevastatingTKL Layout Macro Additions ([#20555](https://github.com/qmk/qmk_firmware/pull/20555))
* CannonKeys Ellipse Layout Macro Additions ([#20558](https://github.com/qmk/qmk_firmware/pull/20558))
* CannonKeys Ellipse Hotswap Layout Macro Addition & Touch-Up ([#20560](https://github.com/qmk/qmk_firmware/pull/20560))
* CannonKeys Gentoo Layout Macro Additions ([#20561](https://github.com/qmk/qmk_firmware/pull/20561))
* CannonKeys Gentoo Hotswap Touch-Up ([#20562](https://github.com/qmk/qmk_firmware/pull/20562))
* CannonKeys HoodrowG Layout Macro Additions ([#20563](https://github.com/qmk/qmk_firmware/pull/20563))
* CannonKeys Moment Layout Macro Additions ([#20564](https://github.com/qmk/qmk_firmware/pull/20564))
* CannonKeys Moment Hotswap Touch-Up ([#20565](https://github.com/qmk/qmk_firmware/pull/20565))
* CannonKeys Nearfield Layout Macro Addition ([#20566](https://github.com/qmk/qmk_firmware/pull/20566))
* CannonKeys Obliterated75 Layout Macro Additions ([#20567](https://github.com/qmk/qmk_firmware/pull/20567))
* CannonKeys Onyx Layout Macro Additions ([#20568](https://github.com/qmk/qmk_firmware/pull/20568))
* CannonKeys Rekt1800 Layout Macro Additions ([#20569](https://github.com/qmk/qmk_firmware/pull/20569))
* CannonKeys Serenity Layout Macro Additions ([#20570](https://github.com/qmk/qmk_firmware/pull/20570))
* CannonKeys Vector Layout Macro Additions ([#20571](https://github.com/qmk/qmk_firmware/pull/20571))
* Carbo65 Community Layout support ([#20580](https://github.com/qmk/qmk_firmware/pull/20580))
* cest73 TKM Layout Macro Additions ([#20583](https://github.com/qmk/qmk_firmware/pull/20583))
* Charue Charon Layout Macro Additions ([#20585](https://github.com/qmk/qmk_firmware/pull/20585))
* Charue Sunsetter R2 Layout Macro Additions ([#20586](https://github.com/qmk/qmk_firmware/pull/20586))
* Remove `FLIP_HALF` layouts and move to data driven ([#20588](https://github.com/qmk/qmk_firmware/pull/20588))
* update ymdk/id75/rules.mk for develop ([#20592](https://github.com/qmk/qmk_firmware/pull/20592))
* CherryB Studio CB1800 Layout Macro Additions ([#20593](https://github.com/qmk/qmk_firmware/pull/20593))
* CherryB Studio CB65 Layout Macro Additions ([#20594](https://github.com/qmk/qmk_firmware/pull/20594))
* CherryB Studio CB87RGB Layout Macro Additions ([#20595](https://github.com/qmk/qmk_firmware/pull/20595))
* CheckerBoards G_IDB60 Layout Macro Edits ([#20596](https://github.com/qmk/qmk_firmware/pull/20596))
* CherryB Studio CB87v2 Layout Macro Additions ([#20597](https://github.com/qmk/qmk_firmware/pull/20597))
* CX60 Community Layout Support ([#20598](https://github.com/qmk/qmk_firmware/pull/20598))
* Demiurge Layout Macro Touch-Up ([#20599](https://github.com/qmk/qmk_firmware/pull/20599))
* Ducky One 2 SF 1967ST Layout Macro Additions ([#20600](https://github.com/qmk/qmk_firmware/pull/20600))
* Move `FORCE_NKRO` to data driven ([#20604](https://github.com/qmk/qmk_firmware/pull/20604))
* dyz Synthesis60 Layout Macro Addition ([#20610](https://github.com/qmk/qmk_firmware/pull/20610))
* DZTech Bocc Layout Macro Additions ([#20611](https://github.com/qmk/qmk_firmware/pull/20611))
* E88 Layout Macro Additions ([#20612](https://github.com/qmk/qmk_firmware/pull/20612))
* Emery65 Layout Macro Additions ([#20613](https://github.com/qmk/qmk_firmware/pull/20613))
* EvyD13 MX5160 Layout Macro Additions ([#20614](https://github.com/qmk/qmk_firmware/pull/20614))
* FJLabs AD65 Layout Macro Additions ([#20619](https://github.com/qmk/qmk_firmware/pull/20619))
* FJLabs Avalon Layout Additions and Touch-Up ([#20620](https://github.com/qmk/qmk_firmware/pull/20620))
* FJLabs Midway60 Layout Macro Additions ([#20621](https://github.com/qmk/qmk_firmware/pull/20621))
* FJLabs Polaris Layout Additions and Touch-Up ([#20622](https://github.com/qmk/qmk_firmware/pull/20622))
* FJLabs Sinanju WK Layout Additions and Touch-Up ([#20628](https://github.com/qmk/qmk_firmware/pull/20628))
* LFK87 refactor ([#20635](https://github.com/qmk/qmk_firmware/pull/20635))
* Fox Lab Time80 Layout Macro Additions ([#20636](https://github.com/qmk/qmk_firmware/pull/20636))
* FJLabs Solanis Layout Macro Additions ([#20639](https://github.com/qmk/qmk_firmware/pull/20639))
* GrayStudio Aero 75 Refactor and Touch-Up ([#20640](https://github.com/qmk/qmk_firmware/pull/20640))
* Move `USB_MAX_POWER_CONSUMPTION` to data driven ([#20648](https://github.com/qmk/qmk_firmware/pull/20648))
* `info.json` whitespace cleanups ([#20651](https://github.com/qmk/qmk_firmware/pull/20651))
* Hand88 Layout Macro Additions ([#20657](https://github.com/qmk/qmk_firmware/pull/20657))
* Cyberstar Handwired Layout Macro Additions ([#20658](https://github.com/qmk/qmk_firmware/pull/20658))
* split_65 Handwired Layout Macro Addition and Touch-Up ([#20659](https://github.com/qmk/qmk_firmware/pull/20659))
* Bebol Handwired Layout Macro Additions ([#20660](https://github.com/qmk/qmk_firmware/pull/20660))
* Glacier Handwired Layout Macro Addition and Touch-Up ([#20661](https://github.com/qmk/qmk_firmware/pull/20661))
* Koalafications Handwired Layout Macro Additions ([#20662](https://github.com/qmk/qmk_firmware/pull/20662))
* The Galleon Handwired Layout Macro Additions ([#20663](https://github.com/qmk/qmk_firmware/pull/20663))
* More `info.json` whitespace cleanups ([#20665](https://github.com/qmk/qmk_firmware/pull/20665))
* Remove use of layout macros for LFKeyboards LED config ([#20666](https://github.com/qmk/qmk_firmware/pull/20666))
* Helix rev2: remove 4 rows option ([#20667](https://github.com/qmk/qmk_firmware/pull/20667))
* Wakizashi40 Handwired Touch-Up ([#20671](https://github.com/qmk/qmk_firmware/pull/20671))
* yttyx: convert readme to utf-8 encoding ([#20672](https://github.com/qmk/qmk_firmware/pull/20672))
* Alicia Cook Layout Macro Additions ([#20675](https://github.com/qmk/qmk_firmware/pull/20675))
* Primus75 Layout Macro Additions ([#20676](https://github.com/qmk/qmk_firmware/pull/20676))
* Volcano660 Layout Macro Additions ([#20677](https://github.com/qmk/qmk_firmware/pull/20677))
* Iris Keyboards Iris60 Layout Macro Additions ([#20678](https://github.com/qmk/qmk_firmware/pull/20678))
* Irene Layout Macro Additions ([#20679](https://github.com/qmk/qmk_firmware/pull/20679))
* Iron180 Layout Macro Additions ([#20680](https://github.com/qmk/qmk_firmware/pull/20680))
* kinesis/alvicstep: remove kicad project files ([#20681](https://github.com/qmk/qmk_firmware/pull/20681))
* Remove more junk files and scripts ([#20682](https://github.com/qmk/qmk_firmware/pull/20682))
* JKeys Design Gentleman65 Layout Macro Addition and Touch-Up ([#20684](https://github.com/qmk/qmk_firmware/pull/20684))
* JKeys Design Gentleman65 Suited Edition Layout Macro Addition ([#20685](https://github.com/qmk/qmk_firmware/pull/20685))
* add additional layouts to `dactyl_manuform` variants ([#20688](https://github.com/qmk/qmk_firmware/pull/20688))
* TheDogKeyboard Layout Macro Addition ([#20689](https://github.com/qmk/qmk_firmware/pull/20689))
* KBDfans Bella Soldered Layout Macro Additions ([#20691](https://github.com/qmk/qmk_firmware/pull/20691))
* KBDfans Bounce75 Hotswap Touch-Up ([#20692](https://github.com/qmk/qmk_firmware/pull/20692))
* KBDfans KBD66 Layout Additions and Refactor ([#20693](https://github.com/qmk/qmk_firmware/pull/20693))
* KBDfans Odin RGB Touch-Up ([#20694](https://github.com/qmk/qmk_firmware/pull/20694))
* KBDfans Odin Soldered Layout Additions and Touch-Up ([#20695](https://github.com/qmk/qmk_firmware/pull/20695))
* keebzdotnet FMe Layout Additions ([#20696](https://github.com/qmk/qmk_firmware/pull/20696))
* Kegen G-Boy Layout Additions ([#20697](https://github.com/qmk/qmk_firmware/pull/20697))
* Escape Unicode characters in info.json ([#20698](https://github.com/qmk/qmk_firmware/pull/20698))
* Kiko's Lab Ellora65 Layout Additions ([#20699](https://github.com/qmk/qmk_firmware/pull/20699))
* Even more `info.json` whitespace cleanups ([#20703](https://github.com/qmk/qmk_firmware/pull/20703))
* kkatano Bakeneko 65 V3 Layout Additions ([#20706](https://github.com/qmk/qmk_firmware/pull/20706))
* kopibeng MNK65 Layout Additions ([#20708](https://github.com/qmk/qmk_firmware/pull/20708))
* kopibeng Typ65+ Layout Additions ([#20710](https://github.com/qmk/qmk_firmware/pull/20710))
* kopibeng XT60 Layout Additions ([#20711](https://github.com/qmk/qmk_firmware/pull/20711))
* kopibeng XT60_SINGA Layout Additions ([#20712](https://github.com/qmk/qmk_firmware/pull/20712))
* kopibeng XT8x Layout Additions ([#20713](https://github.com/qmk/qmk_firmware/pull/20713))
* Lefty Touch-Up ([#20714](https://github.com/qmk/qmk_firmware/pull/20714))
* Loki65 Layout Additions ([#20715](https://github.com/qmk/qmk_firmware/pull/20715))
* Lucid Alexa Solder Layout Additions ([#20716](https://github.com/qmk/qmk_firmware/pull/20716))
* Lucid Phantom Soldered Layout Additions ([#20717](https://github.com/qmk/qmk_firmware/pull/20717))
* Leftover30 Layout Addition ([#20718](https://github.com/qmk/qmk_firmware/pull/20718))
* Matrix Cain RE Touch-Up ([#20719](https://github.com/qmk/qmk_firmware/pull/20719))
* Matrix Lab 8XV1.2 OG Layout Updates ([#20720](https://github.com/qmk/qmk_firmware/pull/20720))
* Mechlovin Studio Hex6C Layout Additions ([#20722](https://github.com/qmk/qmk_firmware/pull/20722))
* Mechlovin.Studio Rogue87 Rev.1 Layout Additions ([#20724](https://github.com/qmk/qmk_firmware/pull/20724))
* Mechlovin.Studio Rouge87 Rev.1 Layout Additions ([#20725](https://github.com/qmk/qmk_firmware/pull/20725))
* Mechlovin.Studio infinity87 Rev.1 Layout Additions ([#20726](https://github.com/qmk/qmk_firmware/pull/20726))
* Mechlovin.Studio Infinity87 RGB Rev1 Layout Additions ([#20727](https://github.com/qmk/qmk_firmware/pull/20727))
* Mechlovin9 Layout Addition ([#20728](https://github.com/qmk/qmk_firmware/pull/20728))
* 1upkeyboards/pi50 WS2812_DI_PIN patch for develop ([#20731](https://github.com/qmk/qmk_firmware/pull/20731))
* Mechlovin.Studio Infinity87 Rev.2 Layout Additions ([#20735](https://github.com/qmk/qmk_firmware/pull/20735))
* Mechlovin.Studio Olly JF Layout Additions ([#20736](https://github.com/qmk/qmk_firmware/pull/20736))
* Mechlovin Studio Serratus Layout Additions ([#20737](https://github.com/qmk/qmk_firmware/pull/20737))
* MechWild Mercutio Layout Addition ([#20738](https://github.com/qmk/qmk_firmware/pull/20738))
* MisterKnife Knife66 ISO Layout Addition ([#20739](https://github.com/qmk/qmk_firmware/pull/20739))
* MNK1800s Layout Addition ([#20740](https://github.com/qmk/qmk_firmware/pull/20740))
* MNK75 Layout Additions ([#20741](https://github.com/qmk/qmk_firmware/pull/20741))
* Mode SixtyFive S Layout Additions ([#20742](https://github.com/qmk/qmk_firmware/pull/20742))
* Mode SeventyFive H Layout Addition ([#20743](https://github.com/qmk/qmk_firmware/pull/20743))
* Monstargear XO87 Soldered Layout Additions ([#20744](https://github.com/qmk/qmk_firmware/pull/20744))
* MTBKeys MTB60 Solder Layout Additions ([#20745](https://github.com/qmk/qmk_firmware/pull/20745))
* Nix Keyboards Day Off 60 Touch-Up and Layout Additions ([#20746](https://github.com/qmk/qmk_firmware/pull/20746))
* Kastenwagen 1840 Layout Addition ([#20747](https://github.com/qmk/qmk_firmware/pull/20747))
* Kastenwagen 48 Layout Addition ([#20748](https://github.com/qmk/qmk_firmware/pull/20748))
* NovelKeys NK87 Touch-Up ([#20749](https://github.com/qmk/qmk_firmware/pull/20749))
* NovelKeys NK87B Touch-Up ([#20750](https://github.com/qmk/qmk_firmware/pull/20750))
* Noxary 378 Layout Addition ([#20751](https://github.com/qmk/qmk_firmware/pull/20751))
* Noxary Valhalla Layout Addition ([#20752](https://github.com/qmk/qmk_firmware/pull/20752))
* Nightly Boards/DeskDaily Daily60 Layout Additions ([#20753](https://github.com/qmk/qmk_firmware/pull/20753))
* Odelia Touch-Up ([#20754](https://github.com/qmk/qmk_firmware/pull/20754))
* One Key Co Dango40 Touch-Up and Layout Addition ([#20755](https://github.com/qmk/qmk_firmware/pull/20755))
* P3D Glitch Layout Addition ([#20763](https://github.com/qmk/qmk_firmware/pull/20763))
* Pearl Boards Pandora Layout Additions ([#20764](https://github.com/qmk/qmk_firmware/pull/20764))
* Pearl Boards Pearl Layout Addition ([#20765](https://github.com/qmk/qmk_firmware/pull/20765))
* support boards with APM32 instead of the STM32 ([#20770](https://github.com/qmk/qmk_firmware/pull/20770))
* Pearl Boards Zeus Layout Additions ([#20773](https://github.com/qmk/qmk_firmware/pull/20773))
* Peej Rosaline Staggered Layout Additions ([#20774](https://github.com/qmk/qmk_firmware/pull/20774))
* plywrks Lune Layout Touch-Up ([#20775](https://github.com/qmk/qmk_firmware/pull/20775))
* Project Keyboard Signature65 Layout Additions ([#20776](https://github.com/qmk/qmk_firmware/pull/20776))
* protoTypist Allison Layout Additions ([#20777](https://github.com/qmk/qmk_firmware/pull/20777))
* Prototypist J-01 Rev1 Layout Additions ([#20778](https://github.com/qmk/qmk_firmware/pull/20778))
* Protozoa Cassini Layout Additions ([#20779](https://github.com/qmk/qmk_firmware/pull/20779))
* Protozoa P.01 Layout Additions ([#20781](https://github.com/qmk/qmk_firmware/pull/20781))
* QwertleKeys Calice Layout Addition ([#20782](https://github.com/qmk/qmk_firmware/pull/20782))
* Ramlord WITF Layout Touch-Up and Addition ([#20783](https://github.com/qmk/qmk_firmware/pull/20783))
* Rart45: rename LAYOUT_all to LAYOUT ([#20784](https://github.com/qmk/qmk_firmware/pull/20784))
* Rart60 Layout Additions ([#20785](https://github.com/qmk/qmk_firmware/pull/20785))
* Rart67 Layout Additions ([#20786](https://github.com/qmk/qmk_firmware/pull/20786))
* Rart67M: rename LAYOUT_all to LAYOUT ([#20787](https://github.com/qmk/qmk_firmware/pull/20787))
* RART75 Layout Additions ([#20788](https://github.com/qmk/qmk_firmware/pull/20788))
* RART75 Hotswap Layout Additions ([#20789](https://github.com/qmk/qmk_firmware/pull/20789))
* RART75M: rename LAYOUT_all to LAYOUT ([#20790](https://github.com/qmk/qmk_firmware/pull/20790))
* RART80 Hotswap Layout Additions ([#20791](https://github.com/qmk/qmk_firmware/pull/20791))
* Rartand Layout Additions ([#20799](https://github.com/qmk/qmk_firmware/pull/20799))
* Rartlice: rename LAYOUT_all to LAYOUT ([#20800](https://github.com/qmk/qmk_firmware/pull/20800))
* Ratio65 Hotswap: rename LAYOUT_all to LAYOUT_65_ansi_blocker ([#20801](https://github.com/qmk/qmk_firmware/pull/20801))
* Ratio65 Solder Layout Additions ([#20802](https://github.com/qmk/qmk_firmware/pull/20802))
* Specifying the default board file is redundant ([#20807](https://github.com/qmk/qmk_firmware/pull/20807))
* RGBKB Pan Layout Additions ([#20809](https://github.com/qmk/qmk_firmware/pull/20809))
* saevus cor Layout Additions ([#20810](https://github.com/qmk/qmk_firmware/pull/20810))
* Clean up trailing commas from info.json ([#20812](https://github.com/qmk/qmk_firmware/pull/20812))
* Enable LTO on salicylic acid 7skb to reduce size ([#20813](https://github.com/qmk/qmk_firmware/pull/20813))
* Reduce compiled size for mt64rgb's via keymap ([#20814](https://github.com/qmk/qmk_firmware/pull/20814))
* Reduce compiled size for prototypist oceanographer's via keymap ([#20816](https://github.com/qmk/qmk_firmware/pull/20816))
* Sauce Mild Layout Additions ([#20818](https://github.com/qmk/qmk_firmware/pull/20818))
* VCL x SawnsProjects VCL65 Layout Additions ([#20819](https://github.com/qmk/qmk_firmware/pull/20819))
* senselessclay had60 Layout Additions ([#20820](https://github.com/qmk/qmk_firmware/pull/20820))
* Space Holdings Nebula12B ([#20821](https://github.com/qmk/qmk_firmware/pull/20821))
* SmithRune Iron180 Layout Additions ([#20822](https://github.com/qmk/qmk_firmware/pull/20822))
* Stello65 Beta Layout Additions and Clean-Up ([#20824](https://github.com/qmk/qmk_firmware/pull/20824))
* Studio Kestra Nue Layout Additions ([#20825](https://github.com/qmk/qmk_firmware/pull/20825))
* Switchplate Peripherals 910 Layout Additions ([#20827](https://github.com/qmk/qmk_firmware/pull/20827))
* TKC California Layout Addition and Touch-Up ([#20829](https://github.com/qmk/qmk_firmware/pull/20829))
* TKC M0lly Layout Additions ([#20830](https://github.com/qmk/qmk_firmware/pull/20830))
* TKC TKL A/B87 Layout Additions ([#20831](https://github.com/qmk/qmk_firmware/pull/20831))
* Viendi 8L Layout Additions ([#20832](https://github.com/qmk/qmk_firmware/pull/20832))
* Viktus Smolka Layout Additions ([#20833](https://github.com/qmk/qmk_firmware/pull/20833))
* Viktus SP111 Layout Additions ([#20834](https://github.com/qmk/qmk_firmware/pull/20834))
* Viktus SP_Mini Layout Additions ([#20835](https://github.com/qmk/qmk_firmware/pull/20835))
* W1-AT Layout Additions ([#20842](https://github.com/qmk/qmk_firmware/pull/20842))
* Weirdo Geminate60 Layout Additions ([#20843](https://github.com/qmk/qmk_firmware/pull/20843))
* Cypher rev5 Layout Additions ([#20844](https://github.com/qmk/qmk_firmware/pull/20844))
* Prophet Layout Additions ([#20845](https://github.com/qmk/qmk_firmware/pull/20845))
* Tidy up encoder_map directions ([#20847](https://github.com/qmk/qmk_firmware/pull/20847))
* Rama Works Koyu Community Layout Support ([#20848](https://github.com/qmk/qmk_firmware/pull/20848))
* Rama Works M65-B Community Layout Support ([#20850](https://github.com/qmk/qmk_firmware/pull/20850))
* Rama Works M65-BX Community Layout Support ([#20851](https://github.com/qmk/qmk_firmware/pull/20851))
* Rama Works U80-A Community Layout Support ([#20853](https://github.com/qmk/qmk_firmware/pull/20853))
* Wilba Tech WT60-B Community Layout Support ([#20854](https://github.com/qmk/qmk_firmware/pull/20854))
* Wilba Tech WT60-BX Layout Additions and Touch-Up ([#20855](https://github.com/qmk/qmk_firmware/pull/20855))
* Wilba Tech WT60-C Community Layout Support ([#20858](https://github.com/qmk/qmk_firmware/pull/20858))
* Wilba Tech WT60-D Layout Addition and Touch-Up ([#20859](https://github.com/qmk/qmk_firmware/pull/20859))
* Wilba Tech WT60-G Community Layout Support ([#20860](https://github.com/qmk/qmk_firmware/pull/20860))
* Wilba Tech WT60-G2 Community Layout Support ([#20861](https://github.com/qmk/qmk_firmware/pull/20861))
* Wilba Tech WT60-H2: rename LAYOUT_all to LAYOUT_60_ansi_tsangan_split_rshift ([#20864](https://github.com/qmk/qmk_firmware/pull/20864))
* Wilba Tech WT60-XT Layout Additions and Touch-Up ([#20865](https://github.com/qmk/qmk_firmware/pull/20865))
* Wilba Tech WT65-A Community Layout Support and Touch-Up ([#20866](https://github.com/qmk/qmk_firmware/pull/20866))
* Wilba Tech WT65-B Layout Addition and Touch-Up ([#20867](https://github.com/qmk/qmk_firmware/pull/20867))
* Wilba Tech WT65-F Community Layout Support and Touch-Up ([#20869](https://github.com/qmk/qmk_firmware/pull/20869))
* Wilba Tech WT65-FX Community Layout Support ([#20870](https://github.com/qmk/qmk_firmware/pull/20870))
* Wilba Tech WT65-G Layout Additions and Touch-Up ([#20871](https://github.com/qmk/qmk_firmware/pull/20871))
* Wilba Tech WT65-G2 Layout Additions and Touch-Up ([#20872](https://github.com/qmk/qmk_firmware/pull/20872))
* Wilba Tech WT65-XT: rename LAYOUT_all to LAYOUT_65_xt_ansi_blocker_tsangan ([#20873](https://github.com/qmk/qmk_firmware/pull/20873))
* Wilba Tech WT65-XTX Layout Additions and Touch-Up ([#20874](https://github.com/qmk/qmk_firmware/pull/20874))
* Wilba Tech WT69-A Layout Addition and Touch-Up ([#20875](https://github.com/qmk/qmk_firmware/pull/20875))
* Wilba Tech WT70-JB Layout Addition and Touch-Up ([#20876](https://github.com/qmk/qmk_firmware/pull/20876))
* Wilba Tech WT75-A Layout Additions and Touch-Up ([#20877](https://github.com/qmk/qmk_firmware/pull/20877))
* Wilba Tech WT75-B Layout Additions and Touch-Up ([#20878](https://github.com/qmk/qmk_firmware/pull/20878))
* Wilba Tech WT75-C Layout Additions and Touch-Up ([#20879](https://github.com/qmk/qmk_firmware/pull/20879))
* Wilba Tech WT80-G Layout Additions and Touch-Up ([#20880](https://github.com/qmk/qmk_firmware/pull/20880))
* WinKeys Mini Winni: rename LAYOUT_all to LAYOUT_ortho_2x4 ([#20881](https://github.com/qmk/qmk_firmware/pull/20881))
* Scarlet Bandana Layout Additions ([#20882](https://github.com/qmk/qmk_firmware/pull/20882))
* Winkeyless B87 Community Layout Support ([#20884](https://github.com/qmk/qmk_firmware/pull/20884))
* Xelus AkiS Layout Additions ([#20885](https://github.com/qmk/qmk_firmware/pull/20885))
* Xelus Dharma Layout Additions ([#20886](https://github.com/qmk/qmk_firmware/pull/20886))
* Xelus Kangaroo Layout Additions ([#20887](https://github.com/qmk/qmk_firmware/pull/20887))
* Xelus La+ Layout Addition ([#20888](https://github.com/qmk/qmk_firmware/pull/20888))
* Xelus Pachi Mini 32U4 Community Layout Support ([#20889](https://github.com/qmk/qmk_firmware/pull/20889))
* Xelus Pachi rev1 Community Layout Support ([#20891](https://github.com/qmk/qmk_firmware/pull/20891))
* Xelus Trinity XT TKL Layout Additions ([#20892](https://github.com/qmk/qmk_firmware/pull/20892))
* Xelus Valor FRL TKL Layout Additions ([#20893](https://github.com/qmk/qmk_firmware/pull/20893))
* YDKB Chili Community Layout Support ([#20895](https://github.com/qmk/qmk_firmware/pull/20895))
* YDKB Grape Layout Additions ([#20899](https://github.com/qmk/qmk_firmware/pull/20899))
* YMDK Wings Layout Addition ([#20900](https://github.com/qmk/qmk_firmware/pull/20900))
* YMDK Wings Hotswap: rename LAYOUT_all to LAYOUT ([#20901](https://github.com/qmk/qmk_firmware/pull/20901))
* YMDK YM68 Community Layout Support ([#20906](https://github.com/qmk/qmk_firmware/pull/20906))
* Yugo-M Controller Layout Additions ([#20907](https://github.com/qmk/qmk_firmware/pull/20907))
* Zicodia TKLFRLNRLMLAO Layout Addition ([#20908](https://github.com/qmk/qmk_firmware/pull/20908))
* ZTBoards After Layout Addition ([#20912](https://github.com/qmk/qmk_firmware/pull/20912))
* ZTBoards Noon Layout Addition ([#20913](https://github.com/qmk/qmk_firmware/pull/20913))
* SawnsProjects Amber80 Solder Community Layout Support ([#20917](https://github.com/qmk/qmk_firmware/pull/20917))
* Pearl Boards Atlas Layout Additions ([#20918](https://github.com/qmk/qmk_firmware/pull/20918))
* Xiudi XD004: rename LAYOUT_all to LAYOUT_ortho_1x4 ([#20919](https://github.com/qmk/qmk_firmware/pull/20919))
* Wilba Tech WT80-BC Community Layout Support ([#20920](https://github.com/qmk/qmk_firmware/pull/20920))
* 4pplet Eagle Viper REP Rev B Community Layout Support ([#20921](https://github.com/qmk/qmk_firmware/pull/20921))
* FR4Boards unix60 Layout Additions ([#20926](https://github.com/qmk/qmk_firmware/pull/20926))
* MC-76K: rename LAYOUT_all to LAYOUT ([#20927](https://github.com/qmk/qmk_firmware/pull/20927))
* Mechlovin Studio Jay60 Community Layout Support ([#20928](https://github.com/qmk/qmk_firmware/pull/20928))
* MisterKnife Knife66 Layout Additions ([#20929](https://github.com/qmk/qmk_firmware/pull/20929))
* MisterKnife Knife66 ISO Layout Additions II ([#20930](https://github.com/qmk/qmk_firmware/pull/20930))
* 4pplet Waffling80 Community Layout Support and Touch-Up ([#20932](https://github.com/qmk/qmk_firmware/pull/20932))
* Acheron Elongate Delta: rename LAYOUT_all to LAYOUT ([#20956](https://github.com/qmk/qmk_firmware/pull/20956))
* ADPenrose Akemipad Layout Addition ([#20957](https://github.com/qmk/qmk_firmware/pull/20957))
* ADPenrose Shisaku: rename LAYOUT_all to LAYOUT ([#20958](https://github.com/qmk/qmk_firmware/pull/20958))
* AEBoards Aegis Layout Additions ([#20960](https://github.com/qmk/qmk_firmware/pull/20960))
* rart/rart80:via: restore rules.mk after #20334 ([#21002](https://github.com/qmk/qmk_firmware/pull/21002))
* Remove HHKB RN42 code ([#21007](https://github.com/qmk/qmk_firmware/pull/21007))
* Move `thekey` to Drop vendor folder ([#21032](https://github.com/qmk/qmk_firmware/pull/21032))

Keyboard fixes:
* userspace/community layout fixes ([#19998](https://github.com/qmk/qmk_firmware/pull/19998))
* Fix layout macro keys with no matrix position ([#20033](https://github.com/qmk/qmk_firmware/pull/20033))
* Restore matrix pins for ep/40 ([#20083](https://github.com/qmk/qmk_firmware/pull/20083))
* kbdfans/tiger80: remove duplicate keys in info.json ([#20148](https://github.com/qmk/qmk_firmware/pull/20148))
* Fixup z70ultra — replace mis-removed file ([#20157](https://github.com/qmk/qmk_firmware/pull/20157))
* Fixup CI build for F103C6 onekey. ([#20188](https://github.com/qmk/qmk_firmware/pull/20188))
* Fix layouts containing keys with multiple matrix positions ([#20191](https://github.com/qmk/qmk_firmware/pull/20191))
* Fix some more missing `#pragma once`s ([#20241](https://github.com/qmk/qmk_firmware/pull/20241))
* Fixup CI build for `nack`. ([#20292](https://github.com/qmk/qmk_firmware/pull/20292))
* Fixup Pointing device functions ([#20311](https://github.com/qmk/qmk_firmware/pull/20311))
* Fix a handful of CLI errors ([#20321](https://github.com/qmk/qmk_firmware/pull/20321))
* Fix API errors ([#20326](https://github.com/qmk/qmk_firmware/pull/20326))
* Set up DEFAULT_FOLDER for primekb/meridian ([#20367](https://github.com/qmk/qmk_firmware/pull/20367))
* Fix up via keymap builds. ([#20383](https://github.com/qmk/qmk_firmware/pull/20383))
* Fix up via keymap builds. ([#20397](https://github.com/qmk/qmk_firmware/pull/20397))
* Fix some missing QMK_KEYBOARD_H includes in user keymaps ([#20417](https://github.com/qmk/qmk_firmware/pull/20417))
* Update ymdk/id75 config ([#20432](https://github.com/qmk/qmk_firmware/pull/20432))
* Fix info.json LTO and format encoder definitions ([#20456](https://github.com/qmk/qmk_firmware/pull/20456))
* Fixup dymium65 RGB Pin on develop ([#20473](https://github.com/qmk/qmk_firmware/pull/20473))
* Fixup missing include in mxss `via` keymap ([#20475](https://github.com/qmk/qmk_firmware/pull/20475))
* Fix nk plus ws2812 config ([#20524](https://github.com/qmk/qmk_firmware/pull/20524))
* cannonkeys/ellipse_hs: correct layout macro references ([#20577](https://github.com/qmk/qmk_firmware/pull/20577))
* Remove use of layout macros for `music_map` ([#20634](https://github.com/qmk/qmk_firmware/pull/20634))
* Vertex/angle65 WS2812 pin fix ([#20653](https://github.com/qmk/qmk_firmware/pull/20653))
* Fix ws2812 pin for phantagom boards ([#20670](https://github.com/qmk/qmk_firmware/pull/20670))
* Fixup 1upkeyboards/pi50 ([#20733](https://github.com/qmk/qmk_firmware/pull/20733))
* Fix `test_json2c_no_json()` ([#20756](https://github.com/qmk/qmk_firmware/pull/20756))
* Fix mxss rgblight.c compilation issues ([#20804](https://github.com/qmk/qmk_firmware/pull/20804))
* Fixup paladin64 ([#20805](https://github.com/qmk/qmk_firmware/pull/20805))
* Fixup dogtag ([#20808](https://github.com/qmk/qmk_firmware/pull/20808))
* Fixup zwag75 ([#20923](https://github.com/qmk/qmk_firmware/pull/20923))
* Fixup latinpadble ([#20924](https://github.com/qmk/qmk_firmware/pull/20924))
* Add missing layout data for a handful of boards ([#20931](https://github.com/qmk/qmk_firmware/pull/20931))
* Fixup evo70 ([#20949](https://github.com/qmk/qmk_firmware/pull/20949))
* Fixup Crkbd default keymap ([#20962](https://github.com/qmk/qmk_firmware/pull/20962))
* Fix key display on Corne OLED ([#21044](https://github.com/qmk/qmk_firmware/pull/21044))

Others:
* Add layer-cycle example ([#19069](https://github.com/qmk/qmk_firmware/pull/19069))
* Remove remnants of Vagrant. ([#20000](https://github.com/qmk/qmk_firmware/pull/20000))
* Develop cleanup IS31FL3736 docs ([#20633](https://github.com/qmk/qmk_firmware/pull/20633))
* Organise config/rules <-> info mappings ([#20723](https://github.com/qmk/qmk_firmware/pull/20723))
* Add a change log for PR20584 ([#20998](https://github.com/qmk/qmk_firmware/pull/20998))

Bugs:
* Strip whitespace from CONVERT_TO variables ([#19948](https://github.com/qmk/qmk_firmware/pull/19948))
* Check all rows have the correct number of columns when parsing `g_led_config` ([#19954](https://github.com/qmk/qmk_firmware/pull/19954))
* Fix OSMs getting stuck ([#20034](https://github.com/qmk/qmk_firmware/pull/20034))
* Fix rgblight layers when animations aren't enabled ([#20097](https://github.com/qmk/qmk_firmware/pull/20097))
* Fixed split keyboard issue where custom LED indicators could activate incorrect LEDs (#20203) ([#20204](https://github.com/qmk/qmk_firmware/pull/20204))
* Reduce _validate complexity ([#20274](https://github.com/qmk/qmk_firmware/pull/20274))
* `qmk info`: account for ISO enter when calculating layout X offset ([#20325](https://github.com/qmk/qmk_firmware/pull/20325))
* Disable specific warnings to mitigate compilation problems with `KEEP_INTERMEDIATES=yes`. ([#20339](https://github.com/qmk/qmk_firmware/pull/20339))
* Fix compilation issue with Swap Hands and Encoder Map ([#20348](https://github.com/qmk/qmk_firmware/pull/20348))
* Fix preprocessor condition for SPLIT_HAPTIC_ENABLE ([#20411](https://github.com/qmk/qmk_firmware/pull/20411))
* Fix compilation issues with PS/2 driver on F4x1 controllers ([#20433](https://github.com/qmk/qmk_firmware/pull/20433))
* Fix capital letters not getting sent with sendstring_swiss_fr.h ([#20515](https://github.com/qmk/qmk_firmware/pull/20515))
* Duplicate board files for blok converter ([#20629](https://github.com/qmk/qmk_firmware/pull/20629))
* Fix Mod-Tap combo regression ([#20669](https://github.com/qmk/qmk_firmware/pull/20669))
* Revert use of legacy wear leveling driver now ChibiOS is fixed ([#20806](https://github.com/qmk/qmk_firmware/pull/20806))
* Fix compilation error introduced by #20669 ([#20849](https://github.com/qmk/qmk_firmware/pull/20849))
* Fix English word list retrieval in qmk generate-autocorrect-data ([#20915](https://github.com/qmk/qmk_firmware/pull/20915))
* Improve keymap folder resolution ([#20981](https://github.com/qmk/qmk_firmware/pull/20981))
* Fix issue with Repeat Key-Combo test ([#21005](https://github.com/qmk/qmk_firmware/pull/21005))
* `qmk info` - Remove printing of "Keyboard Folder" ([#21033](https://github.com/qmk/qmk_firmware/pull/21033))
