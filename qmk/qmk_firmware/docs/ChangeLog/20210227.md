# QMK Breaking Changes - 2021 February 27 Changelog

## Changes Requiring User Action

The following keyboards have had their source moved within QMK:

Old Keyboard Name | New Keyboard Name
:---------------- | :----------------
bear_65 | jacky_studio/bear_65
s7_elephant/rev1 | jacky_studio/s7_elephant/rev1
s7_elephant/rev2 | jacky_studio/s7_elephant/rev2
aplx6 | aplyard/aplx6/rev1
southpaw75 | fr4/southpaw75

The [Aplyard Aplx6 rev2](https://github.com/qmk/qmk_firmware/tree/0.12.0/keyboards/aplyard/aplx6/rev1) and the [FR4Boards Unix60](https://github.com/qmk/qmk_firmware/tree/0.12.0/keyboards/fr4/unix60) have also been added as part of these changes.

Additionally, the `handwired/bluepill/bluepill70` keyboard has been removed.

## Core Changes

### ChibiOS Update and Config Migration

QMK's ChibiOS and ChibiOS-Contrib submodules have been updated to version 20.3.2.

Along with this, QMK now provides default configuration files for all commonly-supported ARM microcontrollers running on ChibiOS. As such, keyboards are now only required to define settings which differ from the defaults, thereby reducing the size of pull requests for keyboards running atop ChibiOS.

### QMK Infrastructure and Internals

Python is now required to build QMK. The minimum Python version has been increased to 3.7.

The power of `info.json` has been massively expanded. Most keyboard parameters can now be expressed in `info.json` instead of `config.h`/`rules.mk`. This should make maintaining keyboards easier, and will enable tooling that can allow non-technical users to add and maintain QMK keyboards without writing any code.

To ease migration a new command has been provided, `qmk generate-info-json -kb <keyboard>`. You can use this command to generate a complete `info.json` file for a keyboard and then remove the duplicate information from `config.h` and `rules.mk`. 

Detailed example showing how to generate a new info.json and identify duplicate keys:

```
user@hostname:~/qmk_firmware/keyboards/lets_split:0$ qmk generate-info-json > new-info.json
user@hostname:~/qmk_firmware/keyboards/lets_split:0$ mv new-info.json info.json
user@hostname:~/qmk_firmware/keyboards/lets_split:0$ qmk info
⚠ lets_split/rev2: DEBOUNCE in config.h is overwriting debounce in info.json
⚠ lets_split/rev2: DEVICE_VER in config.h is overwriting usb.device_ver in info.json
⚠ lets_split/rev2: DIODE_DIRECTION in config.h is overwriting diode_direction in info.json
⚠ lets_split/rev2: MANUFACTURER in config.h is overwriting manufacturer in info.json
⚠ lets_split/rev2: RGB_DI_PIN in config.h is overwriting rgblight.pin in info.json
⚠ lets_split/rev2: RGBLED_NUM in config.h is overwriting rgblight.led_count in info.json
⚠ lets_split/rev2: PRODUCT_ID in config.h is overwriting usb.pid in info.json
⚠ lets_split/rev2: VENDOR_ID in config.h is overwriting usb.vid in info.json
⚠ lets_split/rev2: Matrix pins are specified in both info.json and config.h, the config.h values win.
⚠ lets_split/rev2: LAYOUTS in rules.mk is overwriting community_layouts in info.json
⚠ lets_split/rev2: Feature bootmagic is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature mousekey is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature extrakey is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature console is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature command is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature nkro is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature backlight is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature midi is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature audio is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature unicode is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature bluetooth is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature rgblight is specified in both info.json and rules.mk, the rules.mk value wins.
⚠ lets_split/rev2: Feature sleep_led is specified in both info.json and rules.mk, the rules.mk value wins.
Keyboard Name: Let's Split
Manufacturer: Wootpatoot
Website:
Maintainer: QMK Community
Keyboard Folder: lets_split/rev2
Layouts: LAYOUT, LAYOUT_ortho_4x12
Size: 13 x 4
Processor: atmega32u4
Bootloader: caterina
```

## Detailed Change List

### Changes Requiring User Action

* Refactor Jacky's boards (Bear65 and S7 Elephant) ([#10528](https://github.com/qmk/qmk_firmware/pull/10528), [#11981](https://github.com/qmk/qmk_firmware/pull/11981))
* Remove handwired/bluepill ([#11415](https://github.com/qmk/qmk_firmware/pull/11415))
* Aplyard Aplx6 Added rev2 & move rev1+rev2 to parent folder ([#10973](https://github.com/qmk/qmk_firmware/pull/10973))
* added `unix60`, moved together with `southpaw75` into `fr4` folder ([#11195](https://github.com/qmk/qmk_firmware/pull/11195))

### Fixes

* GCC 10 can now compile Drop Alt firmware ([#9485](https://github.com/qmk/qmk_firmware/pull/9485))
* Fix compiling on `develop` branch ([#11409](https://github.com/qmk/qmk_firmware/pull/11409))
* Fix broken keyboards and keymaps ([#11412](https://github.com/qmk/qmk_firmware/pull/11412), [#11427](https://github.com/qmk/qmk_firmware/pull/11427), [#11448](https://github.com/qmk/qmk_firmware/pull/11448), [#11447](https://github.com/qmk/qmk_firmware/pull/11447), [#11473](https://github.com/qmk/qmk_firmware/pull/11473), [#11584](https://github.com/qmk/qmk_firmware/pull/11584), [#11600](https://github.com/qmk/qmk_firmware/pull/11600))
* Fixed up build dependencies so that generated files are made available before compiling any object files ([#11435](https://github.com/qmk/qmk_firmware/pull/11435))
* Formatting fixes ([`378edd9`](https://github.com/qmk/qmk_firmware/commit/378edd9491f2ab0d3d8a970c9a8e64bc03ca15cf), [#11594](https://github.com/qmk/qmk_firmware/pull/11594), [`27749e1`](https://github.com/qmk/qmk_firmware/commit/27749e1c967c02c05e62a89a0ae2776dd7e5158c))
* Include `stdbool.h` in `uart.h` to fix compiler errors ([#11728](https://github.com/qmk/qmk_firmware/pull/11728))
* Decouple USB events from the USB interrupt handler in ChibiOS ([#10437](https://github.com/qmk/qmk_firmware/pull/10437))
  * Fixes an issue while using Backlight and External EEPROM at the same time that would cause the MCU to lock up.
* Address wake from sleep instability ([#11450](https://github.com/qmk/qmk_firmware/pull/11450))
* Fix pressing media key on a momentarily activated layer may lead to missing key up events ([#11162](https://github.com/qmk/qmk_firmware/pull/11162))
* Fix an RGB initialisation bug on Massdrop keyboards ([#12022](https://github.com/qmk/qmk_firmware/pull/12022))
* Fix file encoding errors on Windows, and layouts not correctly merging into info.json ([#12039](https://github.com/qmk/qmk_firmware/pull/12039))

### Additions and Enhancements

* Allow configuration of serial USART timeout ([#11057](https://github.com/qmk/qmk_firmware/pull/11057))
* Added Sync Timer feature for Split Common keyboards ([#10997](https://github.com/qmk/qmk_firmware/pull/10997))
* Add modifier state to the Split Common transport ([#10400](https://github.com/qmk/qmk_firmware/pull/10400))
* Add Pix keyboard by sendz (`sendyyeah/pix`) ([#11154](https://github.com/qmk/qmk_firmware/pull/11154))
* Implement option for kinetic mouse movement algorithm for mouse keys ([#6739](https://github.com/qmk/qmk_firmware/pull/6739))
* Improved Language Specific Keycodes for US International and Extended Layouts ([#11307](https://github.com/qmk/qmk_firmware/pull/11307))
* Modified `QWIIC_ENABLE` in `rules.mk` to be yes/no choice, adding `QWIIC_DRIVERS` to allow for inclusion of specific drivers ([#11426](https://github.com/qmk/qmk_firmware/pull/11426))
* Allow AVR-based keyboards to override the `bootloader_jump` function ([#11418](https://github.com/qmk/qmk_firmware/pull/11418))
* Refine RGBLight Twinkle effect to be smoother (use breathing curve) ([#11350](https://github.com/qmk/qmk_firmware/pull/11350))
* Keep track of last matrix activity ([#10730](https://github.com/qmk/qmk_firmware/pull/10730), [`ab375d3`](https://github.com/qmk/qmk_firmware/commit/ab375d3d075c105f09a1ddd0e155f178225518bc), [#11552](https://github.com/qmk/qmk_firmware/pull/11552))
* fix `matrix_io_delay()` timing in `quantum/matrix.c` ([#9603](https://github.com/qmk/qmk_firmware/pull/9603))
* Keep track of encoder activity ([#11595](https://github.com/qmk/qmk_firmware/pull/11595))
* Backport ChibiOS Audio changes from ZSA ([#11687](https://github.com/qmk/qmk_firmware/pull/11687))
* Add support for 8 buttons to mouse report ([#10807](https://github.com/qmk/qmk_firmware/pull/10807))
* Allow `post_config.h` to be implemented in userspace ([#11519](https://github.com/qmk/qmk_firmware/pull/11519))
* Adds AT90USB162 support ([#11570](https://github.com/qmk/qmk_firmware/pull/11570))
* Stop sounds when suspended ([#11553](https://github.com/qmk/qmk_firmware/pull/11553))
* Revamp spidey3 userspace and keymaps ([#11768](https://github.com/qmk/qmk_firmware/pull/11768))
* Add support for analog USBPD on STM32G4xx ([#11824](https://github.com/qmk/qmk_firmware/pull/11824))
* Master matrix can now be transported to the slave side in Split Common keyboards ([#11046](https://github.com/qmk/qmk_firmware/pull/11046))
* RGBLight: Allow configurable default settings ([#11912](https://github.com/qmk/qmk_firmware/pull/11912))
* Add `tap_code_delay(code, delay)` ([#11913](https://github.com/qmk/qmk_firmware/pull/11913), [#11938](https://github.com/qmk/qmk_firmware/pull/11938))

### Clean-ups and Optimizations

* Fix duplicate `I2C_KEYMAP_START` define ([#11237](https://github.com/qmk/qmk_firmware/pull/11237))
* Rewrite APA102 support for RGBLight ([#10894](https://github.com/qmk/qmk_firmware/pull/10894))
* Update ADB Protocol implementation in TMK Core ([#11168](https://github.com/qmk/qmk_firmware/pull/11168))
* Remove unused `action_get_macro()` usages in user files ([#11165](https://github.com/qmk/qmk_firmware/pull/11165))
* Remove `QMK_KEYBOARD_CONFIG_H` ([#11576](https://github.com/qmk/qmk_firmware/pull/11576))
* Remove duplicated housekeeping in `arm_atsam` ([#11672](https://github.com/qmk/qmk_firmware/pull/11672))
* UART driver refactor ([#11637](https://github.com/qmk/qmk_firmware/pull/11637))
* Move `transport.c` to `QUANTUM_LIB_SRC` ([#11751](https://github.com/qmk/qmk_firmware/pull/11751))
* Remove `MIDI_ENABLE_STRICT` from user keymaps ([#11750](https://github.com/qmk/qmk_firmware/pull/11750))
* Remove legacy print backward compatiblitly ([#11805](https://github.com/qmk/qmk_firmware/pull/11805))
* Migrate mousekey to quantum ([#11804](https://github.com/qmk/qmk_firmware/pull/11804))
* remove deprecated `qmk json-keymap` ([#11823](https://github.com/qmk/qmk_firmware/pull/11823))
* Remove FAUXCLICKY feature (deprecated) ([#11829](https://github.com/qmk/qmk_firmware/pull/11829))
* Refactor platform logic within `print.h` ([#11863](https://github.com/qmk/qmk_firmware/pull/11863))
* Audio system overhaul ([#11820](https://github.com/qmk/qmk_firmware/pull/11820))
* Output selection: Remove "USB and BT" option for Bluetooth ([#11940](https://github.com/qmk/qmk_firmware/pull/11940))
* `tmk_core/common/action.c`: refactor for code size; merge multiple `case`s into one ([#11943](https://github.com/qmk/qmk_firmware/pull/11943))
* Remove rules and settings from user keymaps that are already defined at keyboard level ([#11966](https://github.com/qmk/qmk_firmware/pull/11966))

### QMK Infrastructure and Internals

* bump to python 3.7 ([#11408](https://github.com/qmk/qmk_firmware/pull/11408))
* `develop` branch is now formatted as part of CI tasks ([#11893](https://github.com/qmk/qmk_firmware/pull/11893), [#11905](https://github.com/qmk/qmk_firmware/pull/11905), [#11907](https://github.com/qmk/qmk_firmware/pull/11907), [#11928](https://github.com/qmk/qmk_firmware/pull/11928), [#11936](https://github.com/qmk/qmk_firmware/pull/11936))
* Configure keyboard matrix from info.json ([#10817](https://github.com/qmk/qmk_firmware/pull/10817))
* Validate our JSON data using json_schema ([#11101](https://github.com/qmk/qmk_firmware/pull/11101))
* Use the schema to eliminate custom code ([#11108](https://github.com/qmk/qmk_firmware/pull/11108))
* Add support for specifying BOARD in `info.json` ([#11492](https://github.com/qmk/qmk_firmware/pull/11492))
* Document how to add data driven configurations ([#11502](https://github.com/qmk/qmk_firmware/pull/11502))
* Process info.json rules ahead of userspace rules ([#11542](https://github.com/qmk/qmk_firmware/pull/11542))
* Remove duplicate manufacturer definitions ([#11544](https://github.com/qmk/qmk_firmware/pull/11544))
* Update list of MCUs in `keyboard.jsonschema` to mirror `qmk.constants.py` ([#11688](https://github.com/qmk/qmk_firmware/pull/11688))
* Create a system to map between `info.json` and `config.h`/`rules.mk` ([#11548](https://github.com/qmk/qmk_firmware/pull/11548))
* Make LAYOUT parsing more robust ([#12000](https://github.com/qmk/qmk_firmware/pull/12000))


### ChibiOS Update and Config Migration

* Add board specific to Proton-C, with usual defaults turned on to match Pro-Micro ([#10976](https://github.com/qmk/qmk_firmware/pull/10976))
* Disable almost all ChibiOS subsystems in default configs ([#11111](https://github.com/qmk/qmk_firmware/pull/11111))
* Config Migrations ([#10418](https://github.com/qmk/qmk_firmware/pull/10418), [#11123](https://github.com/qmk/qmk_firmware/pull/11123), [#11261](https://github.com/qmk/qmk_firmware/pull/11261), [#11413](https://github.com/qmk/qmk_firmware/pull/11413), [#11414](https://github.com/qmk/qmk_firmware/pull/11414), [#11495](https://github.com/qmk/qmk_firmware/pull/11495), [#11504](https://github.com/qmk/qmk_firmware/pull/11504), [#11529](https://github.com/qmk/qmk_firmware/pull/11529), [#11588](https://github.com/qmk/qmk_firmware/pull/11588), [#11598](https://github.com/qmk/qmk_firmware/pull/11598), [#11607](https://github.com/qmk/qmk_firmware/pull/11607), [#11617](https://github.com/qmk/qmk_firmware/pull/11617), [#11620](https://github.com/qmk/qmk_firmware/pull/11620), [#11630](https://github.com/qmk/qmk_firmware/pull/11630), [#11646](https://github.com/qmk/qmk_firmware/pull/11646), [#11689](https://github.com/qmk/qmk_firmware/pull/11689), [#11846](https://github.com/qmk/qmk_firmware/pull/11846), [#11927](https://github.com/qmk/qmk_firmware/pull/11927), [#12001](https://github.com/qmk/qmk_firmware/pull/12001))
* Disable subsystems repo-wide ([#11449](https://github.com/qmk/qmk_firmware/pull/11449))
* Leftover early initialisation conversions ([#11615](https://github.com/qmk/qmk_firmware/pull/11615))
* Fix up comments showing how to execute config migration ([#11621](https://github.com/qmk/qmk_firmware/pull/11621))
* Add STM32G431 and STM32G474 board definitions ([#11793](https://github.com/qmk/qmk_firmware/pull/11793))
