# QMK Breaking Changes - 2021 May 29 Changelog

## Notable Changes {#notable-changes}

### RGB Matrix support for split common ([#11055](https://github.com/qmk/qmk_firmware/pull/11055)) {#rgb-matrix-split-common}

Split boards can now use RGB Matrix without defining a custom matrix.

### Teensy 3.6 support ([#12258](https://github.com/qmk/qmk_firmware/pull/12258)) {#teensy-3-6-support}

Added support for MK66F18 (Teensy 3.6) microcontroller.

### New command: qmk console ([#12828](https://github.com/qmk/qmk_firmware/pull/12828)) {#new-command-qmk-console}

A new `qmk console` command has been added for attaching to your keyboard's console. It operates similiarly to QMK Toolbox by allowing you to connect to one or more keyboard consoles to display debugging messages.

### Improved command: qmk config {#improve-command-qmk-config}

We've updated the `qmk config` command to show only the configuration items you have actually set. You can now display (almost) all of the available configuration options, along with their default values, using `qmk config -a`.

### LED Matrix Improvements ([#12509](https://github.com/qmk/qmk_firmware/pull/12509), [#12580](https://github.com/qmk/qmk_firmware/pull/12580), [#12588](https://github.com/qmk/qmk_firmware/pull/12588), [#12633](https://github.com/qmk/qmk_firmware/pull/12633), [#12651](https://github.com/qmk/qmk_firmware/pull/12651), [#12685](https://github.com/qmk/qmk_firmware/pull/12685)) {#led-matrix-improvements}

LED Matrix has been improved with effects, CIE1931 curves, and a task system.

## Changes Requiring User Action {#changes-requiring-user-action}

### Updated Keyboard Codebases {#updated-keyboard-codebases}

* Durgod keyboard refactor in preparation for adding additional durgod keyboards ([#11978](https://github.com/qmk/qmk_firmware/pull/11978))
* Updated Function96 with V2 files and removed chconf.h and halconf.h ([#12613](https://github.com/qmk/qmk_firmware/pull/12613))
* [Keyboard] updated a vendor name / fixed minor keymap issues ([#12881](https://github.com/qmk/qmk_firmware/pull/12881))
* [Keyboard] Corne - Remove legacy revision support ([#12226](https://github.com/qmk/qmk_firmware/pull/12226))

The following keyboards have had their source moved within QMK:

Old Keyboard Name | New Keyboard Name
:---------------- | :----------------
crkbd/rev1/common | crkbd/rev1
function96 | function96/v1
nckiibs/flatbread60 | delikeeb/flatbread60
nckiibs/vaguettelite | delikeeb/vaguettelite
nckiibs/vanana/rev1 | delikeeb/vanana/rev1
nckiibs/vanana/rev2 | delikeeb/vanana/rev2
nckiibs/vaneela | delikeeb/vaneela
nckiibs/vaneelaex | delikeeb/vaneelaex
nckiibs/waaffle/rev3/elite_c | delikeeb/waaffle/rev3/elite_c
nckiibs/waaffle/rev3/pro_micro | delikeeb/waaffle/rev3/pro_micro

The [Function96 V2](https://github.com/qmk/qmk_firmware/tree/0.13.0/keyboards/function96/v2) has also been added as part of these changes.

The codebase for the [Durgod K320](https://github.com/qmk/qmk_firmware/tree/0.13.0/keyboards/durgod/k320) has been reworked in anticipation of additional Durgod keyboards gaining QMK support.

Additionally, the `crkbd/rev1/legacy` keyboard has been removed.

### Bootmagic Deprecation and Refactor ([#12172](https://github.com/qmk/qmk_firmware/pull/12172)) {#bootmagic-deprecation-and-refactor}

QMK has decided to deprecate the full Bootmagic feature and leave Bootmagic Lite as the only remaining option.

This pull request changes the behavior of `BOOTMAGIC_ENABLE` such that specifying `BOOTMAGIC_ENABLE = yes` enables Bootmagic Lite instead of full Bootmagic.

If attempts to use Bootmagic functionality result in unexpected behavior, check your `rules.mk` file and change the `BOOTMAGIC_ENABLE` setting to specify either `lite` or `full`.

#### Tentative Deprecation Schedule

This is the current planned roadmap for the behavior of `BOOTMAGIC_ENABLE`:

- From 2021 May 29, setting `BOOTMAGIC_ENABLE = yes` will enable Bootmagic Lite instead of full Bootmagic.
- From 2021 Aug 28, `BOOTMAGIC_ENABLE` must be either `yes`, `lite`, or `no` – setting `BOOTMAGIC_ENABLE = full` will cause compilation to fail.
- From 2021 Nov 27, `BOOTMAGIC_ENABLE` must be either `yes` or `no` – setting `BOOTMAGIC_ENABLE = lite` will cause compilation to fail.

### Removal of LAYOUT_kc ([#12160](https://github.com/qmk/qmk_firmware/pull/12160)) {#removal-of-layout-kc}

We've removed support for `LAYOUT_kc` macros, if your keymap uses one you will need to update it use a regular `LAYOUT` macro.

### Encoder callbacks are now boolean ([#12805](https://github.com/qmk/qmk_firmware/pull/12805), [#12985](https://github.com/qmk/qmk_firmware/pull/12985)) {#encoder-callback-boolean}

To allow for keyboards to override (or not) keymap level code the `encoder_update_kb` function has been changed from `void` to `bool`. You will need to update your function definition to reflect this and ensure that you return a `true` or `false` value.

Example code before change:

```c
void encoder_update_kb(uint8_t index, bool clockwise) {
    encoder_update_user(index, clockwise);
}

void encoder_update_user(uint8_t index, bool clockwise) {
    if (index == 0) { /* First encoder */
        if (clockwise) {
            tap_code(KC_PGDN);
        } else {
            tap_code(KC_PGUP);
        }
    } else if (index == 1) { /* Second encoder */
        if (clockwise) {
            tap_code(KC_DOWN);
        } else {
            tap_code(KC_UP);
        }
    }
}
```

Example code after change:

```c
bool encoder_update_kb(uint8_t index, bool clockwise) {
    return encoder_update_user(index, clockwise);
}

bool encoder_update_user(uint8_t index, bool clockwise) {
    if (index == 0) { /* First encoder */
        if (clockwise) {
            tap_code(KC_PGDN);
        } else {
            tap_code(KC_PGUP);
        }
    } else if (index == 1) { /* Second encoder */
        if (clockwise) {
            tap_code(KC_DOWN);
        } else {
            tap_code(KC_UP);
        }
    }
    return true; 
    // If you return true, this will allow the keyboard level code to run, as well. 
    //Returning false will override the keyboard level code. Depending on how the keyboard level function is set up.
}
```

## Core Changes {#core-changes}

### Fixes {#core-fixes}

* Fix connection issue in split keyboards when slave and OLED display are connected via I2C (fixes #9335) ([#11487](https://github.com/qmk/qmk_firmware/pull/11487))
* Terrazzo: Fix wrong LED Matrix function names ([#12561](https://github.com/qmk/qmk_firmware/pull/12561))
* Apply the "NO_LIMITED_CONTROLLER_CONNECT" fix to atmega16u2 ([#12482](https://github.com/qmk/qmk_firmware/pull/12482))
* Fix comment parsing ([#12750](https://github.com/qmk/qmk_firmware/pull/12750))
* Turn OLED off on suspend in soundmonster Corne keymap ([#10419](https://github.com/qmk/qmk_firmware/pull/10419))
* Fixup build errors on `develop` branch. ([#12723](https://github.com/qmk/qmk_firmware/pull/12723))
* Fix syntax error when compiling for ARM ([#12866](https://github.com/qmk/qmk_firmware/pull/12866))
* Add missing LED Matrix suspend code to suspend.c ([#12878](https://github.com/qmk/qmk_firmware/pull/12878))
* Fix spelling mistake regarding LED Matrix in split_common. ([#12888](https://github.com/qmk/qmk_firmware/pull/12888))
* [Keymap] Fix QWERTY/DVORAK status output for kzar keymap ([#12895](https://github.com/qmk/qmk_firmware/pull/12895))
* Fixup housekeeping from being invoked twice per loop. ([#12933](https://github.com/qmk/qmk_firmware/pull/12933))
* wait for matrix row signal to go HIGH for every row ([#12945](https://github.com/qmk/qmk_firmware/pull/12945))
* ensure we do not conflict with existing keymap aliases ([#12976](https://github.com/qmk/qmk_firmware/pull/12976))
* [Keyboard] Fix Terrazzo build failure ([#12977](https://github.com/qmk/qmk_firmware/pull/12977))
* Do not hard set config in CPTC files ([#11864](https://github.com/qmk/qmk_firmware/pull/11864))

### Additions and Enhancements {#core-additions}

* ARM - Refactor SLEEP_LED to support more platforms ([#8403](https://github.com/qmk/qmk_firmware/pull/8403))
* Add ability to toggle One Shot functionality ([#4198](https://github.com/qmk/qmk_firmware/pull/4198))
* Add RGB Matrix support to Split Common ([#11055](https://github.com/qmk/qmk_firmware/pull/11055))
* Add support for complementary outputs to the ChibiOS WS2812 PWM driver ([#11988](https://github.com/qmk/qmk_firmware/pull/11988))
* Enable RGB Matrix for Corne ([#12091](https://github.com/qmk/qmk_firmware/pull/12091))
* Set default OLED Update Interval for Split Keyboards to improve matrix scan performance ([#12107](https://github.com/qmk/qmk_firmware/pull/12107))
* Add support for MK66F18 (Teensy 3.6) micro controller ([#12258](https://github.com/qmk/qmk_firmware/pull/12258))
* Split RGB Matrix support for RGBKB Zygomorph ([#11083](https://github.com/qmk/qmk_firmware/pull/11083))
* Add baudrate and circular buffer to ARM WS2812 SPI config ([#12216](https://github.com/qmk/qmk_firmware/pull/12216))
* Add keyboard level weak function for slave matrix scan ([#12317](https://github.com/qmk/qmk_firmware/pull/12317))
* Add link to schematic on EasyEDA for XD60 ([#12018](https://github.com/qmk/qmk_firmware/pull/12018))
* Add Config functions for LED Matrix ([#12361](https://github.com/qmk/qmk_firmware/pull/12361))
* Add pin definitions for MK66F18 ([#12419](https://github.com/qmk/qmk_firmware/pull/12419))
* add kinesis/kint36 keyboard ([#10171](https://github.com/qmk/qmk_firmware/pull/10171))
* Add support for producing UF2-format binaries. ([#12435](https://github.com/qmk/qmk_firmware/pull/12435))
* Implement CIE1931 curve for LED Matrix ([#12417](https://github.com/qmk/qmk_firmware/pull/12417))
* Change `BOOTMAGIC_ENABLE=yes` to use Bootmagic Lite ([#12172](https://github.com/qmk/qmk_firmware/pull/12172))
* Add kzar keymap for Kinesis Advantage ([#12444](https://github.com/qmk/qmk_firmware/pull/12444))
* LED Matrix: suspend code ([#12509](https://github.com/qmk/qmk_firmware/pull/12509))
* LED Matrix: Task system ([#12580](https://github.com/qmk/qmk_firmware/pull/12580))
* Add missing RGB_MODE_TWINKLE / RGB_M_TW keycodes ([#11935](https://github.com/qmk/qmk_firmware/pull/11935))
* Enhancement of WPM feature ([#11727](https://github.com/qmk/qmk_firmware/pull/11727))
* Add Per Key functionality for AutoShift ([#11536](https://github.com/qmk/qmk_firmware/pull/11536))
* LED Matrix: Reactive effect buffers & advanced indicators ([#12588](https://github.com/qmk/qmk_firmware/pull/12588))
* LED Matrix: support for Split keyboards ([#12633](https://github.com/qmk/qmk_firmware/pull/12633))
* add setting to enable infinite timeout for leader key ([#6580](https://github.com/qmk/qmk_firmware/pull/6580), [#12721](https://github.com/qmk/qmk_firmware/pull/12721 "Fix bad PR merge for #6580"))
* Update ADC driver for STM32F1xx, STM32F3xx, STM32F4xx ([#12403](https://github.com/qmk/qmk_firmware/pull/12403))
* Add initial support for tinyuf2 bootloader (when hosted on F411 blackpill) ([#12600](https://github.com/qmk/qmk_firmware/pull/12600))
* Add support for STM32F446 MCU ([#12619](https://github.com/qmk/qmk_firmware/pull/12619))
* Add STM32L433 and L443 support ([#12063](https://github.com/qmk/qmk_firmware/pull/12063))
* Added OLED fade out support ([#12086](https://github.com/qmk/qmk_firmware/pull/12086))
* New command: `qmk console` ([#12828](https://github.com/qmk/qmk_firmware/pull/12828))
* LED Matrix: Effects! ([#12651](https://github.com/qmk/qmk_firmware/pull/12651))
* Add setup, clone, and env to the list of commands we allow even with broken modules ([#12868](https://github.com/qmk/qmk_firmware/pull/12868))
* LED Matrix: Documentation ([#12685](https://github.com/qmk/qmk_firmware/pull/12685))
* Add function to allow repeated blinking of one layer ([#12237](https://github.com/qmk/qmk_firmware/pull/12237))
* Add support for up to 4 IS31FL3733 drivers ([#12342](https://github.com/qmk/qmk_firmware/pull/12342))
* Convert Encoder callbacks to be boolean functions ([#12805](https://github.com/qmk/qmk_firmware/pull/12805), [#12985](https://github.com/qmk/qmk_firmware/pull/12985))
* [Keymap] Update to Drashna keymap and user code (based on develop) ([#12936](https://github.com/qmk/qmk_firmware/pull/12936))
* Add Full-duplex serial driver for ARM boards ([#9842](https://github.com/qmk/qmk_firmware/pull/9842))
* Document LED_MATRIX_FRAMEBUFFER_EFFECTS ([#12987](https://github.com/qmk/qmk_firmware/pull/12987))
* Backlight: add defines for default level and breathing state ([#12560](https://github.com/qmk/qmk_firmware/pull/12560), [#13024](https://github.com/qmk/qmk_firmware/pull/13024))
* Add dire message about LUFA mass storage bootloader ([#13014](https://github.com/qmk/qmk_firmware/pull/13014))

### Clean-ups and Optimizations {#core-optimizations}

* Overhaul bootmagic logic to have single entrypoint ([#8532](https://github.com/qmk/qmk_firmware/pull/8532))
* Refactor of USB code within split_common ([#11890](https://github.com/qmk/qmk_firmware/pull/11890))
* Begin the process of deprecating `bin/qmk` in favor of the global CLI ([#12109](https://github.com/qmk/qmk_firmware/pull/12109))
* LED Matrix: decouple from Backlight ([#12054](https://github.com/qmk/qmk_firmware/pull/12054))
* Remove `FUNC()` ([#12161](https://github.com/qmk/qmk_firmware/pull/12161))
* Move gpio wait logic to wait.h ([#12067](https://github.com/qmk/qmk_firmware/pull/12067))
* LED Matrix: Clean up includes ([#12197](https://github.com/qmk/qmk_firmware/pull/12197))
* Consistently use bin/qmk when that script is called ([#12286](https://github.com/qmk/qmk_firmware/pull/12286))
* LED Matrix: Additional common_features.mk tweaks ([#12187](https://github.com/qmk/qmk_firmware/pull/12187))
* LED Matrix: Fix up eeconfig code ([#12327](https://github.com/qmk/qmk_firmware/pull/12327))
* Big quantum_keycodes cleanup ([#12249](https://github.com/qmk/qmk_firmware/pull/12249))
* Fix up builds that are now too big for `develop` branch. ([#12495](https://github.com/qmk/qmk_firmware/pull/12495))
* [Keyboard] kint36: switch to sym_eager_pk debouncing ([#12626](https://github.com/qmk/qmk_firmware/pull/12626))
* [Keyboard] kint2pp: reduce input latency by ≈10ms ([#12625](https://github.com/qmk/qmk_firmware/pull/12625))
* eeprom driver: Refactor where eeprom driver initialisation (and EEPROM emulation initialisation) occurs to make it non-target-specific. ([#12671](https://github.com/qmk/qmk_firmware/pull/12671))
* Change RGB/LED Matrix to use a simple define for USB suspend ([#12697](https://github.com/qmk/qmk_firmware/pull/12697), [#12770](https://github.com/qmk/qmk_firmware/pull/12770 "Fixing transport's led/rgb matrix suspend state logic"))
* Remove pointless SERIAL_LINK_ENABLE rules ([#12846](https://github.com/qmk/qmk_firmware/pull/12846))
* Make Swap Hands use PROGMEM ([#12284](https://github.com/qmk/qmk_firmware/pull/12284))
* Remove KEYMAP and LAYOUT_kc ([#12160](https://github.com/qmk/qmk_firmware/pull/12160))
* Rename `point_t` -> `led_point_t` ([#12864](https://github.com/qmk/qmk_firmware/pull/12864))
* Deprecate `send_unicode_hex_string()` ([#12602](https://github.com/qmk/qmk_firmware/pull/12602))
* [Keyboard] Remove redundant legacy and common headers for crkbd ([#13023](https://github.com/qmk/qmk_firmware/pull/13023))

### QMK Infrastructure and Internals {#qmk-internals}

* trivial change to trigger api update ([`b15288fb87`](https://github.com/qmk/qmk_firmware/commit/b15288fb87))
* fix some references to bin/qmk that slipped in ([#12832](https://github.com/qmk/qmk_firmware/pull/12832))
* Resolve a number of warnings in `qmk generate-api` ([#12833](https://github.com/qmk/qmk_firmware/pull/12833))
* Fix another bin/qmk reference ([#12856](https://github.com/qmk/qmk_firmware/pull/12856))
* Use milc.subcommand.config instead of qmk.cli.config ([#12915](https://github.com/qmk/qmk_firmware/pull/12915))
