# QMK Breaking Change - 2020 Aug 29 Changelog

Four times a year QMK runs a process for merging Breaking Changes. A Breaking Change is any change which modifies how QMK behaves in a way that is incompatible or potentially dangerous. We limit these changes to 4 times per year so that users can have confidence that updating their QMK tree will not break their keymaps.


## Changes Requiring User Action {#changes-requiring-user-action}

### Relocated Keyboards {#relocated-keyboards}

#### The Key Company project consolidation ([#9547](https://github.com/qmk/qmk_firmware/pull/9547))
#### relocating boards by flehrad to flehrad/ folder ([#9635](https://github.com/qmk/qmk_firmware/pull/9635))

Keyboards released by The Key Company and keyboards designed by flehrad have moved to vendor folders. If you own any of the keyboards listed below, please use the new names to compile your firmware moving forward.

Old Name               | New Name
:--------------------- | :------------------
candybar/lefty         | tkc/candybar/lefty
candybar/righty        | tkc/candybar/righty
m0lly                  | tkc/m0lly
tkc1800                | tkc/tkc1800
bigswitch              | flehrad/bigswitch
handwired/downbubble   | flehrad/downbubble
handwired/numbrero     | flehrad/numbrero
snagpad                | flehrad/snagpad
handwired/tradestation | flehrad/tradestation

### Updated Keyboard Codebases {#keyboard-updates}

#### Keebio RGB wiring update ([#7754](https://github.com/qmk/qmk_firmware/pull/7754))

This pull request changes the configuration for Keebio split boards to use the same RGB strip wiring for each half, which provides the following improvements:

* Easier wiring due to one fewer wire needed (the wire between left DOut to extra data pin) and the fact that wiring is the same for both halves.
* RGB LEDs can be controlled by each half now instead of just master half.
* Extra data line is freed up to allow for I2C usage instead of serial.

If you have customized the value of `RGBLED_SPLIT` for your keymap, you will need to undefine it using `#undef RGBLED_SPLIT` before defining it to your customized value.

This change affects:

* BFO-9000
* Fourier
* Iris rev2
* Levinson, revs. 1 and 2
* Nyquist, revs. 1 and 2
* Quefrency rev1
* Viterbi, revs. 1 and 2

### Changes to Core Functionality {#core-updates}

* Bigger Combo index ([#9318](https://github.com/qmk/qmk_firmware/pull/9318))

Allows the Combo feature to support more than 256 combos.

Any fork that uses `process_combo_event` needs to update the function's first argument to `uint16_t`:

* Old function: `void process_combo_event(uint8_t combo_index, bool pressed)`
* New function: `void process_combo_event(uint16_t combo_index, bool pressed)`


## Core Changes {#core-changes}

### Fixes {#core-fixes}

* Mousekeys: scrolling acceleration is no longer coupled to mouse movement acceleration ([#9174](https://github.com/qmk/qmk_firmware/pull/9174))
* Keymap Extras: correctly assign Question Mark in Czech layout ([#9987](https://github.com/qmk/qmk_firmware/pull/9987))

### Additions and Enhancements {#core-additions}

* allow for WS2812 PWM to work on DMAMUX-capable devices ([#9471](https://github.com/qmk/qmk_firmware/pull/9471))
  * Newer STM32 MCUs have a DMAMUX peripheral, which allows mapping of DMAs to different DMA streams, rather than hard-defining the target streams in silicon.
  * Affects STM32L4+ devices, as well as the soon-to-be-supported-by-QMK STM32G4/H7 families.
  * Tested on F303/Proton C (ChibiOS v19, non-DMAMUX), G474 (ChibiOS v20, with DMAMUX).
* dual-bank STM32 bootloader support ([#8778](https://github.com/qmk/qmk_firmware/pull/8778) and [#9738](https://github.com/qmk/qmk_firmware/pull/9738))
  * Adds support for STM32 dual-bank flash bootloaders, by toggling a GPIO during early init in order to charge an RC circuit attached to `BOOT0`.
  * The main rationale behind this is that dual-bank STM32 devices unconditionally execute user-mode code, regardless of whether or not the user-mode code jumps to the bootloader. If either flash bank is valid (and `BOOT0` is low), then the built-in bootloader will skip any sort of DFU.
  * This PR allows for the initialisation sequencing to charge the RC circuit based on the example circuit posted on Discord, effectively pulling `BOOT0` high before issuing the system reset. As the RC circuit takes a while to discharge, the system reset executes the ROM bootloader which subsequently sees `BOOT0` high, and starts executing the DFU routines.
  * Tested with STM32L082 (with current QMK+current ChibiOS), and STM32G474 (against ChibiOS 20.x).
* update Space Cadet and Tap Dance features to use Custom Tapping Term when appropriate ([#6259](https://github.com/qmk/qmk_firmware/pull/6259))
  * For the Tap Dance feature, this completely removes the need for the `ACTION_TAP_DANCE_FN_ADVANCED_TIME` dance.
* HID Joystick Interface ([#4226](https://github.com/qmk/qmk_firmware/pull/4226) and [#9949](https://github.com/qmk/qmk_firmware/pull/9949 "Fix Joystick Compile Issues"))
  * This implements a joystick feature, including a joystick_task function called from TMK, specific keycodes for joystick buttons and a USB HID interface.
  * Tested on V-USB backend and Proton C; compiles but untested on LUFA.
  * In order to test, you have to add `JOYSTICK_ENABLE = yes` to your `rules.mk` and
    ```c
    #define JOYSTICK_BUTTON_COUNT 8
    #define JOYSTICK_AXES_COUNT 2
    ```
    in your config.h.
* Christmas RGB Underglow animation now fades between green and red ([#7648](https://github.com/qmk/qmk_firmware/pull/7648))
  * `RGBLIGHT_EFFECT_CHRISTMAS_INTERVAL` has been greatly decreased; please check your animation if you have customized this value.
* layer state now initializes on startup ([#8318](https://github.com/qmk/qmk_firmware/pull/8318))
  * This should produce more consistent behavior between the two functions and layer masks.
* added support for HSV->RGB conversion without using CIE curve ([#9856](https://github.com/qmk/qmk_firmware/pull/9856))
* added NOEEPROM functions for RGB Matrix ([#9487](https://github.com/qmk/qmk_firmware/pull/9487))
  * Added eeprom_helpers for toggle, mode, sethsv, speed, similar to rgblight versions.
  * Added set_speed function.
  * Added helper functions, similar to those in rgblight, in order to add NOEEPROM versions of toggle, step, hue, sat, val, and speed.
  * Minor: spelling correction for EEPROM in a debug message.
* flashing firmware using `st-flash` utility from [STLink Tools](https://github.com/stlink-org/stlink) is now supported ([#9964](https://github.com/qmk/qmk_firmware/pull/9964))
* add ability to dump all makefile variables for the specified target ([#8256](https://github.com/qmk/qmk_firmware/pull/8256))
  * Adds a new subtarget to builds, `dump_vars`, which allows for printing out all the variables that make knows about, after all substitutions occur.
  * Example: `make handwired/onekey/proton_c:default:dump_vars`
* add ability to change the Auto Shift timeout in real time ([#8441](https://github.com/qmk/qmk_firmware/pull/8441))
* added a timer implementation for backlight on ChibiOS ([#8291](https://github.com/qmk/qmk_firmware/pull/8291))
* added a third endpoint to V-USB keyboards ([#9020](https://github.com/qmk/qmk_firmware/pull/9020))
* added a method to read the OLED display buffer from user space ([#8777](https://github.com/qmk/qmk_firmware/pull/8777))
* K-Type refactor ([#9864](https://github.com/qmk/qmk_firmware/pull/9864))
  * The K-Type has been refactored to use QMK's native matrix scanning routine, and now has partial support for the RGB Matrix feature.
* Joysticks can now be used without defining analog pins ([#10169](https://github.com/qmk/qmk_firmware/pull/10169))

### Clean-ups and Optimizations {#core-optimizations}

* iWRAP protocol removed ([#9284](https://github.com/qmk/qmk_firmware/pull/9284))
* work begun for consolidation of ChibiOS platform files ([#8327](https://github.com/qmk/qmk_firmware/pull/8327) and [#9315](https://github.com/qmk/qmk_firmware/pull/9315))
  * Start of the consolidation work to move the ChibiOS board definitions as well as the default set of configuration files for existing board definitions used by keyboards.
    * Uses `/platforms/chibios` as previously discussed on discord.
    * Consolidates the Proton C configs into the generic F303 definitions.
    * Allows for defining a default set of `chconf.h`, `halconf.h`, and `mcuconf.h` files within the platform definition, which is able to be overridden by the keyboard directly, though include path ordering.
    * Adds template `chconf.h`, `halconf.h`, `mcuconf.h`, and `board.h` that can be dropped into a keyboard directory, in order to override rather than replace the entire contents of the respective files.
    * Removed Proton C QMK board definitions, falling back to ChibiOS board definitions with QMK overrides.
* Various tidy-ups for USB descriptor code ([#9005](https://github.com/qmk/qmk_firmware/pull/9005))
  * Renamed `keyboard_led_stats` in lufa.c and ChibiOS usb_main.c to `keyboard_led_state`, as well as `vusb_keyboard_leds`, for consistency
  * Formatted CDC and MIDI descriptors better
  * Removed `ENDPOINT_CONFIG` macro, it seems pointless and removes the need for endpoint address defines in the middle of the endpoint numbering enum
  * Fixed (possibly?) V-USB `GET_REPORT` request handling. Not sure about this one, but the existing code appears to always return an empty report - now `send_keyboard` sets this variable to the current report, matching what the LUFA code does.
* converted `CONSUMER2BLUEFRUIT()` and `CONSUMER2RN42()` macros to static inline functions ([#9055](https://github.com/qmk/qmk_firmware/pull/9055))
* Additional cleanups for V-USB code ([#9310](https://github.com/qmk/qmk_firmware/pull/9310))
  * Removing the UART stuff entirely, now that we have Console support. Also fixing up various other things; switching some `debug()` calls to `dprintf()`, moved `raw_hid_report` out of the way so that we can implement the shared endpoint stuff.
* removed inclusion of `adafruit_ble.h` from `ssd1306.c` ([#9355](https://github.com/qmk/qmk_firmware/pull/9355))
* `outputselect.c` is no longer compiled if Bluetooth is disabled ([#9356](https://github.com/qmk/qmk_firmware/pull/9356))
* `analogRead()` deprecated in favor of `analogReadPin()` ([#9023](https://github.com/qmk/qmk_firmware/pull/9023))
* forcibly disable NKRO on V-USB controllers ([#9054](https://github.com/qmk/qmk_firmware/pull/9054))
* removed warning if running backlight on STM32F072 ([#10040](https://github.com/qmk/qmk_firmware/pull/10040))
* removed unused CORTEX_VTOR_INIT rules.mk option ([#10053](https://github.com/qmk/qmk_firmware/pull/10053))
* improved handling for enabling Link Time Optimization ([#9832](https://github.com/qmk/qmk_firmware/pull/9832))
* streamline rules for supporting Kiibohd bootloader ([#10129](https://github.com/qmk/qmk_firmware/pull/10129))
* Define `STM32_DMA_REQUIRED` when using DMA-based WS2812 driver on STM32 ([#10127](https://github.com/qmk/qmk_firmware/pull/10127))
* fix DMA stream ID calculation in ws2812_pwm ([#10008](https://github.com/qmk/qmk_firmware/pull/10008))
* remove support for Adafruit EZ Key Bluetooth controller ([#10103](https://github.com/qmk/qmk_firmware/pull/10103))


## QMK Infrastructure and Internals {#qmk-internals}

* Attempt to fix CI for non-master branches. ([#9308](https://github.com/qmk/qmk_firmware/pull/9308))
  * Actually fetch the branch we're attempting to compare against.
* Run `qmk cformat` on `develop` branch ([#9501](https://github.com/qmk/qmk_firmware/pull/9501))
* minor refactor of Bluetooth API ([#9905](https://github.com/qmk/qmk_firmware/pull/9905))
