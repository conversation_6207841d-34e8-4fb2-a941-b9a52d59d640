# QMK Breaking Changes - 2023 November 26 Changelog

## Notable Features {#notable-features}

As per last few breaking changes cycles, there have been _a lot_ of behind-the-scenes changes, mainly around consolidation of config into `info.json` files, cleanup of `info.json` files, cleaning up driver naming, as well as addressing technical debt.

As a followup to last cycle's [notable changes](20230827#notable-changes), as `qmk/qmk_firmware` is no longer accepting PRs for keymaps we're pleased to announce that storing and building keymaps externally from the normal QMK Firmware repository is now possible. This is done through the new [External Userspace](../newbs_external_userspace) feature, more details below!

## Changes Requiring User Action {#changes-requiring-user-action}

### Updated Keyboard Codebases {#updated-keyboard-codebases}

| Old Keyboard Name                     | New Keyboard Name             |
|---------------------------------------|-------------------------------|
| adm42                                 | adm42/rev4                    |
| dp3000                                | dp3000/rev1                   |
| handwired/dactyl_manuform/dmote/62key | handwired/dmote               |
| keychron/q0/rev_0130                  | keychron/q0/base              |
| keychron/q0/rev_0131                  | keychron/q0/plus              |
| keychron/q1/ansi                      | keychron/q1v1/ansi            |
| keychron/q1/ansi_encoder              | keychron/q1v1/ansi_encoder    |
| keychron/q1/iso                       | keychron/q1v1/iso             |
| keychron/q1/iso_encoder               | keychron/q1v1/iso_encoder     |
| keychron/q4/ansi_v1                   | keychron/q4/ansi              |
| kprepublic/bm40hsrgb                  | kprepublic/bm40hsrgb/rev1     |
| matchstickworks/southpad              | matchstickworks/southpad/rev2 |
| soda/mango                            | magic_force/mf17              |
| soda/pocket                           | magic_force/mf34              |
| studiokestra/line_tkl                 | studiokestra/line_friends_tkl |
| ymdk/melody96                         | ymdk/melody96/soldered        |

## Notable core changes {#notable-core}

### External Userspace ([#22222](https://github.com/qmk/qmk_firmware/pull/22222))

As mentioned above, the new External Userspace feature allows for keymaps to be stored and built externally from the main QMK Firmware repository. This allows for keymaps to be stored separately -- usually in their own repository -- and for users to be able to maintain and build their keymaps without needing to fork the main QMK Firmware repository.

See the [External Userspace documentation](../newbs_external_userspace) for more details.

A significant portion of user keymaps have already been removed from `qmk/qmk_firmware` and more will follow in coming weeks. You can still recover your keymap from the tag [user-keymaps-still-present](https://github.com/qmk/qmk_firmware/tree/user-keymaps-still-present) if required -- a perfect time to migrate to the new External Userspace!

::: warning
This feature is still in beta, and we're looking for feedback on it. Please try it out and let us know what you think -- a new `#help-userspace` channel has been set up on Discord.
:::

### Improve and Cleanup Shutdown callbacks ([#21060](https://github.com/qmk/qmk_firmware/pull/20160)) {#improve-and-cleanup-shutdown-callbacks}

Shutdown callbacks at the keyboard level were never present, preventing safe shutdown sequencing for peripherals such as OLEDs, RGB LEDs, and other devices. This PR adds a new `shutdown_kb` function, as well as amending `shutdown_user`, allowing for safe shutdown of peripherals at both keyboard and keymap level.

See the [Keyboard Shutdown/Reboot Code](../custom_quantum_functions#keyboard-shutdown-reboot-code) documentation for more details.

### OLED Force Flush ([#20953](https://github.com/qmk/qmk_firmware/pull/20953)) {#oled-force-flush}

Along with the new `shutdown_kb` function, a new API `oled_render_dirty(bool)` function has been added. This allows OLED contents to be written deterministically when supplied with `true` -- that is, the OLED will be updated immediately, rather than waiting for the next OLED update cycle. This allows for OLEDs to show things such as "BOOTLOADER MODE" and the like if resetting to bootloader from QMK.

### Switch statement helpers for keycode ranges ([#20059](https://github.com/qmk/qmk_firmware/pull/20059)) {#switch-statement-helpers-for-keycode-ranges}

Predefined ranges usable within switch statements have been added for groups of similar keycodes, where people who wish to handle entire blocks at once can do so. This allows keymaps to be immune to changes in keycode values, and also allows for more efficient code generation.

The ranges are as follows:

| Name                                | Mapping                                                                |
|-------------------------------------|------------------------------------------------------------------------|
| `INTERNAL_KEYCODE_RANGE`            | `KC_NO ... KC_TRANSPARENT`                                             |
| `BASIC_KEYCODE_RANGE`               | `KC_A ... KC_EXSEL`                                                    |
| `SYSTEM_KEYCODE_RANGE`              | `KC_SYSTEM_POWER ... KC_SYSTEM_WAKE`                                   |
| `CONSUMER_KEYCODE_RANGE`            | `KC_AUDIO_MUTE ... KC_LAUNCHPAD`                                       |
| `MOUSE_KEYCODE_RANGE`               | `KC_MS_UP ... KC_MS_ACCEL2`                                            |
| `MODIFIER_KEYCODE_RANGE`            | `KC_LEFT_CTRL ... KC_RIGHT_GUI`                                        |
| `SWAP_HANDS_KEYCODE_RANGE`          | `QK_SWAP_HANDS_TOGGLE ... QK_SWAP_HANDS_ONE_SHOT`                      |
| `MAGIC_KEYCODE_RANGE`               | `QK_MAGIC_SWAP_CONTROL_CAPS_LOCK ... QK_MAGIC_TOGGLE_ESCAPE_CAPS_LOCK` |
| `MIDI_KEYCODE_RANGE`                | `QK_MIDI_ON ... QK_MIDI_PITCH_BEND_UP`                                 |
| `SEQUENCER_KEYCODE_RANGE`           | `QK_SEQUENCER_ON ... QK_SEQUENCER_STEPS_CLEAR`                         |
| `JOYSTICK_KEYCODE_RANGE`            | `QK_JOYSTICK_BUTTON_0 ... QK_JOYSTICK_BUTTON_31`                       |
| `PROGRAMMABLE_BUTTON_KEYCODE_RANGE` | `QK_PROGRAMMABLE_BUTTON_1 ... QK_PROGRAMMABLE_BUTTON_32`               |
| `AUDIO_KEYCODE_RANGE`               | `QK_AUDIO_ON ... QK_AUDIO_VOICE_PREVIOUS`                              |
| `STENO_KEYCODE_RANGE`               | `QK_STENO_BOLT ... QK_STENO_COMB_MAX`                                  |
| `MACRO_KEYCODE_RANGE`               | `QK_MACRO_0 ... QK_MACRO_31`                                           |
| `BACKLIGHT_KEYCODE_RANGE`           | `QK_BACKLIGHT_ON ... QK_BACKLIGHT_TOGGLE_BREATHING`                    |
| `RGB_KEYCODE_RANGE`                 | `RGB_TOG ... RGB_MODE_TWINKLE`                                         |
| `QUANTUM_KEYCODE_RANGE`             | `QK_BOOTLOADER ... QK_ALT_REPEAT_KEY`                                  |
| `KB_KEYCODE_RANGE`                  | `QK_KB_0 ... QK_KB_31`                                                 |
| `USER_KEYCODE_RANGE`                | `QK_USER_0 ... QK_USER_31`                                             |

Usage:

```c
    switch (keycode) {
        case KC_A ... KC_EXSEL:
        case KC_LEFT_CTRL ... KC_RIGHT_GUI:
        /* do stuff with basic and modifier keycodes */
```

Becomes:

```c
    switch (keycode) {
        case BASIC_KEYCODE_RANGE:
        case MODIFIER_KEYCODE_RANGE:
        /* do stuff with basic and modifier keycodes */
```

### Quantum Painter OLED support ([#19997](https://github.com/qmk/qmk_firmware/pull/19997)) {#quantum-painter-oled-support}

Quantum Painter has picked up support for SH1106 displays -- commonly seen as 128x64 OLEDs. Support for both I2C and SPI displays is available.

If you're already using OLED through `OLED_DRIVER_ENABLE = yes` or equivalent in `info.json` and wish to use Quantum Painter instead, you'll need to disable the old OLED system, instead enabling Quantum Painter as well as enabling the appropriate SH1106 driver. See the [Quantum Painter driver documentation](../quantum_painter#quantum-painter-drivers) for more details. The old OLED driver is still available, and keymaps do not require migrating to Quantum Painter if you don't want to do so.

### RGB/LED lighting driver naming and cleanup ([#21890](https://github.com/qmk/qmk_firmware/pull/21890), [#21891](https://github.com/qmk/qmk_firmware/pull/21891), [#21892](https://github.com/qmk/qmk_firmware/pull/21892), [#21903](https://github.com/qmk/qmk_firmware/pull/21903), [#21904](https://github.com/qmk/qmk_firmware/pull/21904), [#21905](https://github.com/qmk/qmk_firmware/pull/21905), [#21918](https://github.com/qmk/qmk_firmware/pull/21918), [#21929](https://github.com/qmk/qmk_firmware/pull/21929), [#21938](https://github.com/qmk/qmk_firmware/pull/21938), [#22004](https://github.com/qmk/qmk_firmware/pull/22004), [#22008](https://github.com/qmk/qmk_firmware/pull/22008), [#22009](https://github.com/qmk/qmk_firmware/pull/22009), [#22071](https://github.com/qmk/qmk_firmware/pull/22071), [#22090](https://github.com/qmk/qmk_firmware/pull/22090), [#22099](https://github.com/qmk/qmk_firmware/pull/22099), [#22126](https://github.com/qmk/qmk_firmware/pull/22126), [#22133](https://github.com/qmk/qmk_firmware/pull/22133), [#22163](https://github.com/qmk/qmk_firmware/pull/22163), [#22200](https://github.com/qmk/qmk_firmware/pull/22200), [#22308](https://github.com/qmk/qmk_firmware/pull/22308), [#22309](https://github.com/qmk/qmk_firmware/pull/22309), [#22311](https://github.com/qmk/qmk_firmware/pull/22311), [#22325](https://github.com/qmk/qmk_firmware/pull/22325), [#22365](https://github.com/qmk/qmk_firmware/pull/22365), [#22379](https://github.com/qmk/qmk_firmware/pull/22379), [#22380](https://github.com/qmk/qmk_firmware/pull/22380), [#22381](https://github.com/qmk/qmk_firmware/pull/22381), [#22383](https://github.com/qmk/qmk_firmware/pull/22383), [#22436](https://github.com/qmk/qmk_firmware/pull/22436))

As you can probably tell by the list of PRs just above, there has been a lot of cleanup and consolidation this cycle when it comes to RGB/LED lighting drivers. The number of changes is too large to list here, but the general theme has been focusing on consistency of naming, both of drivers themselves and their respective implementation and configuration. Most changes only affect keyboard designers -- if you find that your in-development keyboard is no longer building due to naming of defines changing, your best bet is to refer to another board already in the repository which has had the changes applied.

### Peripheral subsystem enabling ([#22253](https://github.com/qmk/qmk_firmware/pull/22253), [#22448](https://github.com/qmk/qmk_firmware/pull/22448), [#22106](https://github.com/qmk/qmk_firmware/pull/22106)) {#peripheral-subsystem-enabling}

When enabling peripherals such as I2C, SPI, or Analog/ADC, some required manual inclusion of source files in order to provide driver support, and in some cases, when multiple drivers were using the same underlying peripheral, files were being added to the build multiple times.

Most systems requiring other peripherals now mark their respective dependencies as "required", allowing the build system to check whether peripherals are necessary before including them in the build rather than having each location enable them manually.

For a concrete example, users or keyboard designers who previously added `SRC += analog.c` in order to allow for analog readings via an ADC now should specify `ANALOG_DRIVER_REQUIRED = yes` instead. The full list of added options is as follows:

| New option                     | Old Equivalent                                             |
|--------------------------------|------------------------------------------------------------|
| `ANALOG_DRIVER_REQUIRED = yes` | `SRC += analog.c`                                          |
| `APA102_DRIVER_REQUIRED = yes` | `SRC += apa102.c`                                          |
| `I2C_DRIVER_REQUIRED = yes`    | `SRC += i2c_master.c` or `QUANTUM_LIB_SRC += i2c_master.c` |
| `SPI_DRIVER_REQUIRED = yes`    | `SRC += spi_master.c` or `QUANTUM_LIB_SRC += spi_master.c` |
| `UART_DRIVER_REQUIRED = yes`   | `SRC += uart.c`                                            |
| `WS2812_DRIVER_REQUIRED = yes` | `SRC += ws2812.c`                                          |

### NKRO on V-USB boards ([#22398](https://github.com/qmk/qmk_firmware/pull/22398)) {#vusb-nkro}

NKRO is now available for ATmega32A and 328P-based keyboards (including PS2AVRGB/Bootmapper boards), thanks to some internal refactoring and cleanup. To enable it, the process is the same as always - add `NKRO_ENABLE = yes` to your `rules.mk`, then assign and press the `NK_TOGG` keycode to switch modes.

## Full changelist {#full-changelist}

Core:
* Compilation warning if both `keymap.json` and `keymap.c` exist ([#19939](https://github.com/qmk/qmk_firmware/pull/19939))
* [QP] Add support for OLED, variable framebuffer bpp ([#19997](https://github.com/qmk/qmk_firmware/pull/19997))
* Generate switch statement helpers for keycode ranges ([#20059](https://github.com/qmk/qmk_firmware/pull/20059))
* Chibios SPI driver: allow some SPI pins to be left unassigned ([#20315](https://github.com/qmk/qmk_firmware/pull/20315))
* Take care of scroll divisor remainders for PS/2 drag scroll ([#20732](https://github.com/qmk/qmk_firmware/pull/20732))
* Add `RGBLIGHT_DEFAULT_ON` macro configuration option ([#20857](https://github.com/qmk/qmk_firmware/pull/20857))
* Allow force flush of oled display. ([#20953](https://github.com/qmk/qmk_firmware/pull/20953))
* Improve and Cleanup Shutdown callbacks ([#21060](https://github.com/qmk/qmk_firmware/pull/21060))
* [Enhancement] QP Getters ([#21171](https://github.com/qmk/qmk_firmware/pull/21171))
* Russian typewriter keymap file for popular legacy layout. ([#21174](https://github.com/qmk/qmk_firmware/pull/21174))
* Improve directional transition of overlapping mouse keys ([#21494](https://github.com/qmk/qmk_firmware/pull/21494))
* Add full solenoid support on split keyboards ([#21583](https://github.com/qmk/qmk_firmware/pull/21583))
* Reduce popping during audio initialization using the additive DAC ([#21642](https://github.com/qmk/qmk_firmware/pull/21642))
* [Maintenance] USB HID control packet as struct ([#21688](https://github.com/qmk/qmk_firmware/pull/21688))
* Bump mouse endpoint packet size to 16 bytes ([#21711](https://github.com/qmk/qmk_firmware/pull/21711))
* Allow customizing PWM frequency ([#21717](https://github.com/qmk/qmk_firmware/pull/21717))
* Add simpler method for relocating functions to RAM. ([#21804](https://github.com/qmk/qmk_firmware/pull/21804))
* Clean up RGB LED type ([#21859](https://github.com/qmk/qmk_firmware/pull/21859))
* is31fl3741: Allow changing config register ([#21861](https://github.com/qmk/qmk_firmware/pull/21861))
* Add _DEFAULT_ON lighting configuration options ([#21865](https://github.com/qmk/qmk_firmware/pull/21865))
* Modify split config is_keyboard_master/left checks. ([#21875](https://github.com/qmk/qmk_firmware/pull/21875))
* Remove old `IS_LED_ON/OFF()` macros ([#21878](https://github.com/qmk/qmk_firmware/pull/21878))
* ckled2001: driver naming cleanups ([#21890](https://github.com/qmk/qmk_firmware/pull/21890))
* aw20216: driver naming cleanups ([#21891](https://github.com/qmk/qmk_firmware/pull/21891))
* is31fl3218: driver naming cleanups ([#21892](https://github.com/qmk/qmk_firmware/pull/21892))
* is31fl3736: driver naming cleanups ([#21903](https://github.com/qmk/qmk_firmware/pull/21903))
* is31fl3737: driver naming cleanups ([#21904](https://github.com/qmk/qmk_firmware/pull/21904))
* is31fl3733: driver naming cleanups ([#21905](https://github.com/qmk/qmk_firmware/pull/21905))
* Enable RP2040 support for apa102 RGB LED driver ([#21908](https://github.com/qmk/qmk_firmware/pull/21908))
* is31fl3731: driver naming cleanups ([#21918](https://github.com/qmk/qmk_firmware/pull/21918))
* is31fl3741: driver naming cleanups ([#21929](https://github.com/qmk/qmk_firmware/pull/21929))
* refactor: move default RGB/LED matrix #defines ([#21938](https://github.com/qmk/qmk_firmware/pull/21938))
* Added flower blooming on RGB Matrix effect ([#21948](https://github.com/qmk/qmk_firmware/pull/21948))
* Remove 'Firmware size check does not yet support' message ([#21977](https://github.com/qmk/qmk_firmware/pull/21977))
* chibios: mark boot2 bootlader data readonly ([#21986](https://github.com/qmk/qmk_firmware/pull/21986))
* Complete RGB Matrix support for IS31FL3218 ([#22004](https://github.com/qmk/qmk_firmware/pull/22004))
* Default wear leveling logical size to half backing ([#22006](https://github.com/qmk/qmk_firmware/pull/22006))
* chibios: disable RWX segment warning on newer GNU lds ([#22007](https://github.com/qmk/qmk_firmware/pull/22007))
* Add and use I2C address defines for ISSI LED drivers ([#22008](https://github.com/qmk/qmk_firmware/pull/22008))
* Add and use PWM frequency defines for ISSI LED drivers ([#22009](https://github.com/qmk/qmk_firmware/pull/22009))
* directly use object files when linking ELF ([#22025](https://github.com/qmk/qmk_firmware/pull/22025))
* Lvgl rate control ([#22049](https://github.com/qmk/qmk_firmware/pull/22049))
* Rename CKLED2001 driver to SNLED27351 ([#22071](https://github.com/qmk/qmk_firmware/pull/22071))
* Move `PACKED` define to util.h ([#22074](https://github.com/qmk/qmk_firmware/pull/22074))
* Simplify more feature driver defines ([#22090](https://github.com/qmk/qmk_firmware/pull/22090))
* Update ISSI LED types ([#22099](https://github.com/qmk/qmk_firmware/pull/22099))
* Move velocikey to within rgblight ([#22123](https://github.com/qmk/qmk_firmware/pull/22123))
* is31fl3218: Add LED Matrix support ([#22126](https://github.com/qmk/qmk_firmware/pull/22126))
* Set default board files for uf2boot bootloader ([#22129](https://github.com/qmk/qmk_firmware/pull/22129))
* is31fl3736: extract single-color API ([#22133](https://github.com/qmk/qmk_firmware/pull/22133))
* is31fl3737/3741: add LED Matrix support ([#22163](https://github.com/qmk/qmk_firmware/pull/22163))
* Rename `DRIVER_ADDR_n` defines ([#22200](https://github.com/qmk/qmk_firmware/pull/22200))
* New RGB Animations - 4 "Starlight" Animation Variations ([#22212](https://github.com/qmk/qmk_firmware/pull/22212))
* QMK Userspace ([#22222](https://github.com/qmk/qmk_firmware/pull/22222))
* Dedupe I2C, SPI, UART driver inclusions ([#22253](https://github.com/qmk/qmk_firmware/pull/22253))
* Add "AC Next Keyboard Layout Select" consumer usage entry (macOS Globe key) ([#22256](https://github.com/qmk/qmk_firmware/pull/22256))
* Separate 6KRO and NKRO report structs ([#22267](https://github.com/qmk/qmk_firmware/pull/22267))
* Azoteq IQS5xx support ([#22280](https://github.com/qmk/qmk_firmware/pull/22280))
* Add `_flush()` functions to LED drivers ([#22308](https://github.com/qmk/qmk_firmware/pull/22308))
* Add `_LED_COUNT` defines to LED drivers ([#22309](https://github.com/qmk/qmk_firmware/pull/22309))
* Infer LED DRIVER_COUNT from configured addresses ([#22311](https://github.com/qmk/qmk_firmware/pull/22311))
* Added gamma values for ST7735 displays ([#22313](https://github.com/qmk/qmk_firmware/pull/22313))
* Consolidate some EEPROM Driver configuration ([#22321](https://github.com/qmk/qmk_firmware/pull/22321))
* V-USB: Add generic `send_report()` function ([#22323](https://github.com/qmk/qmk_firmware/pull/22323))
* V-USB: Implement `GET_PROTOCOL` and `SET_PROTOCOL` handling ([#22324](https://github.com/qmk/qmk_firmware/pull/22324))
* RGB/LED matrix use limits size optimisation ([#22325](https://github.com/qmk/qmk_firmware/pull/22325))
* Relocate LED driver init code ([#22365](https://github.com/qmk/qmk_firmware/pull/22365))
* WT RGB cleanups ([#22379](https://github.com/qmk/qmk_firmware/pull/22379))
* LED drivers: use `PACKED` define from util.h ([#22380](https://github.com/qmk/qmk_firmware/pull/22380))
* LED drivers: clean up `SWx`/`CSy` pullup/down resistor config ([#22381](https://github.com/qmk/qmk_firmware/pull/22381))
* LED drivers: add defines for PWM and LED control register counts ([#22383](https://github.com/qmk/qmk_firmware/pull/22383))
* V-USB: implement NKRO ([#22398](https://github.com/qmk/qmk_firmware/pull/22398))
* Allow generic_features to handle subdirectories ([#22400](https://github.com/qmk/qmk_firmware/pull/22400))
* Migrate some common features to generic ([#22403](https://github.com/qmk/qmk_firmware/pull/22403))
* Remove requirement for `keymap_steno.h` include in keymaps ([#22423](https://github.com/qmk/qmk_firmware/pull/22423))
* LED drivers: register naming cleanups ([#22436](https://github.com/qmk/qmk_firmware/pull/22436))
* Slight refactor of joystick axis type into typedef ([#22445](https://github.com/qmk/qmk_firmware/pull/22445))
* Generalise analog SRC inclusion ([#22448](https://github.com/qmk/qmk_firmware/pull/22448))
* Revert "chibios: disable RWX segment warning on newer GNU lds" ([#22469](https://github.com/qmk/qmk_firmware/pull/22469))
* chibios: disable RWX segment warning on newer GNU lds ([#22471](https://github.com/qmk/qmk_firmware/pull/22471))

CLI:
* Implement data driven lighting defaults ([#21825](https://github.com/qmk/qmk_firmware/pull/21825))
* Generate keymap.json config options more forcefully ([#21960](https://github.com/qmk/qmk_firmware/pull/21960))
* Implement data driven dip switches ([#22017](https://github.com/qmk/qmk_firmware/pull/22017))
* Improve argument handling of c2json ([#22170](https://github.com/qmk/qmk_firmware/pull/22170))
* Support additional split sync items for info.json ([#22193](https://github.com/qmk/qmk_firmware/pull/22193))
* CLI refactoring for common build target APIs ([#22221](https://github.com/qmk/qmk_firmware/pull/22221))
* Add dd mapping for hardware based split handedness ([#22369](https://github.com/qmk/qmk_firmware/pull/22369))
* CLI parallel search updates ([#22525](https://github.com/qmk/qmk_firmware/pull/22525))
* Remove duplicates from search results ([#22528](https://github.com/qmk/qmk_firmware/pull/22528))

Keyboards:
* Add KPRepublic/BM40hsrgb rev2 ([#16689](https://github.com/qmk/qmk_firmware/pull/16689))
* update to data driven - superseeds part of https://github.com/qmk/qmk… ([#20220](https://github.com/qmk/qmk_firmware/pull/20220))
* Modernize `dactyl_manuform/dmote` keyboard ([#20427](https://github.com/qmk/qmk_firmware/pull/20427))
* add Skyloong/GK61_V1 keyboard ([#21364](https://github.com/qmk/qmk_firmware/pull/21364))
* [Refactor] Make changes to some pins ([#21380](https://github.com/qmk/qmk_firmware/pull/21380))
* Add missing fullsize extended default layouts ([#21402](https://github.com/qmk/qmk_firmware/pull/21402))
* Add Skyloong/Gk61 PRO keyboard ([#21450](https://github.com/qmk/qmk_firmware/pull/21450))
* Added skyloong/Qk21 v1 Number Pad ([#21467](https://github.com/qmk/qmk_firmware/pull/21467))
* matchstickworks/southpad - Move files to rev1, add rev2 ([#21574](https://github.com/qmk/qmk_firmware/pull/21574))
* partially modernize `dactyl_minidox` ([#21576](https://github.com/qmk/qmk_firmware/pull/21576))
* tominabox1/le_chiffre oled rework ([#21611](https://github.com/qmk/qmk_firmware/pull/21611))
* Add Skyloong/Gk61_pro_48 keyboard ([#21654](https://github.com/qmk/qmk_firmware/pull/21654))
* Adding support for new Waffling60 revision ([#21664](https://github.com/qmk/qmk_firmware/pull/21664))
* Leeloo revision 2 updates. ([#21671](https://github.com/qmk/qmk_firmware/pull/21671))
* rename og60 to tofu60 ([#21684](https://github.com/qmk/qmk_firmware/pull/21684))
* add tofujr v2 keyboard ([#21740](https://github.com/qmk/qmk_firmware/pull/21740))
* Rotary numpad ([#21744](https://github.com/qmk/qmk_firmware/pull/21744))
* Update era/divine ([#21767](https://github.com/qmk/qmk_firmware/pull/21767))
* 1UpKeyboards Pi60 Layout Additions ([#21874](https://github.com/qmk/qmk_firmware/pull/21874))
* BIOI keyboards: use core UART driver ([#21879](https://github.com/qmk/qmk_firmware/pull/21879))
* Resolve some "Layout should not contain name of keyboard" lint warnings ([#21898](https://github.com/qmk/qmk_firmware/pull/21898))
* fc660c/fc980c: clean up actuation point adjustment code ([#21964](https://github.com/qmk/qmk_firmware/pull/21964))
* Chromatonemini info json revised to support qmk 0.22.2 ([#21966](https://github.com/qmk/qmk_firmware/pull/21966))
* Migrate spi_flash WEAR_LEVELING_DRIVER to info.json ([#21978](https://github.com/qmk/qmk_firmware/pull/21978))
* Remove duplication of RP2040 EEPROM defaults ([#21979](https://github.com/qmk/qmk_firmware/pull/21979))
* Remove duplication of STM32L432 EEPROM defaults ([#21981](https://github.com/qmk/qmk_firmware/pull/21981))
* Migrate spi EEPROM_DRIVER to info.json ([#21991](https://github.com/qmk/qmk_firmware/pull/21991))
* Update Keychron Q1v1 ([#21993](https://github.com/qmk/qmk_firmware/pull/21993))
* Update Keychron Q2 ([#21994](https://github.com/qmk/qmk_firmware/pull/21994))
* Update Keychron Q3 ([#21995](https://github.com/qmk/qmk_firmware/pull/21995))
* Update Keychron Q4 ([#21996](https://github.com/qmk/qmk_firmware/pull/21996))
* Migrate WEAR_LEVELING_*_SIZE to info.json ([#22010](https://github.com/qmk/qmk_firmware/pull/22010))
* Remove duplication of EEPROM defaults ([#22011](https://github.com/qmk/qmk_firmware/pull/22011))
* Migrate i2c EEPROM_DRIVER to info.json ([#22013](https://github.com/qmk/qmk_firmware/pull/22013))
* Remove config.h which only set DYNAMIC_KEYMAP_LAYER_COUNT ([#22034](https://github.com/qmk/qmk_firmware/pull/22034))
* Add community layout support to tofu60 ([#22041](https://github.com/qmk/qmk_firmware/pull/22041))
* Update Keychron Q0 ([#22068](https://github.com/qmk/qmk_firmware/pull/22068))
* Remove custom ISSI lighting code ([#22073](https://github.com/qmk/qmk_firmware/pull/22073))
* add dp3000 rev2 featuring rgblight ([#22084](https://github.com/qmk/qmk_firmware/pull/22084))
* Remove ALLOW_WARNINGS and PICO_INTRINSICS_ENABLED ([#22085](https://github.com/qmk/qmk_firmware/pull/22085))
* Partially migrate `DYNAMIC_KEYMAP_LAYER_COUNT` ([#22087](https://github.com/qmk/qmk_firmware/pull/22087))
* feat(eyeohdesigns/babyv): rgb matrix ([#22105](https://github.com/qmk/qmk_firmware/pull/22105))
* input_club/infinity60: remove custom 3731 code, convert to LED Matrix ([#22117](https://github.com/qmk/qmk_firmware/pull/22117))
* YMDK Melody96 Break-Up ([#22121](https://github.com/qmk/qmk_firmware/pull/22121))
* Remove duplicated rgblight implementation from mxss ([#22122](https://github.com/qmk/qmk_firmware/pull/22122))
* KC60 Layout Standardization and Cleanup ([#22125](https://github.com/qmk/qmk_firmware/pull/22125))
* Convert adm42 to data driven ([#22144](https://github.com/qmk/qmk_firmware/pull/22144))
* Update Drop keyboards for develop ([#22145](https://github.com/qmk/qmk_firmware/pull/22145))
* move soda/mango and soda/pocket to magic_force/mf17 and magic_force/mf34 ([#22151](https://github.com/qmk/qmk_firmware/pull/22151))
* GMMK2 65% ISO Community Layout Support ([#22152](https://github.com/qmk/qmk_firmware/pull/22152))
* Leeloo v2.1 revision 3 updates. ([#22236](https://github.com/qmk/qmk_firmware/pull/22236))
* jian/rev1: convert to DIP Switch ([#22248](https://github.com/qmk/qmk_firmware/pull/22248))
* Enable linking of encoders to switch within layout macros ([#22264](https://github.com/qmk/qmk_firmware/pull/22264))
* Migrate recently introduced sync items ([#22305](https://github.com/qmk/qmk_firmware/pull/22305))
* Rename LINE FRIENDS TKL keyboard ([#22310](https://github.com/qmk/qmk_firmware/pull/22310))
* feat(mechwild/clunker): new layouts ([#22342](https://github.com/qmk/qmk_firmware/pull/22342))
* Remove use of broken split.main ([#22363](https://github.com/qmk/qmk_firmware/pull/22363))
* whitefox: remove pointless file ([#22366](https://github.com/qmk/qmk_firmware/pull/22366))
* Migrate some EEPROM config to info.json ([#22434](https://github.com/qmk/qmk_firmware/pull/22434))
* Remove unnecessary driver counts ([#22435](https://github.com/qmk/qmk_firmware/pull/22435))
* Migrate some dip switch config to info.json ([#22437](https://github.com/qmk/qmk_firmware/pull/22437))
* Remove userspace keymaps ([#22544](https://github.com/qmk/qmk_firmware/pull/22544))
* Stub out community layout directory structure ([#22545](https://github.com/qmk/qmk_firmware/pull/22545))
* Remove symbolic linked userspace folder ([#22548](https://github.com/qmk/qmk_firmware/pull/22548))

Keyboard fixes:
* fix unxmaal for 60_iso ([#21975](https://github.com/qmk/qmk_firmware/pull/21975))
* Fix input_club/k_type when RGB Matrix disabled ([#22021](https://github.com/qmk/qmk_firmware/pull/22021))
* Fixup snes_macropad on develop ([#22444](https://github.com/qmk/qmk_firmware/pull/22444))
* Fix missed shutdown callbacks ([#22549](https://github.com/qmk/qmk_firmware/pull/22549))

Others:
* Implement data driven wear leveling ([#21906](https://github.com/qmk/qmk_firmware/pull/21906))
* More data driven RGB/LED Matrix config ([#21939](https://github.com/qmk/qmk_firmware/pull/21939))
* Update WS2812 docs and add APA102 docs ([#22106](https://github.com/qmk/qmk_firmware/pull/22106))
* Add DD mappings for locking switch ([#22242](https://github.com/qmk/qmk_firmware/pull/22242))

Bugs:
* Improve test invocation, fix Retro Shift bugs, and add Auto+Retro Shift test cases ([#15889](https://github.com/qmk/qmk_firmware/pull/15889))
* [Bugfix] `qp_ellipse` overflow ([#19005](https://github.com/qmk/qmk_firmware/pull/19005))
* Cater for ECC failures in EFL wear-leveling. ([#19749](https://github.com/qmk/qmk_firmware/pull/19749))
* Fix OSM on a OSL activated layer ([#20410](https://github.com/qmk/qmk_firmware/pull/20410))
* Fixed WB32 MCU remote wakeup issue ([#20863](https://github.com/qmk/qmk_firmware/pull/20863))
* Optimize the additive DAC code, fixing performance-related hangs ([#21662](https://github.com/qmk/qmk_firmware/pull/21662))
* [Enhancement] Improvements for debounce test coverage + bug fixes for sym_defer_g and sym_eager_pr ([#21667](https://github.com/qmk/qmk_firmware/pull/21667))
* fix: make clicky delay silent ([#21866](https://github.com/qmk/qmk_firmware/pull/21866))
* Add `mousekey.h` include to `quantum.h` ([#21897](https://github.com/qmk/qmk_firmware/pull/21897))
* Fix default layer value in eeconfig_init ([#21909](https://github.com/qmk/qmk_firmware/pull/21909))
* Add RTC IRQ Priority to RP2040 board files ([#21926](https://github.com/qmk/qmk_firmware/pull/21926))
* Update AW20216S LED type ([#22072](https://github.com/qmk/qmk_firmware/pull/22072))
* LED/RGB Matrix: prefix driver defines ([#22088](https://github.com/qmk/qmk_firmware/pull/22088))
* RGBLight/Backlight: add prefixed driver defines ([#22089](https://github.com/qmk/qmk_firmware/pull/22089))
* Fix lower cpi bound on PMW33XX ([#22108](https://github.com/qmk/qmk_firmware/pull/22108))
* Fix parsing/validation for 21939 ([#22148](https://github.com/qmk/qmk_firmware/pull/22148))
* is31fl3733: complete LED Matrix support ([#22149](https://github.com/qmk/qmk_firmware/pull/22149))
* Fix memory leak in realloc failure handling ([#22188](https://github.com/qmk/qmk_firmware/pull/22188))
* avrdude: Version 7.2 changes the text output ([#22235](https://github.com/qmk/qmk_firmware/pull/22235))
* Resolve invalid keyboard alias targets ([#22239](https://github.com/qmk/qmk_firmware/pull/22239))
* Prep work for NKRO report separation ([#22268](https://github.com/qmk/qmk_firmware/pull/22268))
* ChibiOS pin defs: use only vendor if present ([#22297](https://github.com/qmk/qmk_firmware/pull/22297))
* Fix invalid LED driver config ([#22312](https://github.com/qmk/qmk_firmware/pull/22312))
* Fix compilation error when led/rgb process limit is zero. ([#22328](https://github.com/qmk/qmk_firmware/pull/22328))
* V-USB: Fix `GET_IDLE/SET_IDLE` ([#22332](https://github.com/qmk/qmk_firmware/pull/22332))
* QP getters correction ([#22357](https://github.com/qmk/qmk_firmware/pull/22357))
* Fix 'to_c' for config.h mappings ([#22364](https://github.com/qmk/qmk_firmware/pull/22364))
* snled27351: fix missing `i2c_init()` ([#22446](https://github.com/qmk/qmk_firmware/pull/22446))
* Move BACKLIGHT_PWM_PERIOD to correct docs section ([#22480](https://github.com/qmk/qmk_firmware/pull/22480))
* `qmk find`: Fix failure with multiple filters ([#22497](https://github.com/qmk/qmk_firmware/pull/22497))
* Fix `qmk find` failure due to circular imports ([#22523](https://github.com/qmk/qmk_firmware/pull/22523))
