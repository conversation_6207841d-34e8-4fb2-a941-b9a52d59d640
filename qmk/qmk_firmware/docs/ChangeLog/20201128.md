# QMK Breaking Change - 2020 Nov 28 Changelog

Four times a year QMK runs a process for merging Breaking Changes. A Breaking Change is any change which modifies how QMK behaves in a way that is incompatible or potentially dangerous. We limit these changes to 4 times per year so that users can have confidence that updating their QMK tree will not break their keymaps.


## Changes Requiring User Action {#changes-requiring-user-action}

### Relocated Keyboards {#relocated-keyboards}

#### Reduce Helix keyboard build variation ([#8669](https://github.com/qmk/qmk_firmware/pull/8669))

The build commands for the Helix keyboard are:

```
make <helix_build_name>:<keymap_name>
```

For `<helix_build_name>`, specify the one in the rightmost column of the table below, such as `helix`,` helix/pico`.

| before Oct 17 2019   |  Oct 17 2019            | Mar 10 2020             | Nov 28 2020             |
| ---------------------|-------------------------|-------------------------| ------------------------|
| helix/rev1           | helix/rev1              | helix/rev1              | helix/rev1              |
| helix/pico           | helix/pico              | helix/pico              | helix/pico              |
|                      | helix/pico/back         | helix/pico/back         | helix/pico/back         |
|                      | helix/pico/under        | helix/pico/under        | helix/pico/under        |
|                      |                         | helix/pico/sc           | --                      |
|                      |                         | helix/pico/sc/back      | helix/pico/sc           |
|                      |                         | helix/pico/sc/under     | --                      |
| helix/rev2 (=helix)  | helix/rev2 (=helix)     | helix/rev2 (=helix)     | --                      |
|                      | helix/rev2/back         | helix/rev2/back         | --                      |
|                      | helix/rev2/back/oled    | helix/rev2/back/oled    | ( --> helix/rev2/back)  |
|                      | helix/rev2/oled         | helix/rev2/oled         | helix/rev2　(=helix)     |
|                      | helix/rev2/oled/back    | helix/rev2/oled/back    | helix/rev2/back         |
|                      | helix/rev2/oled/under   | helix/rev2/oled/under   | helix/rev2/under        |
|                      |                         | helix/rev2/sc           | --                      |
|                      |                         | helix/rev2/sc/back      | --                      |
|                      |                         | helix/rev2/sc/oled      | --                      |
|                      |                         | helix/rev2/sc/oledback  | helix/rev2/sc           |
|                      |                         | helix/rev2/sc/oledunder | --                      |
|                      |                         | helix/rev2/sc/under     | --                      |
|                      | helix/rev2/under        | helix/rev2/under        | --                      |
|                      | helix/rev2/under/oled   | helix/rev2/under/oled   | ( --> helix/rev2/under) |

#### Update the Speedo firmware for v3.0 ([#10657](https://github.com/qmk/qmk_firmware/pull/10657))

The Speedo keyboard has moved to `cozykeys/speedo/v2` as the designer prepares to release the Speedo v3.0.

| Previous Name | New Name                   |
| :------------ | :------------------------- |
| speedo        | cozykeys/speedo/v2         |
| --            | cozykeys/speedo/v3 **new** |

#### Maartenwut/Maarten name change to evyd13/Evy ([#10274](https://github.com/qmk/qmk_firmware/pull/10274))

Maartenwut has rebranded as @evyd13, and all released Maartenwut boards have moved.

| Previous Name          | New Name           |
| :--------------------- | :----------------- |
| maartenwut/atom47/rev2 | evyd13/atom47/rev2 |
| maartenwut/atom47/rev3 | evyd13/atom47/rev3 |
| maartenwut/eon40       | evyd13/eon40       |
| maartenwut/eon65       | evyd13/eon65       |
| maartenwut/eon75       | evyd13/eon75       |
| maartenwut/eon87       | evyd13/eon87       |
| maartenwut/eon95       | evyd13/eon95       |
| maartenwut/gh80_1800   | evyd13/gh80_1800   |
| maartenwut/gh80_3700   | evyd13/gh80_3700   |
| maartenwut/minitomic   | evyd13/minitomic   |
| maartenwut/mx5160      | evyd13/mx5160      |
| maartenwut/nt660       | evyd13/nt660       |
| maartenwut/omrontkl    | evyd13/omrontkl    |
| maartenwut/plain60     | evyd13/plain60     |
| maartenwut/pockettype  | evyd13/pockettype  |
| maartenwut/quackfire   | evyd13/quackfire   |
| maartenwut/solheim68   | evyd13/solheim68   |
| maartenwut/ta65        | evyd13/ta65        |
| maartenwut/wasdat      | evyd13/wasdat      |
| maartenwut/wasdat_code | evyd13/wasdat_code |
| maartenwut/wonderland  | evyd13/wonderland  |

#### Xelus Valor and Dawn60 Refactors ([#10512](https://github.com/qmk/qmk_firmware/pull/10512), [#10584](https://github.com/qmk/qmk_firmware/pull/10584))

The Valor and Dawn60 keyboards by Xelus22 both now require their revisions to be specified when compiling.

| Previous Name | New Name          |
| :------------ | :---------------- |
| xelus/dawn60  | xelus/dawn60/rev1 |
| xelus/valor   | xelus/valor/rev1  |


### Updated Keyboard Codebases {#keyboard-updates}

#### AEboards EXT65 Refactor ([#10820](https://github.com/qmk/qmk_firmware/pull/10820))

The EXT65 codebase has been reworked so keymaps can be used with either revision.


## Core Changes {#core-changes}

### Fixes {#core-fixes}

* Reconnect the USB if users wake up a computer from the keyboard to restore the USB state ([#10088](https://github.com/qmk/qmk_firmware/pull/10088))
* Fix cursor position bug in oled_write_raw functions ([#10800](https://github.com/qmk/qmk_firmware/pull/10800))

### Additions and Enhancements {#core-additions}

* Allow MATRIX_ROWS to be greater than 32 ([#10183](https://github.com/qmk/qmk_firmware/pull/10183))
* Add support for soft serial to ATmega32U2 ([#10204](https://github.com/qmk/qmk_firmware/pull/10204))
* Allow direct control of MIDI velocity value ([#9940](https://github.com/qmk/qmk_firmware/pull/9940))
* Joystick 16-bit support ([#10439](https://github.com/qmk/qmk_firmware/pull/10439))
* Allow encoder resolutions to be set per encoder ([#10259](https://github.com/qmk/qmk_firmware/pull/10259))
* Share button state from mousekey to pointing_device ([#10179](https://github.com/qmk/qmk_firmware/pull/10179))
* Add advanced/efficient RGB Matrix Indicators ([#8564](https://github.com/qmk/qmk_firmware/pull/8564))
* OLED display update interval support ([#10388](https://github.com/qmk/qmk_firmware/pull/10388))
* Per-Key Retro Tapping ([#10622](https://github.com/qmk/qmk_firmware/pull/10622))
* Allow backlight duty cycle limit ([#10260](https://github.com/qmk/qmk_firmware/pull/10260))
* Add step sequencer feature ([#9703](https://github.com/qmk/qmk_firmware/pull/9703))
* Added `add_oneshot_mods` & `del_oneshot_mods` ([#10549](https://github.com/qmk/qmk_firmware/pull/10549))
* Add AT90USB support for serial.c ([#10706](https://github.com/qmk/qmk_firmware/pull/10706))
* Auto shift: support repeats and early registration (#9826)

### Clean-ups and Optimizations {#core-optimizations}

* Haptic and solenoid cleanup ([#9700](https://github.com/qmk/qmk_firmware/pull/9700))
* XD75 cleanup ([#10524](https://github.com/qmk/qmk_firmware/pull/10524))
* Minor change to behavior allowing display updates to continue between task ticks ([#10750](https://github.com/qmk/qmk_firmware/pull/10750))
* Change some GPIO manipulations in matrix.c to be atomic ([#10491](https://github.com/qmk/qmk_firmware/pull/10491))
* combine repeated lines of code for ATmega32U2, ATmega16U2, ATmega328 and ATmega328P ([#10837](https://github.com/qmk/qmk_firmware/pull/10837))
* Remove references to HD44780 ([#10735](https://github.com/qmk/qmk_firmware/pull/10735))


## QMK Infrastructure and Internals {#qmk-internals}

* Add ability to build a subset of all keyboards based on platform. ([#10420](https://github.com/qmk/qmk_firmware/pull/10420))
* Initialise EEPROM drivers at startup, instead of upon first execution ([#10438](https://github.com/qmk/qmk_firmware/pull/10438))
* Make bootloader_jump weak for ChibiOS ([#10417](https://github.com/qmk/qmk_firmware/pull/10417))
* Support for STM32 GPIOF,G,H,I,J,K ([#10206](https://github.com/qmk/qmk_firmware/pull/10206))
* Add milc as a dependency and remove the installed milc ([#10563](https://github.com/qmk/qmk_firmware/pull/10563))
* ChibiOS upgrade: early init conversions ([#10214](https://github.com/qmk/qmk_firmware/pull/10214))
* ChibiOS upgrade: configuration file migrator ([#9952](https://github.com/qmk/qmk_firmware/pull/9952))
* Add definition based on currently-selected serial driver. ([#10716](https://github.com/qmk/qmk_firmware/pull/10716))
* Allow for modification of output RGB values when using rgblight/rgb_matrix. ([#10638](https://github.com/qmk/qmk_firmware/pull/10638))
* Allow keyboards/keymaps to execute code at each main loop iteration ([#10530](https://github.com/qmk/qmk_firmware/pull/10530))
* qmk cformat ([#10767](https://github.com/qmk/qmk_firmware/pull/10767))
* Add a Make variable to easily enable DEBUG_MATRIX_SCAN_RATE on the command line ([#10824](https://github.com/qmk/qmk_firmware/pull/10824))
* update Chibios OS USB for the OTG driver ([#8893](https://github.com/qmk/qmk_firmware/pull/8893))
* Fixup version.h writing when using `SKIP_VERSION=yes` ([#10972](https://github.com/qmk/qmk_firmware/pull/10972), [#10974](https://github.com/qmk/qmk_firmware/pull/10974))
* Rename ledmatrix.h to match .c file ([#7949](https://github.com/qmk/qmk_firmware/pull/7949))
* Split RGB_MATRIX_ENABLE into _ENABLE and _DRIVER ([#10231](https://github.com/qmk/qmk_firmware/pull/10231))
* Split LED_MATRIX_ENABLE into _ENABLE and _DRIVER ([#10840](https://github.com/qmk/qmk_firmware/pull/10840))
