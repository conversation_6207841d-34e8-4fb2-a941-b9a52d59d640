# QMK Breaking Changes - 2024 August 25 Changelog

## Notable Features

August 2024 brings another maintenance release of QMK. Almost all PRs this cycle were to do with cleanup or re-organization of existing features and code.

One key item is that there are no longer any `via`-enabled keymaps in the QMK repository -- these have all transitioned to an [External Userspace](../newbs_external_userspace) repository under the VIA team's control. Going forward, all PRs to `qmk_firmware` must not contain `via` keymaps; these should instead be redirected to the [VIA QMK Userspace](https://github.com/the-via/qmk_userspace_via) repository.

## Changes Requiring User Action

### Updated Keyboard Codebases

One note with updated keyboard names -- historical keyboard names are still considered valid when using [External Userspace](../newbs_external_userspace) for builds. If you're already using External Userspace, you do not need to move your keymap inside your repository.

| Old Keyboard Name                | New Keyboard Name             |
|----------------------------------|-------------------------------|
| dnworks/9973                     | dnworks/tkl87                 |
| maple_computing/lets_split_eh/eh | maple_computing/lets_split_eh |
| planck/ez/base                   | zsa/planck_ez/base            |

### SparkFun Pro Micro RP2040 converter renamed ([#24192](https://github.com/qmk/qmk_firmware/pull/24192))

The converter for the SparkFun Pro Micro RP2040 has been renamed.

| Old               | New               |
|-------------------|-------------------|
| `promicro_rp2040` | `sparkfun_rp2040` |

This change was made to avoid confusion between the clones named ProMicro RP2040 and the SparkFun Pro Micro RP2040. The clones should be using the `rp2040_ce` option.

### Key Override `keymap.c` signature change ([#24120](https://github.com/qmk/qmk_firmware/pull/24120))

Changes were made to key overrides in order to hook them into the keymap introspection system.

If you're using key overrides, your `keymap.c` will need a change to the signature of your list of key overrides, as well as removing the `NULL` terminator.

For example, you'll need to change existing code from (note the highlighted lines):

```c{1,4-5}
const key_override_t **key_overrides = (const key_override_t *[]){
    &my_override_1,
    &my_override_2,
    NULL
};
```

to:

```c{1,4}
const key_override_t *key_overrides[] = {
    &my_override_1,
    &my_override_2,
};
```

### ADNS9800 and PMW33xx firmware upload now opt-in ([#24001](https://github.com/qmk/qmk_firmware/pull/24001))

Due to ambiguity with licensing compatibility, QMK has made the firmware ROM uploads for the ADNS9800 and PMW33xx lines of pointing device sensors temporarily opt-in with the view to removing them. Historically they were included by default, but as of this PR this is now no longer the case.

Please get in touch with the QMK team if your sensor no longer functions without the firmware upload -- so far we've tested each device type and they still seem to function without a new firmware, but this has not been a 100% exhaustive validation.

To re-enable firmware upload for your own builds, add the following to your keymap's `config.h`:

| Sensor   | Define                         |
|----------|--------------------------------|
| ADNS9800 | `#define ADNS9800_UPLOAD_SROM` |
| PMW33xx  | `#define PMW33XX_UPLOAD_SROM`  |

:::info Note
If no issues arise during this current breaking changes cycle, these sensor firmware ROMs will be removed from QMK entirely.
:::

## Deprecation Notices

In line with the [notice period](../support_deprecation_policy#how-much-advance-notice-will-be-given), deprecation notices for larger items are listed here.

### Migration of VIA keymaps to VIA team control

Last breaking changes cycle, the QMK team informed that [`via` keymaps are moving](./20240526#migration-of-via-keymaps-to-via-team-control) to the control of the VIA team. As of this merge, any `via`-enabled keymaps should now be PR'ed to the [VIA QMK Userspace](https://github.com/the-via/qmk_userspace_via) repository.

Any existing or newly-opened PRs against `qmk_firmware` will now be asked to remove any `via`-enabled keymaps from their submissions.

### ADNS9800 and PMW33xx sensor firmware ROM removal

As mentioned above, there's license compatibility issues between QMK and the firmware blobs historically uploaded for the ADNS9800 and PMW33xx sensors. This notice is to inform users that these firmware blobs will almost certainly be removed from QMK in the next breaking changes merge.

As previously noted this does not seem to have any detrimental effect to any of those devices, as they seem to include firmware on-chip without requiring one to be uploaded. Please get in touch with the QMK team on Discord if your sensor is no longer functional.

## Full changelist

Core:
* EEPROM: Don't erase if we don't have to. Adding eeprom_driver_format abstraction. ([#18332](https://github.com/qmk/qmk_firmware/pull/18332))
* Generate keymap dd keycodes to header ([#20273](https://github.com/qmk/qmk_firmware/pull/20273))
* [wear_leveling] efl updates ([#22489](https://github.com/qmk/qmk_firmware/pull/22489))
* GC9xxx LCD family drivers (GC9107 and GC9A01) ([#23091](https://github.com/qmk/qmk_firmware/pull/23091))
* [build_test] set CONSOLE_ENABLE=yes if DEBUG > 0 ([#23097](https://github.com/qmk/qmk_firmware/pull/23097))
* Enhance overlapping mouse keys control ([#23341](https://github.com/qmk/qmk_firmware/pull/23341))
* APA102: API rework ([#23355](https://github.com/qmk/qmk_firmware/pull/23355))
* [WIP] Adds bus acquire/release semantics to SPI start/stop. ([#23439](https://github.com/qmk/qmk_firmware/pull/23439))
* add farsi keymap extras ([#23650](https://github.com/qmk/qmk_firmware/pull/23650))
* Update atomic GPIO macros in keyboard custom matrix ([#23796](https://github.com/qmk/qmk_firmware/pull/23796))
* Check LED flags for Pixel Fractal effect ([#23881](https://github.com/qmk/qmk_firmware/pull/23881))
* Add STM32F405RG ld script for tinyuf2 ([#23885](https://github.com/qmk/qmk_firmware/pull/23885))
* SPI flash API cleanup, add async erase capability. ([#23894](https://github.com/qmk/qmk_firmware/pull/23894))
* Add process_keycode handlers for new RGB Matrix and Underglow keycodes ([#23896](https://github.com/qmk/qmk_firmware/pull/23896))
* Decouple VIA from STM32 L0/L1 EEPROM implementation ([#23901](https://github.com/qmk/qmk_firmware/pull/23901))
* Normalise mouse keycodes ([#23975](https://github.com/qmk/qmk_firmware/pull/23975))
* Remove deprecated `led_set_user` ([#23979](https://github.com/qmk/qmk_firmware/pull/23979))
* Change ADNS9800 and PMW33XX SROM uploads to opt in. ([#24001](https://github.com/qmk/qmk_firmware/pull/24001))
* Rename encoder pins defines ([#24003](https://github.com/qmk/qmk_firmware/pull/24003))
* Change suspend condition check order on ChibiOS ([#24020](https://github.com/qmk/qmk_firmware/pull/24020))
* Tap dance introspection ([#24049](https://github.com/qmk/qmk_firmware/pull/24049))
* Allow overriding `get_hardware_id()`. ([#24051](https://github.com/qmk/qmk_firmware/pull/24051))
* Align LUFA suspend logic ([#24055](https://github.com/qmk/qmk_firmware/pull/24055))
* Add support for key override introspection. ([#24120](https://github.com/qmk/qmk_firmware/pull/24120))
* Dynamic macro callbacks ([#24142](https://github.com/qmk/qmk_firmware/pull/24142))
* Rename promicro_rp2040 converter to sparkfun_pm2040 ([#24192](https://github.com/qmk/qmk_firmware/pull/24192))
* Refactor starlight RGB matrix effects ([#24202](https://github.com/qmk/qmk_firmware/pull/24202))
* Refactor riverflow RGB matrix loop ([#24203](https://github.com/qmk/qmk_firmware/pull/24203))

CLI:
* Remove handling of keyboard level keymap templates ([#24234](https://github.com/qmk/qmk_firmware/pull/24234))
* Small tweaks to keymap generation ([#24240](https://github.com/qmk/qmk_firmware/pull/24240))

Keyboards:
* refactor: keyboard/ncr80/r2 ([#22670](https://github.com/qmk/qmk_firmware/pull/22670))
* Implement data driven joysticks ([#22947](https://github.com/qmk/qmk_firmware/pull/22947))
* Whitefacemountain Ampersand ([#23437](https://github.com/qmk/qmk_firmware/pull/23437))
* Add TRKeyboard TRK1 keyboard ([#23642](https://github.com/qmk/qmk_firmware/pull/23642))
* Rename dnworks/9973 to dnworks/tkl87 ([#23692](https://github.com/qmk/qmk_firmware/pull/23692))
* Update Underglow keycodes ([#23765](https://github.com/qmk/qmk_firmware/pull/23765))
* Add boardsource/the_q ([#23782](https://github.com/qmk/qmk_firmware/pull/23782))
* BastardKB: remove legacy board `tbk` ([#23818](https://github.com/qmk/qmk_firmware/pull/23818))
* Update ZSA Moonlander ([#23911](https://github.com/qmk/qmk_firmware/pull/23911))
* Move Planck EZ to ZSA vendor folder ([#23917](https://github.com/qmk/qmk_firmware/pull/23917))
* Migrate SPLIT_HAND_PIN to json ([#23924](https://github.com/qmk/qmk_firmware/pull/23924))
* Migrate SERIAL_DRIVER to json ([#23925](https://github.com/qmk/qmk_firmware/pull/23925))
* Migrate RGB Matrix layout for two boards ([#23963](https://github.com/qmk/qmk_firmware/pull/23963))
* Migrate `led_update_kb` implementations to DD ([#23980](https://github.com/qmk/qmk_firmware/pull/23980))
* Migrate `led_update_kb` implementations to DD ([#23981](https://github.com/qmk/qmk_firmware/pull/23981))
* Migrate `led_update_kb` implementations to DD ([#23983](https://github.com/qmk/qmk_firmware/pull/23983))
* Migrate `led_update_kb` implementations to DD ([#23985](https://github.com/qmk/qmk_firmware/pull/23985))
* Relocate m256wh VIA logic ([#24006](https://github.com/qmk/qmk_firmware/pull/24006))
* Relocate winry315 VIA logic ([#24008](https://github.com/qmk/qmk_firmware/pull/24008))
* Relocate m256ws VIA logic ([#24009](https://github.com/qmk/qmk_firmware/pull/24009))
* `atreus`: misc cleanups ([#24010](https://github.com/qmk/qmk_firmware/pull/24010))
* Relocate work_louder VIA logic ([#24011](https://github.com/qmk/qmk_firmware/pull/24011))
* Relocate xelus/pachi/rgb/rev2 VIA logic ([#24016](https://github.com/qmk/qmk_firmware/pull/24016))
* Remove custom keycodes from nullbitsco/snap ([#24017](https://github.com/qmk/qmk_firmware/pull/24017))
* added bear_face/v3 ([#24032](https://github.com/qmk/qmk_firmware/pull/24032))
* Remove DEFAULT_FOLDER from maple_computing/lets_split_eh ([#24054](https://github.com/qmk/qmk_firmware/pull/24054))
* refactor bear_face/v1, v2 ([#24060](https://github.com/qmk/qmk_firmware/pull/24060))
* Convert `eeconfig_init_kb` implementations to config ([#24087](https://github.com/qmk/qmk_firmware/pull/24087))
* Remove broken keymap from keebio/iris ([#24094](https://github.com/qmk/qmk_firmware/pull/24094))
* Move LED Matrix LED config to data driven ([#24122](https://github.com/qmk/qmk_firmware/pull/24122))
* Move split.soft_serial_pin to split.serial.pin ([#24127](https://github.com/qmk/qmk_firmware/pull/24127))
* Remove pointless `RGB_MATRIX_LED_COUNT`s ([#24133](https://github.com/qmk/qmk_firmware/pull/24133))
* `hs60/v1`: separate into ANSI and ISO revisions ([#24136](https://github.com/qmk/qmk_firmware/pull/24136))
* Migrate half-duplex `SERIAL_USART_TX_PIN` to DD ([#24143](https://github.com/qmk/qmk_firmware/pull/24143))
* Migrate split.soft_serial_pin to split.serial.pin O-Z ([#24146](https://github.com/qmk/qmk_firmware/pull/24146))
* Migrate split.soft_serial_pin to split.serial.pin 0-H ([#24155](https://github.com/qmk/qmk_firmware/pull/24155))
* Remove instances of MASTER_LEFT. ([#24163](https://github.com/qmk/qmk_firmware/pull/24163))
* Rename EC Type-K ([#24180](https://github.com/qmk/qmk_firmware/pull/24180))
* Migrate split.soft_serial_pin to split.serial.pin H-O ([#24185](https://github.com/qmk/qmk_firmware/pull/24185))
* Remove split.transport.protocol=serial ([#24191](https://github.com/qmk/qmk_firmware/pull/24191))
* Refactor use of `matrix_scan_kb` ([#24200](https://github.com/qmk/qmk_firmware/pull/24200))
* Eliminate use of `#include "../default/keymap.c"`. ([#24215](https://github.com/qmk/qmk_firmware/pull/24215))
* Remove keyboard level `QK_BOOT` implementations ([#24231](https://github.com/qmk/qmk_firmware/pull/24231))
* Remove `handwired/pytest/has_template` ([#24232](https://github.com/qmk/qmk_firmware/pull/24232))
* Refactor opendeck/32 ([#24233](https://github.com/qmk/qmk_firmware/pull/24233))
* Refactor printedpad ([#24236](https://github.com/qmk/qmk_firmware/pull/24236))
* Refactor orthocode ([#24237](https://github.com/qmk/qmk_firmware/pull/24237))
* Remove unnecessary RGB Matrix shutdown hooks ([#24238](https://github.com/qmk/qmk_firmware/pull/24238))
* Remove all via-enabled keymaps, including `via`. ([#24322](https://github.com/qmk/qmk_firmware/pull/24322))

Keyboard fixes:
* Fix dogtag/info.json ([#23520](https://github.com/qmk/qmk_firmware/pull/23520))
* splitkb/kyria: remove `CONVERT_TO` at keyboard level ([#23857](https://github.com/qmk/qmk_firmware/pull/23857))
* Fixup mt/mt84 ([#23883](https://github.com/qmk/qmk_firmware/pull/23883))
* Fix for encoders and support ENCODER_MAP_ENABLE on Planck rev7 ([#23967](https://github.com/qmk/qmk_firmware/pull/23967))
* `handwired/swiftrax/bumblebee`: fix layout name ([#24064](https://github.com/qmk/qmk_firmware/pull/24064))
* Fixup boardsource/the_q RGB matrix coordinates ([#24086](https://github.com/qmk/qmk_firmware/pull/24086))
* Various fixes for keyboards not implementing callbacks correctly ([#24092](https://github.com/qmk/qmk_firmware/pull/24092))
* Various fixes for keyboards not implementing callbacks correctly ([#24116](https://github.com/qmk/qmk_firmware/pull/24116))
* Remove duplicate calls to `housekeeping_task_user` ([#24201](https://github.com/qmk/qmk_firmware/pull/24201))
* Fixup `handwired/dactyl_minidox` ([#24253](https://github.com/qmk/qmk_firmware/pull/24253))
* Fix build failure on zsa/moonlander with DYNAMIC_MACRO_ENABLE ([#24316](https://github.com/qmk/qmk_firmware/pull/24316))

Others:
* LED drivers: extract documentation from LED/RGB Matrix pages ([#23630](https://github.com/qmk/qmk_firmware/pull/23630))
* Implement data driven serial driver ([#23923](https://github.com/qmk/qmk_firmware/pull/23923))
* Remove skipped schema files ([#23987](https://github.com/qmk/qmk_firmware/pull/23987))
* Update RGBLight (Underglow) keycode names ([#23999](https://github.com/qmk/qmk_firmware/pull/23999))

Bugs:
* Fix NKRO and Mouse Emulation on arm_atsam ([#23945](https://github.com/qmk/qmk_firmware/pull/23945))
* Force `dump_lines()` to always use Unix line endings ([#23954](https://github.com/qmk/qmk_firmware/pull/23954))
* Fixup home link. ([#24068](https://github.com/qmk/qmk_firmware/pull/24068))
