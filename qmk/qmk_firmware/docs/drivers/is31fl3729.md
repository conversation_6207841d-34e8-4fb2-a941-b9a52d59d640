# IS31FL3729 Driver {#is31fl3729-driver}

I²C 16x8/15x9 LED matrix driver by <PERSON><PERSON><PERSON><PERSON>. Supports a maximum of four drivers, each controlling up to 135 single-color LEDs, or 45 RGB LEDs.

[IS31FL3729 Datasheet](https://www.lumissil.com/assets/pdf/core/IS31FL3729_DS.pdf)

## Usage {#usage}

The IS31FL3729 driver code is automatically included if you are using the [LED Matrix](../features/led_matrix) or [RGB Matrix](../features/rgb_matrix) feature with the `is31fl3729` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add this to your `rules.mk`:

```make
COMMON_VPATH += $(DRIVER_PATH)/led/issi
SRC += is31fl3729-mono.c # For single-color
SRC += is31fl3729.c # For RGB
I2C_DRIVER_REQUIRED = yes
```

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                      |Default                               |Description                                         |
|----------------------------|--------------------------------------|----------------------------------------------------|
|`IS31FL3729_SDB_PIN`        |*Not defined*                         |The GPIO pin connected to the drivers' shutdown pins|
|`IS31FL3729_I2C_TIMEOUT`    |`100`                                 |The I²C timeout in milliseconds                     |
|`IS31FL3729_I2C_PERSISTENCE`|`0`                                   |The number of times to retry I²C transmissions      |
|`IS31FL3729_I2C_ADDRESS_1`  |*Not defined*                         |The I²C address of driver 0                         |
|`IS31FL3729_I2C_ADDRESS_2`  |*Not defined*                         |The I²C address of driver 1                         |
|`IS31FL3729_I2C_ADDRESS_3`  |*Not defined*                         |The I²C address of driver 2                         |
|`IS31FL3729_I2C_ADDRESS_4`  |*Not defined*                         |The I²C address of driver 3                         |
|`IS31FL3729_PWM_FREQUENCY`  |`IS31FL3729_PWM_FREQUENCY_32K_HZ`     |The PWM frequency of the LEDs                       |
|`IS31FL3729_SW_PULLDOWN`    |`IS31FL3729_SW_PULLDOWN_2K_OHM_SW_OFF`|The `SWx` pullup resistor value                     |
|`IS31FL3729_CS_PULLUP`      |`IS31FL3729_CS_PULLUP_2K_OHM_CS_OFF`  |The `CSx` pulldown resistor value                   |
|`IS31FL3729_GLOBAL_CURRENT` |`0x40`                                |The global current control value                    |

### I²C Addressing {#i2c-addressing}

The IS31FL3729 has four possible 7-bit I²C addresses, depending on how the `AD` pin is connected.

To configure this, set the `IS31FL3729_I2C_ADDRESS_n` defines to one of the following in your `config.h`, where *n* denotes the driver index:

|Define                      |Value |
|----------------------------|------|
|`IS31FL3729_I2C_ADDRESS_GND`|`0x34`|
|`IS31FL3729_I2C_ADDRESS_SCL`|`0x35`|
|`IS31FL3729_I2C_ADDRESS_SDA`|`0x36`|
|`IS31FL3729_I2C_ADDRESS_VCC`|`0x37`|

### PWM Frequency {#pwm-frequency}

The PWM frequency can be adjusted by adding the following to your `config.h`:

```c
#define IS31FL3729_PWM_FREQUENCY IS31FL3729_PWM_FREQUENCY_32K_HZ
```

Valid values are:

|Define                            |Frequency       |
|----------------------------------|----------------|
|`IS31FL3729_PWM_FREQUENCY_55K_HZ` |55 kHz          |
|`IS31FL3729_PWM_FREQUENCY_32K_HZ` |32 kHz (default)|
|`IS31FL3729_PWM_FREQUENCY_4K_HZ`  |4 kHz           |
|`IS31FL3729_PWM_FREQUENCY_2K_HZ`  |2 kHz           |
|`IS31FL3729_PWM_FREQUENCY_1K_HZ`  |1 kHz           |
|`IS31FL3729_PWM_FREQUENCY_500_HZ` |500 Hz          |
|`IS31FL3729_PWM_FREQUENCY_250_HZ` |250 Hz          |
|`IS31FL3729_PWM_FREQUENCY_80K_HZ` |80 kHz          |

### De-Ghosting {#de-ghosting}

These settings control the pulldown and pullup resistor values on the `SWy` and `CSx` pins respectively, for the purposes of eliminating ghosting. Refer to the datasheet (p. 18) for more information on how and why this occurs.

To adjust the resistor values, add the following to your `config.h`:

```c
#define IS31FL3729_SW_PULLDOWN IS31FL3729_SW_PULLDOWN_2K_OHM_SW_OFF
#define IS31FL3729_CS_PULLUP IS31FL3729_CS_PULLUP_2K_OHM_CS_OFF
```

Valid values for `IS31FL3729_SW_PULLDOWN` are:

|Define                                 |Resistance                    |
|---------------------------------------|------------------------------|
|`IS31FL3729_SW_PULLDOWN_0_OHM`         |None                          |
|`IS31FL3729_SW_PULLDOWN_0K5_OHM_SW_OFF`|0.5 kΩ in SWy off time        |
|`IS31FL3729_SW_PULLDOWN_1K_OHM_SW_OFF` |1 kΩ in SWy off time          |
|`IS31FL3729_SW_PULLDOWN_2K_OHM_SW_OFF` |2 kΩ in SWy off time (default)|
|`IS31FL3729_SW_PULLDOWN_1K_OHM`        |1 kΩ                          |
|`IS31FL3729_SW_PULLDOWN_2K_OHM`        |2 kΩ                          |
|`IS31FL3729_SW_PULLDOWN_4K_OHM`        |4 kΩ                          |
|`IS31FL3729_SW_PULLDOWN_8K_OHM`        |8 kΩ                          |

Valid values for `IS31FL3729_CS_PULLUP` are:

|Define                               |Resistance                    |
|-------------------------------------|------------------------------|
|`IS31FL3729_CS_PULLUP_0_OHM`         |None                          |
|`IS31FL3729_CS_PULLUP_0K5_OHM_CS_OFF`|0.5 kΩ in CSx off time        |
|`IS31FL3729_CS_PULLUP_1K_OHM_CS_OFF` |1 kΩ in CSx off time          |
|`IS31FL3729_CS_PULLUP_2K_OHM_CS_OFF` |2 kΩ in CSx off time (default)|
|`IS31FL3729_CS_PULLUP_1K_OHM`        |1 kΩ                          |
|`IS31FL3729_CS_PULLUP_2K_OHM`        |2 kΩ                          |
|`IS31FL3729_CS_PULLUP_4K_OHM`        |4 kΩ                          |
|`IS31FL3729_CS_PULLUP_8K_OHM`        |8 kΩ                          |

### Global Current Control {#global-current-control}

This setting controls the current sunk by the `CSx` pins, from 0 to 255. By default, the value is 64, but if you need to adjust it, add the following to your `config.h`:

```c
#define IS31FL3729_GLOBAL_CURRENT 0x40
```

## ARM/ChibiOS Configuration {#arm-configuration}

Depending on the ChibiOS board configuration, you may need to [enable and configure I²C](i2c#arm-configuration) at the keyboard level.

## LED Mapping {#led-mapping}

In order to use this driver, each output must be mapped to an LED index, by adding the following to your `<keyboardname>.c`:

```c
const is31fl3729_led_t PROGMEM g_is31fl3729_leds[IS31FL3729_LED_COUNT] = {
/* Driver
 *   |  R        G        B */
    {0, SW1_CS1, SW1_CS2, SW1_CS3},
    // etc...
};
```

In this example, the red, green and blue channels for the first LED index on driver 0 all have their anodes connected to the `SW1` pin, and their cathodes on the `CS1`, `CS2` and `CS3` pins respectively.

For the single-color driver, the principle is the same, but there is only one channel:

```c
const is31fl3729_led_t PROGMEM g_is31fl3729_leds[IS31FL3729_LED_COUNT] = {
/* Driver
 *   |  V */
    {0, SW1_CS1},
    // etc...
};
```

These values correspond to the register indices as shown in the datasheet on page 12, figure 9.

## API {#api}

### `struct is31fl3729_led_t` {#api-is31fl3729-led-t}

Contains the PWM register addresses for a single RGB LED.

#### Members {#api-is31fl3729-led-t-members}

 - `uint8_t driver`  
   The driver index of the LED, from 0 to 3.
 - `uint8_t r`  
   The output PWM register address for the LED's red channel (RGB driver only).
 - `uint8_t g`  
   The output PWM register address for the LED's green channel (RGB driver only).
 - `uint8_t b`  
   The output PWM register address for the LED's blue channel (RGB driver only).
 - `uint8_t v`  
   The output PWM register address for the LED (single-color driver only).

---

### `void is31fl3729_init(uint8_t index)` {#api-is31fl3729-init}

Initialize the LED driver. This function should be called first.

#### Arguments {#api-is31fl3729-init-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void is31fl3729_write_register(uint8_t index, uint8_t reg, uint8_t data)` {#api-is31fl3729-write-register}

Set the value of the given register.

#### Arguments {#api-is31fl3729-write-register-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t reg`  
   The register address.
 - `uint8_t data`  
   The value to set.

---

### `void is31fl3729_set_color(int index, uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3729-set-color}

Set the color of a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3729_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3729-set-color-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3729_leds` array).
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3729_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3729-set-color-all}

Set the color of all LEDs (RGB driver only).

#### Arguments {#api-is31fl3729-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3729_set_value(int index, uint8_t value)` {#api-is31fl3729-set-value}

Set the brightness of a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3729_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3729-set-value-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3729_leds` array).
 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3729_set_value_all(uint8_t value)` {#api-is31fl3729-set-value-all}

Set the brightness of all LEDs (single-color driver only).

#### Arguments {#api-is31fl3729-set-value-all-arguments}

 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3729_set_scaling_register(uint8_t index, uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3729-set-scaling-register-rgb}

Configure the scaling registers for a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3729_update_scaling_registers()` after you are finished.

#### Arguments {#api-is31fl3729-set-scaling-register-rgb-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3729_leds` array).
 - `uint8_t red`  
   The scaling value for the red channel.
 - `uint8_t green`  
   The scaling value for the green channel.
 - `uint8_t blue`  
   The scaling value for the blue channel.

---

### `void is31fl3729_set_scaling_register(uint8_t index, uint8_t value)` {#api-is31fl3729-set-scaling-register-mono}

Configure the scaling registers for a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3729_update_scaling_registers()` after you are finished.

#### Arguments {#api-is31fl3729-set-scaling-register-mono-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3729_leds` array).
 - `uint8_t value`  
   The scaling value for the LED.

---

### `void is31fl3729_update_pwm_buffers(uint8_t index)` {#api-is31fl3729-update-pwm-buffers}

Flush the PWM values to the LED driver.

#### Arguments {#api-is31fl3729-update-pwm-buffers-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void is31fl3729_update_scaling_registers(uint8_t index)` {#api-is31fl3729-update-scaling-registers}

Flush the scaling register values to the LED driver.

#### Arguments {#api-is31fl3729-update-scaling-registers-arguments}

 - `uint8_t index`  
   The driver index.
