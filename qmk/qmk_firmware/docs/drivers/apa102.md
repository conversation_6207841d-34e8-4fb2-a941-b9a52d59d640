# APA102 Driver {#apa102-driver}

This driver provides support for APA102 addressable RGB LEDs. They are similar to the [WS2812](ws2812) LEDs, but have increased data and refresh rates.

## Usage {#usage}

In most cases, the APA102 driver code is automatically included if you are using either the [RGBLight](../features/rgblight) or [RGB Matrix](../features/rgb_matrix) feature with the `apa102` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add the following to your `rules.mk`:

```make
APA102_DRIVER_REQUIRED = yes
```

You can then call the APA102 API by including `apa102.h` in your code.

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                     |Default      |Description                                                       |
|---------------------------|-------------|------------------------------------------------------------------|
|`APA102_DI_PIN`            |*Not defined*|The GPIO pin connected to the DI pin of the first LED in the chain|
|`APA102_CI_PIN`            |*Not defined*|The GPIO pin connected to the CI pin of the first LED in the chain|
|`APA102_DEFAULT_BRIGHTNESS`|`31`         |The default global brightness level of the LEDs, from 0 to 31     |

## API {#api}

### `void apa102_init(void)` {#api-apa102-init}

Initialize the LED driver. This function should be called first.

---

### `void apa102_set_color(uint16_t index, uint8_t red, uint8_t green, uint8_t blue)` {#api-apa102-set-color}

Set the color of a single LED. This function does not immediately update the LEDs; call `apa102_flush()` after you are finished.

#### Arguments {#api-apa102-set-color-arguments}

 - `uint16_t index`  
   The LED index in the APA102 chain.
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void apa102_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-apa102-set-color-all}

Set the color of all LEDs.

#### Arguments {#api-apa102-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void apa102_flush(void)` {#api-apa102-flush}

Flush the PWM values to the LED chain.

---

### `void apa102_set_brightness(uint8_t brightness)` {#api-apa102-set-brightness}

Set the global brightness.

#### Arguments {#api-apa102-set-brightness-arguments}

 - `uint8_t brightness`  
   The brightness level to set, from 0 to 31.
