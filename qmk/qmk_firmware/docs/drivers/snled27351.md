# SNLED27351 Driver {#snled27351-driver}

I²C 16x12 LED matrix driver by Sonix. Supports a maximum of four drivers, each controlling up to 192 single-color LEDs, or 64 RGB LEDs.

A slightly modified version of this IC is also known as "CKLED2001".

[SNLED27351 Datasheet](https://www.sonix.com.tw/files/1/D235860C0C037C28E050007F01001CBE)

## Usage {#usage}

The SNLED27351 driver code is automatically included if you are using the [LED Matrix](../features/led_matrix) or [RGB Matrix](../features/rgb_matrix) feature with the `snled27351` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add this to your `rules.mk`:

```make
COMMON_VPATH += $(DRIVER_PATH)/led
SRC += snled27351-mono.c # For single-color
SRC += snled27351.c # For RGB
I2C_DRIVER_REQUIRED = yes
```

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                      |Default      |Description                                         |
|----------------------------|-------------|----------------------------------------------------|
|`SNLED27351_SDB_PIN`        |*Not defined*|The GPIO pin connected to the drivers' shutdown pins|
|`SNLED27351_I2C_TIMEOUT`    |`100`        |The I²C timeout in milliseconds                     |
|`SNLED27351_I2C_PERSISTENCE`|`0`          |The number of times to retry I²C transmissions      |
|`SNLED27351_I2C_ADDRESS_1`  |*Not defined*|The I²C address of driver 0                         |
|`SNLED27351_I2C_ADDRESS_2`  |*Not defined*|The I²C address of driver 1                         |
|`SNLED27351_I2C_ADDRESS_3`  |*Not defined*|The I²C address of driver 2                         |
|`SNLED27351_I2C_ADDRESS_4`  |*Not defined*|The I²C address of driver 3                         |

### I²C Addressing {#i2c-addressing}

The SNLED27351 has four possible 7-bit I²C addresses, depending on how the `ADDR` pin is connected.

To configure this, set the `SNLED27351_I2C_ADDRESS_n` defines to one of the following in your `config.h`, where *n* denotes the driver index:

|Define                        |Value |
|------------------------------|------|
|`SNLED27351_I2C_ADDRESS_GND`  |`0x74`|
|`SNLED27351_I2C_ADDRESS_SCL`  |`0x75`|
|`SNLED27351_I2C_ADDRESS_SDA`  |`0x76`|
|`SNLED27351_I2C_ADDRESS_VDDIO`|`0x77`|

## ARM/ChibiOS Configuration {#arm-configuration}

Depending on the ChibiOS board configuration, you may need to [enable and configure I²C](i2c#arm-configuration) at the keyboard level.

## LED Mapping {#led-mapping}

In order to use this driver, each output must be mapped to an LED index, by adding the following to your `<keyboardname>.c`:

```c
const snled27351_led_t PROGMEM g_snled27351_leds[SNLED27351_LED_COUNT] = {
/* Driver
 *   |  R         G         B */
    {0, CB1_CA1,  CB1_CA2,  CB1_CA3},
    // etc...
};
```

In this example, the red, green and blue channels for the first LED index on driver 0 all have their cathodes connected to the `CB1` pin, and their anodes on the `CA1`, `CA2` and `CA3` pins respectively.

For the single-color driver, the principle is the same, but there is only one channel:

```c
const snled27351_led_t PROGMEM g_snled27351_leds[SNLED27351_LED_COUNT] = {
/* Driver
 *   |  V */
    {0, CB1_CA1},
    // etc...
};
```

These values correspond to the register indices as shown in the datasheet on page 13.

## API {#api}

### `struct snled27351_led_t` {#api-snled27351-led-t}

Contains the PWM register addresses for a single RGB LED.

#### Members {#api-snled27351-led-t-members}

 - `uint8_t driver`  
   The driver index of the LED, from 0 to 3.
 - `uint8_t r`  
   The output PWM register address for the LED's red channel (RGB driver only).
 - `uint8_t g`  
   The output PWM register address for the LED's green channel (RGB driver only).
 - `uint8_t b`  
   The output PWM register address for the LED's blue channel (RGB driver only).
 - `uint8_t v`  
   The output PWM register address for the LED (single-color driver only).

---

### `void snled27351_init(uint8_t index)` {#api-snled27351-init}

Initialize the LED driver. This function should be called first.

#### Arguments {#api-snled27351-init-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void snled27351_write_register(uint8_t index, uint8_t reg, uint8_t data)` {#api-snled27351-write-register}

Set the value of the given register.

#### Arguments {#api-snled27351-write-register-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t reg`  
   The register address.
 - `uint8_t data`  
   The value to set.

---

### `void snled27351_select_page(uint8_t index, uint8_t page)` {#api-snled27351-select-page}

Change the current page for configuring the LED driver.

#### Arguments {#api-snled27351-select-page-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t page`  
   The page number to select.

---

### `void snled27351_set_color(int index, uint8_t red, uint8_t green, uint8_t blue)` {#api-snled27351-set-color}

Set the color of a single LED (RGB driver only). This function does not immediately update the LEDs; call `snled27351_update_pwm_buffers()` after you are finished.

#### Arguments {#api-snled27351-set-color-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_snled27351_leds` array).
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void snled27351_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-snled27351-set-color-all}

Set the color of all LEDs (RGB driver only).

#### Arguments {#api-snled27351-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void snled27351_set_value(int index, uint8_t value)` {#api-snled27351-set-value}

Set the brightness of a single LED (single-color driver only). This function does not immediately update the LEDs; call `snled27351_update_pwm_buffers()` after you are finished.

#### Arguments {#api-snled27351-set-value-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_snled27351_leds` array).
 - `uint8_t value`  
   The brightness value to set.

---

### `void snled27351_set_value_all(uint8_t value)` {#api-snled27351-set-value-all}

Set the brightness of all LEDs (single-color driver only).

#### Arguments {#api-snled27351-set-value-all-arguments}

 - `uint8_t value`  
   The brightness value to set.

---

### `void snled27351_set_led_control_register(uint8_t index, bool red, bool green, bool blue)` {#api-snled27351-set-led-control-register-rgb}

Configure the LED control registers for a single LED (RGB driver only). This function does not immediately update the LEDs; call `snled27351_update_led_control_registers()` after you are finished.

#### Arguments {#api-snled27351-set-led-control-register-rgb-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_snled27351_leds` array).
 - `bool red`  
   Enable or disable the red channel.
 - `bool green`  
   Enable or disable the green channel.
 - `bool blue`  
   Enable or disable the blue channel.

---

### `void snled27351_set_led_control_register(uint8_t index, bool value)` {#api-snled27351-set-led-control-register-mono}

Configure the LED control registers for a single LED (single-color driver only). This function does not immediately update the LEDs; call `snled27351_update_led_control_registers()` after you are finished.

#### Arguments {#api-snled27351-set-led-control-register-mono-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_snled27351_leds` array).
 - `bool value`  
   Enable or disable the LED.

---

### `void snled27351_update_pwm_buffers(uint8_t index)` {#api-snled27351-update-pwm-buffers}

Flush the PWM values to the LED driver.

#### Arguments {#api-snled27351-update-pwm-buffers-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void snled27351_update_led_control_registers(uint8_t index)` {#api-snled27351-update-led-control-registers}

Flush the LED control register values to the LED driver.

#### Arguments {#api-snled27351-update-led-control-registers-arguments}

 - `uint8_t index`  
   The driver index.
