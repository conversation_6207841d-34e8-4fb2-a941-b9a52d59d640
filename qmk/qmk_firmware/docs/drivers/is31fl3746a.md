# IS31FL3746A Driver {#is31fl3746a-driver}

I²C 18x4 LED matrix driver by <PERSON><PERSON><PERSON><PERSON>. Supports a maximum of four drivers, each controlling up to 72 single-color LEDs, or 24 RGB LEDs.

[IS31FL3746A Datasheet](https://www.lumissil.com/assets/pdf/core/IS31FL3746A_DS.pdf)

## Usage {#usage}

The IS31FL3746A driver code is automatically included if you are using the [LED Matrix](../features/led_matrix) or [RGB Matrix](../features/rgb_matrix) feature with the `is31fl3746a` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add this to your `rules.mk`:

```make
COMMON_VPATH += $(DRIVER_PATH)/led/issi
SRC += is31fl3746a-mono.c # For single-color
SRC += is31fl3746a.c # For RGB
I2C_DRIVER_REQUIRED = yes
```

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                       |Default                           |Description                                         |
|-----------------------------|----------------------------------|----------------------------------------------------|
|`IS31FL3746A_SDB_PIN`        |*Not defined*                     |The GPIO pin connected to the drivers' shutdown pins|
|`IS31FL3746A_I2C_TIMEOUT`    |`100`                             |The I²C timeout in milliseconds                     |
|`IS31FL3746A_I2C_PERSISTENCE`|`0`                               |The number of times to retry I²C transmissions      |
|`IS31FL3746A_I2C_ADDRESS_1`  |*Not defined*                     |The I²C address of driver 0                         |
|`IS31FL3746A_I2C_ADDRESS_2`  |*Not defined*                     |The I²C address of driver 1                         |
|`IS31FL3746A_I2C_ADDRESS_3`  |*Not defined*                     |The I²C address of driver 2                         |
|`IS31FL3746A_I2C_ADDRESS_4`  |*Not defined*                     |The I²C address of driver 3                         |
|`IS31FL3746A_CONFIGURATION`  |`0x01`                            |The value of the configuration register             |
|`IS31FL3746A_PWM_FREQUENCY`  |`IS31FL3746A_PWM_FREQUENCY_29K_HZ`|The PWM frequency of the LEDs                       |
|`IS31FL3746A_SW_PULLDOWN`    |`IS31FL3746A_PDR_2K_OHM_SW_OFF`   |The `SWx` pulldown resistor value                   |
|`IS31FL3746A_CS_PULLUP`      |`IS31FL3746A_PUR_2K_OHM_CS_OFF`   |The `CSx` pullup resistor value                     |
|`IS31FL3746A_GLOBAL_CURRENT` |`0xFF`                            |The global current control value                    |

### I²C Addressing {#i2c-addressing}

The IS31FL3746A has 16 possible 7-bit I²C addresses, depending on how the `ADDR1` and `ADDR2` pins are connected.

To configure this, set the `IS31FL3746A_I2C_ADDRESS_n` defines to one of the following in your `config.h`, where *n* denotes the driver index:

|Define                           |Value |
|---------------------------------|------|
|`IS31FL3746A_I2C_ADDRESS_GND_GND`|`0x60`|
|`IS31FL3746A_I2C_ADDRESS_GND_SCL`|`0x61`|
|`IS31FL3746A_I2C_ADDRESS_GND_SDA`|`0x62`|
|`IS31FL3746A_I2C_ADDRESS_GND_VCC`|`0x63`|
|`IS31FL3746A_I2C_ADDRESS_SCL_GND`|`0x64`|
|`IS31FL3746A_I2C_ADDRESS_SCL_SCL`|`0x65`|
|`IS31FL3746A_I2C_ADDRESS_SCL_SDA`|`0x66`|
|`IS31FL3746A_I2C_ADDRESS_SCL_VCC`|`0x67`|
|`IS31FL3746A_I2C_ADDRESS_SDA_GND`|`0x68`|
|`IS31FL3746A_I2C_ADDRESS_SDA_SCL`|`0x69`|
|`IS31FL3746A_I2C_ADDRESS_SDA_SDA`|`0x6A`|
|`IS31FL3746A_I2C_ADDRESS_SDA_VCC`|`0x6B`|
|`IS31FL3746A_I2C_ADDRESS_VCC_GND`|`0x6C`|
|`IS31FL3746A_I2C_ADDRESS_VCC_SCL`|`0x6D`|
|`IS31FL3746A_I2C_ADDRESS_VCC_SDA`|`0x6E`|
|`IS31FL3746A_I2C_ADDRESS_VCC_VCC`|`0x6F`|

### PWM Frequency {#pwm-frequency}

The PWM frequency can be adjusted by adding the following to your `config.h`:

```c
#define IS31FL3746A_PWM_FREQUENCY IS31FL3746A_PWM_FREQUENCY_29K_HZ
```

Valid values are:

|Define                             |Frequency       |
|-----------------------------------|----------------|
|`IS31FL3746A_PWM_FREQUENCY_29K_HZ` |29 kHz (default)|
|`IS31FL3746A_PWM_FREQUENCY_14K5_HZ`|14.5 kHz        |
|`IS31FL3746A_PWM_FREQUENCY_7K25_HZ`|7.25 kHz        |
|`IS31FL3746A_PWM_FREQUENCY_3K63_HZ`|3.63 kHz        |
|`IS31FL3746A_PWM_FREQUENCY_1K81_HZ`|1.81 kHz        |
|`IS31FL3746A_PWM_FREQUENCY_906_HZ` |906 Hz          |
|`IS31FL3746A_PWM_FREQUENCY_453_HZ` |453 Hz          |

### De-Ghosting {#de-ghosting}

These settings control the pulldown and pullup resistor values on the `SWy` and `CSx` pins respectively, for the purposes of eliminating ghosting. Refer to the datasheet (p. 23) for more information on how and why this occurs.

To adjust the resistor values, add the following to your `config.h`:

```c
#define IS31FL3746A_SW_PULLDOWN IS31FL3746A_PDR_2K_OHM_SW_OFF
#define IS31FL3746A_CS_PULLUP IS31FL3746A_PUR_2K_OHM_CS_OFF
```

Valid values for `IS31FL3746A_SW_PULLDOWN` are:

|Define                          |Resistance                    |
|--------------------------------|------------------------------|
|`IS31FL3746A_PDR_0_OHM`         |None                          |
|`IS31FL3746A_PDR_0K5_OHM_SW_OFF`|0.5 kΩ in SWx off time        |
|`IS31FL3746A_PDR_1K_OHM_SW_OFF` |1 kΩ in SWx off time          |
|`IS31FL3746A_PDR_2K_OHM_SW_OFF` |2 kΩ in SWx off time (default)|
|`IS31FL3746A_PDR_1K_OHM`        |1 kΩ                          |
|`IS31FL3746A_PDR_2K_OHM`        |2 kΩ                          |
|`IS31FL3746A_PDR_4K_OHM`        |4 kΩ                          |
|`IS31FL3746A_PDR_8K_OHM`        |8 kΩ                          |

Valid values for `IS31FL3746A_CS_PULLUP` are:

|Define                          |Resistance                    |
|--------------------------------|------------------------------|
|`IS31FL3746A_PUR_0_OHM`         |None (default)                |
|`IS31FL3746A_PUR_0K5_OHM_CS_OFF`|0.5 kΩ in CSy off time        |
|`IS31FL3746A_PUR_1K_OHM_CS_OFF` |1 kΩ in CSy off time          |
|`IS31FL3746A_PUR_2K_OHM_CS_OFF` |2 kΩ in CSy off time (default)|
|`IS31FL3746A_PUR_1K_OHM`        |1 kΩ                          |
|`IS31FL3746A_PUR_2K_OHM`        |2 kΩ                          |
|`IS31FL3746A_PUR_4K_OHM`        |4 kΩ                          |
|`IS31FL3746A_PUR_8K_OHM`        |8 kΩ                          |

### Global Current Control {#global-current-control}

This setting controls the current sunk by the `CSy` pins, from 0 to 255. By default, the value is the maximum (255), but if you need to lower it, add the following to your `config.h`:

```c
#define IS31FL3746A_GLOBAL_CURRENT 0xFF
```

## ARM/ChibiOS Configuration {#arm-configuration}

Depending on the ChibiOS board configuration, you may need to [enable and configure I²C](i2c#arm-configuration) at the keyboard level.

## LED Mapping {#led-mapping}

In order to use this driver, each output must be mapped to an LED index, by adding the following to your `<keyboardname>.c`:

```c
const is31fl3746a_led_t PROGMEM g_is31fl3746a_leds[IS31FL3746A_LED_COUNT] = {
/* Driver
 *   |  R         G         B */
    {0, SW1_CS1,  SW1_CS2,  SW1_CS3},
    // etc...
};
```

In this example, the red, green and blue channels for the first LED index on driver 0 all have their anodes connected to the `SW1` pin, and their cathodes on the `CS1`, `CS2` and `CS3` pins respectively.

For the single-color driver, the principle is the same, but there is only one channel:

```c
const is31fl3746a_led_t PROGMEM g_is31fl3746a_leds[IS31FL3746A_LED_COUNT] = {
/* Driver
 *   |  V */
    {0, SW1_CS1},
    // etc...
};
```

These values correspond to the register indices as shown in the datasheet on page 13, figure 8.

## API {#api}

### `struct is31fl3746a_led_t` {#api-is31fl3746a-led-t}

Contains the PWM register addresses for a single RGB LED.

#### Members {#api-is31fl3746a-led-t-members}

 - `uint8_t driver`  
   The driver index of the LED, from 0 to 3.
 - `uint8_t r`  
   The output PWM register address for the LED's red channel (RGB driver only).
 - `uint8_t g`  
   The output PWM register address for the LED's green channel (RGB driver only).
 - `uint8_t b`  
   The output PWM register address for the LED's blue channel (RGB driver only).
 - `uint8_t v`  
   The output PWM register address for the LED (single-color driver only).

---

### `void is31fl3746a_init(uint8_t index)` {#api-is31fl3746a-init}

Initialize the LED driver. This function should be called first.

#### Arguments {#api-is31fl3746a-init-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t sync`  
   Sync configuration of the LED driver.

---

### `void is31fl3746a_write_register(uint8_t index, uint8_t reg, uint8_t data)` {#api-is31fl3746a-write-register}

Set the value of the given register.

#### Arguments {#api-is31fl3746a-write-register-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t reg`  
   The register address.
 - `uint8_t data`  
   The value to set.

---

### `void is31fl3746a_select_page(uint8_t index, uint8_t page)` {#api-is31fl3746a-select-page}

Change the current page for configuring the LED driver.

#### Arguments {#api-is31fl3746a-select-page-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t page`  
   The page number to select.

---

### `void is31fl3746a_set_color(int index, uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3746a-set-color}

Set the color of a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3746a_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3746a-set-color-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3746a_leds` array).
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3746a_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3746a-set-color-all}

Set the color of all LEDs (RGB driver only).

#### Arguments {#api-is31fl3746a-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3746a_set_value(int index, uint8_t value)` {#api-is31fl3746a-set-value}

Set the brightness of a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3746a_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3746a-set-value-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3746a_leds` array).
 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3746a_set_value_all(uint8_t value)` {#api-is31fl3746a-set-value-all}

Set the brightness of all LEDs (single-color driver only).

#### Arguments {#api-is31fl3746a-set-value-all-arguments}

 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3746a_set_scaling_register(uint8_t index, uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3746a-set-scaling-register-rgb}

Configure the scaling registers for a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3746a_update_scaling_registers()` after you are finished.

#### Arguments {#api-is31fl3746a-set-scaling-register-rgb-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3746a_leds` array).
 - `uint8_t red`  
   The scaling value for the red channel.
 - `uint8_t green`  
   The scaling value for the green channel.
 - `uint8_t blue`  
   The scaling value for the blue channel.

---

### `void is31fl3746a_set_scaling_register(uint8_t index, uint8_t value)` {#api-is31fl3746a-set-scaling-register-mono}

Configure the scaling register for a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3746a_update_scaling_registers()` after you are finished.

#### Arguments {#api-is31fl3746a-set-scaling-register-mono-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3746a_leds` array).
 - `uint8_t value`  
   The scaling value for the LED.

---

### `void is31fl3746a_update_pwm_buffers(uint8_t index)` {#api-is31fl3746a-update-pwm-buffers}

Flush the PWM values to the LED driver.

#### Arguments {#api-is31fl3746a-update-pwm-buffers-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void is31fl3746a_update_scaling_registers(uint8_t index)` {#api-is31fl3746a-update-scaling-registers}

Flush the scaling register values to the LED driver.

#### Arguments {#api-is31fl3746a-update-scaling-registers-arguments}

 - `uint8_t index`  
   The driver index.
