# IS31FL3218 Driver {#is31fl3218-driver}

I²C LED driver by <PERSON><PERSON><PERSON><PERSON>. Supports up to 18 single-color LEDs, or 6 RGB LEDs.

[IS31FL3218 Datasheet](https://www.lumissil.com/assets/pdf/core/IS31FL3218_DS.pdf)

## Usage {#usage}

The IS31FL3218 driver code is automatically included if you are using the [LED Matrix](../features/led_matrix) or [RGB Matrix](../features/rgb_matrix) feature with the `is31fl3218` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add this to your `rules.mk`:

```make
COMMON_VPATH += $(DRIVER_PATH)/led/issi
SRC += is31fl3218-mono.c # For single-color
SRC += is31fl3218.c # For RGB
I2C_DRIVER_REQUIRED = yes
```

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                      |Default      |Description                                        |
|----------------------------|-------------|---------------------------------------------------|
|`IS31FL3218_SDB_PIN`        |*Not defined*|The GPIO pin connected to the driver's shutdown pin|
|`IS31FL3218_I2C_TIMEOUT`    |`100`        |The I²C timeout in milliseconds                    |
|`IS31FL3218_I2C_PERSISTENCE`|`0`          |The number of times to retry I²C transmissions     |

### I²C Addressing {#i2c-addressing}

The IS31FL3218's 7-bit I²C address is `0x54`, available as `IS31FL3218_I2C_ADDRESS`.

## ARM/ChibiOS Configuration {#arm-configuration}

Depending on the ChibiOS board configuration, you may need to [enable and configure I²C](i2c#arm-configuration) at the keyboard level.

## LED Mapping {#led-mapping}

In order to use this driver, each output must be mapped to an LED index, by adding the following to your `<keyboardname>.c`:

```c
const is31fl3218_led_t PROGMEM g_is31fl3218_leds[IS31FL3218_LED_COUNT] = {
/*   R     G     B */
    {OUT1, OUT2, OUT3},
    // etc...
};
```

In this example, the red, green and blue channels for the first LED index all have their anodes connected to `VCC`, and their cathodes on the `OUT1`, `OUT2` and `OUT3` pins respectively.

For the single-color driver, the principle is the same, but there is only one channel:

```c
const is31fl3218_led_t PROGMEM g_is31fl3218_leds[IS31FL3218_LED_COUNT] = {
/*   V */
    {OUT1},
    // etc...
};
```

## API {#api}

### `struct is31fl3218_led_t` {#api-is31fl3218-led-t}

Contains the PWM register addresses for a single RGB LED.

#### Members {#api-is31fl3218-led-t-members}

 - `uint8_t r`  
   The output PWM register address for the LED's red channel (RGB driver only).
 - `uint8_t g`  
   The output PWM register address for the LED's green channel (RGB driver only).
 - `uint8_t b`  
   The output PWM register address for the LED's blue channel (RGB driver only).
 - `uint8_t v`  
   The output PWM register address for the LED (single-color driver only).

---

### `void is31fl3218_init(void)` {#api-is31fl3218-init}

Initialize the LED driver. This function should be called first.

---

### `void is31fl3218_write_register(uint8_t reg, uint8_t data)` {#api-is31fl3218-write-register}

Set the value of the given register.

#### Arguments {#api-is31fl3218-write-register-arguments}

 - `uint8_t reg`  
   The register address.
 - `uint8_t data`  
   The value to set.

---

### `void is31fl3218_set_color(int index, uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3218-set-color}

Set the color of a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3218_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3218-set-color-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3218_leds` array).
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3218_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3218-set-color-all}

Set the color of all LEDs (RGB driver only).

#### Arguments {#api-is31fl3218-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3218_set_value(int index, uint8_t value)` {#api-is31fl3218-set-value}

Set the brightness of a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3218_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3218-set-value-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3218_leds` array).
 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3218_set_value_all(uint8_t value)` {#api-is31fl3218-set-value-all}

Set the brightness of all LEDs (single-color driver only).

#### Arguments {#api-is31fl3218-set-value-all-arguments}

 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3218_set_led_control_register(uint8_t index, bool red, bool green, bool blue)` {#api-is31fl3218-set-led-control-register-rgb}

Configure the LED control registers for a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3218_update_led_control_registers()` after you are finished.

#### Arguments {#api-is31fl3218-set-led-control-register-rgb-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3218_leds` array).
 - `bool red`  
   Enable or disable the red channel.
 - `bool green`  
   Enable or disable the green channel.
 - `bool blue`  
   Enable or disable the blue channel.

---

### `void is31fl3218_set_led_control_register(uint8_t index, bool value)` {#api-is31fl3218-set-led-control-register-mono}

Configure the LED control registers for a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3218_update_led_control_registers()` after you are finished.

#### Arguments {#api-is31fl3218-set-led-control-register-mono-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3218_leds` array).
 - `bool value`  
   Enable or disable the LED.

---

### `void is31fl3218_update_pwm_buffers(void)` {#api-is31fl3218-update-pwm-buffers}

Flush the PWM values to the LED driver.

---

### `void is31fl3218_update_led_control_registers(void)` {#api-is31fl3218-update-led-control-registers}

Flush the LED control register values to the LED driver.
