# IS31FL3736 Driver {#is31fl3736-driver}

I²C 12x8 LED matrix driver by <PERSON><PERSON><PERSON><PERSON>. Supports a maximum of four drivers, each controlling up to 96 single-color LEDs, or 32 RGB LEDs.

[IS31FL3736 Datasheet](https://www.lumissil.com/assets/pdf/core/IS31FL3736_DS.pdf)

## Usage {#usage}

The IS31FL3736 driver code is automatically included if you are using the [LED Matrix](../features/led_matrix) or [RGB Matrix](../features/rgb_matrix) feature with the `is31fl3736` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add this to your `rules.mk`:

```make
COMMON_VPATH += $(DRIVER_PATH)/led/issi
SRC += is31fl3736-mono.c # For single-color
SRC += is31fl3736.c # For RGB
I2C_DRIVER_REQUIRED = yes
```

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                      |Default                          |Description                                         |
|----------------------------|---------------------------------|----------------------------------------------------|
|`IS31FL3736_SDB_PIN`        |*Not defined*                    |The GPIO pin connected to the drivers' shutdown pins|
|`IS31FL3736_I2C_TIMEOUT`    |`100`                            |The I²C timeout in milliseconds                     |
|`IS31FL3736_I2C_PERSISTENCE`|`0`                              |The number of times to retry I²C transmissions      |
|`IS31FL3736_I2C_ADDRESS_1`  |*Not defined*                    |The I²C address of driver 0                         |
|`IS31FL3736_I2C_ADDRESS_2`  |*Not defined*                    |The I²C address of driver 1                         |
|`IS31FL3736_I2C_ADDRESS_3`  |*Not defined*                    |The I²C address of driver 2                         |
|`IS31FL3736_I2C_ADDRESS_4`  |*Not defined*                    |The I²C address of driver 3                         |
|`IS31FL3736_PWM_FREQUENCY`  |`IS31FL3736_PWM_FREQUENCY_8K4_HZ`|The PWM frequency of the LEDs (IS31FL3736B only)    |
|`IS31FL3736_SW_PULLUP`      |`IS31FL3736_PUR_0_OHM`           |The `SWx` pullup resistor value                     |
|`IS31FL3736_CS_PULLDOWN`    |`IS31FL3736_PDR_0_OHM`           |The `CSx` pulldown resistor value                   |
|`IS31FL3736_GLOBAL_CURRENT` |`0xFF`                           |The global current control value                    |

### I²C Addressing {#i2c-addressing}

The IS31FL3736 has 16 possible 7-bit I²C addresses, depending on how the `ADDR1` and `ADDR2` pins are connected.

To configure this, set the `IS31FL3736_I2C_ADDRESS_n` defines to one of the following in your `config.h`, where *n* denotes the driver index:

|Define                          |Value |
|--------------------------------|------|
|`IS31FL3736_I2C_ADDRESS_GND_GND`|`0x50`|
|`IS31FL3736_I2C_ADDRESS_GND_SCL`|`0x51`|
|`IS31FL3736_I2C_ADDRESS_GND_SDA`|`0x52`|
|`IS31FL3736_I2C_ADDRESS_GND_VCC`|`0x53`|
|`IS31FL3736_I2C_ADDRESS_SCL_GND`|`0x54`|
|`IS31FL3736_I2C_ADDRESS_SCL_SCL`|`0x55`|
|`IS31FL3736_I2C_ADDRESS_SCL_SDA`|`0x56`|
|`IS31FL3736_I2C_ADDRESS_SCL_VCC`|`0x57`|
|`IS31FL3736_I2C_ADDRESS_SDA_GND`|`0x58`|
|`IS31FL3736_I2C_ADDRESS_SDA_SCL`|`0x59`|
|`IS31FL3736_I2C_ADDRESS_SDA_SDA`|`0x5A`|
|`IS31FL3736_I2C_ADDRESS_SDA_VCC`|`0x5B`|
|`IS31FL3736_I2C_ADDRESS_VCC_GND`|`0x5C`|
|`IS31FL3736_I2C_ADDRESS_VCC_SCL`|`0x5D`|
|`IS31FL3736_I2C_ADDRESS_VCC_SDA`|`0x5E`|
|`IS31FL3736_I2C_ADDRESS_VCC_VCC`|`0x5F`|

### PWM Frequency {#pwm-frequency}

The PWM frequency can be adjusted (for IS31FL3736B only) by adding the following to your `config.h`:

```c
#define IS31FL3736_PWM_FREQUENCY IS31FL3736_PWM_FREQUENCY_8K4_HZ
```

Valid values are:

|Define                            |Frequency        |
|----------------------------------|-----------------|
|`IS31FL3736_PWM_FREQUENCY_8K4_HZ` |8.4 kHz (default)|
|`IS31FL3736_PWM_FREQUENCY_4K2_HZ` |4.2 kHz          |
|`IS31FL3736_PWM_FREQUENCY_26K7_HZ`|26.7 kHz         |
|`IS31FL3736_PWM_FREQUENCY_2K1_HZ` |2.1 kHz          |
|`IS31FL3736_PWM_FREQUENCY_1K05_HZ`|1.05 kHz         |

### De-Ghosting {#de-ghosting}

These settings control the pullup and pulldown resistor values on the `SWy` and `CSx` pins respectively, for the purposes of eliminating ghosting. Refer to the datasheet (p. 25) for more information on how and why this occurs.

To adjust the resistor values, add the following to your `config.h`:

```c
#define IS31FL3736_SW_PULLUP IS31FL3736_PUR_0_OHM
#define IS31FL3736_CS_PULLDOWN IS31FL3736_PDR_0_OHM
```

Valid values for `IS31FL3736_SW_PULLUP` are:

|Define                  |Resistance    |
|------------------------|--------------|
|`IS31FL3736_PUR_0_OHM`  |None (default)|
|`IS31FL3736_PUR_0K5_OHM`|0.5 kΩ        |
|`IS31FL3736_PUR_1K_OHM` |1 kΩ          |
|`IS31FL3736_PUR_2K_OHM` |2 kΩ          |
|`IS31FL3736_PUR_4K_OHM` |4 kΩ          |
|`IS31FL3736_PUR_8K_OHM` |8 kΩ          |
|`IS31FL3736_PUR_16K_OHM`|16 kΩ         |
|`IS31FL3736_PUR_32K_OHM`|32 kΩ         |

Valid values for `IS31FL3736_CS_PULLDOWN` are:

|Define                  |Resistance    |
|------------------------|--------------|
|`IS31FL3736_PDR_0_OHM`  |None (default)|
|`IS31FL3736_PDR_0K5_OHM`|0.5 kΩ        |
|`IS31FL3736_PDR_1K_OHM` |1 kΩ          |
|`IS31FL3736_PDR_2K_OHM` |2 kΩ          |
|`IS31FL3736_PDR_4K_OHM` |4 kΩ          |
|`IS31FL3736_PDR_8K_OHM` |8 kΩ          |
|`IS31FL3736_PDR_16K_OHM`|16 kΩ         |
|`IS31FL3736_PDR_32K_OHM`|32 kΩ         |

### Global Current Control {#global-current-control}

This setting controls the current sunk by the `CSx` pins, from 0 to 255. By default, the value is the maximum (255), but if you need to lower it, add the following to your `config.h`:

```c
#define IS31FL3736_GLOBAL_CURRENT 0xFF
```

## ARM/ChibiOS Configuration {#arm-configuration}

Depending on the ChibiOS board configuration, you may need to [enable and configure I²C](i2c#arm-configuration) at the keyboard level.

## LED Mapping {#led-mapping}

In order to use this driver, each output must be mapped to an LED index, by adding the following to your `<keyboardname>.c`:

```c
const is31fl3736_led_t PROGMEM g_is31fl3736_leds[IS31FL3736_LED_COUNT] = {
/* Driver
 *   |  R         G         B */
    {0, SW1_CS1,  SW1_CS2,  SW1_CS3},
    // etc...
};
```

In this example, the red, green and blue channels for the first LED index on driver 0 all have their cathodes connected to the `SW1` pin, and their anodes on the `CS1`, `CS2` and `CS3` pins respectively.

For the single-color driver, the principle is the same, but there is only one channel:

```c
const is31fl3736_led_t PROGMEM g_is31fl3736_leds[IS31FL3736_LED_COUNT] = {
/* Driver
 *   |  V */
    {0, SW1_CS1},
    // etc...
};
```

These values correspond to the register indices as shown in the datasheet on page 16, figure 9.

## API {#api}

### `struct is31fl3736_led_t` {#api-is31fl3736-led-t}

Contains the PWM register addresses for a single RGB LED.

#### Members {#api-is31fl3736-led-t-members}

 - `uint8_t driver`  
   The driver index of the LED, from 0 to 3.
 - `uint8_t r`  
   The output PWM register address for the LED's red channel (RGB driver only).
 - `uint8_t g`  
   The output PWM register address for the LED's green channel (RGB driver only).
 - `uint8_t b`  
   The output PWM register address for the LED's blue channel (RGB driver only).
 - `uint8_t v`  
   The output PWM register address for the LED (single-color driver only).

---

### `void is31fl3736_init(uint8_t index)` {#api-is31fl3736-init}

Initialize the LED driver. This function should be called first.

#### Arguments {#api-is31fl3736-init-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void is31fl3736_write_register(uint8_t index, uint8_t reg, uint8_t data)` {#api-is31fl3736-write-register}

Set the value of the given register.

#### Arguments {#api-is31fl3736-write-register-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t reg`  
   The register address.
 - `uint8_t data`  
   The value to set.

---

### `void is31fl3736_select_page(uint8_t index, uint8_t page)` {#api-is31fl3736-select-page}

Change the current page for configuring the LED driver.

#### Arguments {#api-is31fl3736-select-page-arguments}

 - `uint8_t index`  
   The driver index.
 - `uint8_t page`  
   The page number to select.

---

### `void is31fl3736_set_color(int index, uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3736-set-color}

Set the color of a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3736_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3736-set-color-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3736_leds` array).
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3736_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-is31fl3736-set-color-all}

Set the color of all LEDs (RGB driver only).

#### Arguments {#api-is31fl3736-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void is31fl3736_set_value(int index, uint8_t value)` {#api-is31fl3736-set-value}

Set the brightness of a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3736_update_pwm_buffers()` after you are finished.

#### Arguments {#api-is31fl3736-set-value-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_is31fl3736_leds` array).
 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3736_set_value_all(uint8_t value)` {#api-is31fl3736-set-value-all}

Set the brightness of all LEDs (single-color driver only).

#### Arguments {#api-is31fl3736-set-value-all-arguments}

 - `uint8_t value`  
   The brightness value to set.

---

### `void is31fl3736_set_led_control_register(uint8_t index, bool red, bool green, bool blue)` {#api-is31fl3736-set-led-control-register-rgb}

Configure the LED control registers for a single LED (RGB driver only). This function does not immediately update the LEDs; call `is31fl3736_update_led_control_registers()` after you are finished.

#### Arguments {#api-is31fl3736-set-led-control-register-rgb-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3736_leds` array).
 - `bool red`  
   Enable or disable the red channel.
 - `bool green`  
   Enable or disable the green channel.
 - `bool blue`  
   Enable or disable the blue channel.

---

### `void is31fl3736_set_led_control_register(uint8_t index, bool value)` {#api-is31fl3736-set-led-control-register-mono}

Configure the LED control registers for a single LED (single-color driver only). This function does not immediately update the LEDs; call `is31fl3736_update_led_control_registers()` after you are finished.

#### Arguments {#api-is31fl3736-set-led-control-register-mono-arguments}

 - `uint8_t index`  
   The LED index (ie. the index into the `g_is31fl3736_leds` array).
 - `bool value`  
   Enable or disable the LED.

---

### `void is31fl3736_update_pwm_buffers(uint8_t index)` {#api-is31fl3736-update-pwm-buffers}

Flush the PWM values to the LED driver.

#### Arguments {#api-is31fl3736-update-pwm-buffers-arguments}

 - `uint8_t index`  
   The driver index.

---

### `void is31fl3736_update_led_control_registers(uint8_t index)` {#api-is31fl3736-update-led-control-registers}

Flush the LED control register values to the LED driver.

#### Arguments {#api-is31fl3736-update-led-control-registers-arguments}

 - `uint8_t index`  
   The driver index.
