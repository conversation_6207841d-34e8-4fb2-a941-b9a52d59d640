# AW20216S Driver {#aw20216s-driver}

SPI 18x12 LED matrix driver by <PERSON><PERSON><PERSON>. Supports a maximum of four drivers, each controlling up to 216 single-color LEDs, or 72 RGB LEDs.

[AW20216S Datasheet](https://doc.awinic.com/doc/20230609wm/b6a9c70b-e1bd-495b-925f-bcbed3fc2620.pdf)

## Usage {#usage}

The AW20216S driver code is automatically included if you are using the [RGB Matrix](../features/rgb_matrix) feature with the `aw20216s` driver set, and you would use those APIs instead.

However, if you need to use the driver standalone, add this to your `rules.mk`:

```make
COMMON_VPATH += $(DRIVER_PATH)/led
SRC += aw20216s.c
SPI_DRIVER_REQUIRED = yes
```

## Basic Configuration {#basic-configuration}

Add the following to your `config.h`:

|Define                       |Default      |Description                                                  |
|-----------------------------|-------------|-------------------------------------------------------------|
|`AW20216S_CS_PIN_1`          |*Not defined*|The GPIO pin connected to the first driver's Chip Select pin |
|`AW20216S_CS_PIN_2`          |*Not defined*|The GPIO pin connected to the second driver's Chip Select pin|
|`AW20216S_EN_PIN`            |*Not defined*|The GPIO pin connected to the drivers' Enable pins           |
|`AW20216S_SPI_MODE`          |`0`          |The SPI mode to use                                          |
|`AW20216S_SPI_DIVISOR`       |`4`          |The SPI divisor to use                                       |
|`AW20216S_SCALING_MAX`       |`150`        |The scaling value                                            |
|`AW20216S_GLOBAL_CURRENT_MAX`|`150`        |The global current control value                             |

### Global Current Control {#global-current-control}

This setting controls the current sunk by the `CSx` pins, from 0 to 255. To adjust it, add the following to your `config.h`:

```c
#define AW20216S_GLOBAL_CURRENT_MAX 150
```

## ARM/ChibiOS Configuration {#arm-configuration}

Depending on the ChibiOS board configuration, you may need to [enable and configure SPI](spi#arm-configuration) at the keyboard level.

## LED Mapping {#led-mapping}

In order to use this driver, each output must be mapped to an LED index, by adding the following to your `<keyboardname>.c`:

```c
const aw20216s_led_t PROGMEM g_aw20216s_leds[AW20216S_LED_COUNT] = {
/* Driver
 *   |  R          G          B */
    {0, SW1_CS1,   SW1_CS2,   SW1_CS3},
    // etc...
};
```

In this example, the first LED index on driver 0 has its red channel on `SW1_CS1`, green on `SW1_CS2` and blue on `SW1_CS3`.

These values correspond to the matrix locations as shown in the datasheet on page 16, figure 16.

## API {#api}

### `struct aw20216s_led_t` {#api-aw20216s-led-t}

Contains the PWM register addresses for a single RGB LED.

#### Members {#api-aw20216s-led-t-members}

 - `uint8_t driver`  
   The driver index of the LED, from 0 to 3.
 - `uint8_t r`  
   The output PWM register address for the LED's red channel.
 - `uint8_t g`  
   The output PWM register address for the LED's green channel.
 - `uint8_t b`  
   The output PWM register address for the LED's blue channel.

---

### `void aw20216s_init(pin_t cs_pin)` {#api-aw20216s-init}

Initialize the LED driver. This function should be called first.

#### Arguments {#api-aw20216s-init-arguments}

 - `pin_t cs_pin`  
   The GPIO connected to the Chip Select pin of the LED driver to initialize.

---

### `void aw20216s_set_color(int index, uint8_t red, uint8_t green, uint8_t blue)` {#api-aw20216s-set-color}

Set the color of a single LED. This function does not immediately update the LEDs; call `aw20216s_update_pwm_buffers()` after you are finished.

#### Arguments {#api-aw20216s-set-color-arguments}

 - `int index`  
   The LED index (ie. the index into the `g_aw20216s_leds` array).
 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void aw20216s_set_color_all(uint8_t red, uint8_t green, uint8_t blue)` {#api-aw20216s-set-color-all}

Set the color of all LEDs.

#### Arguments {#api-aw20216s-set-color-all-arguments}

 - `uint8_t red`  
   The red value to set.
 - `uint8_t green`  
   The green value to set.
 - `uint8_t blue`  
   The blue value to set.

---

### `void aw20216s_update_pwm_buffers(pin_t cs_pin, uint8_t index)` {#api-aw20216s-update-pwm-buffers}

Flush the PWM values to the LED driver.

#### Arguments {#api-aw20216s-update-pwm-buffers-arguments}

 - `pin_t cs_pin`  
   The GPIO connected to the Chip Select pin of the driver.
 - `uint8_t index`  
   The index of the driver.
