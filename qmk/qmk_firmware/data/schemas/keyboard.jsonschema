{
    "$schema": "https://json-schema.org/draft/2020-12/schema#",
    "$id": "qmk.keyboard.v1",
    "title": "Keyboard Information",
    "definitions": {
        "encoder_config": {
            "type": "object",
            "properties": {
                "driver": {
                    "type": "string",
                    "enum": ["custom", "quadrature"]
                },
                "rotary": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "additionalProperties": false,
                        "required": ["pin_a", "pin_b"],
                        "properties": {
                            "pin_a": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                            "pin_b": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                            "resolution": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                        }
                    }
                }
            }
        },
        "dip_switch_config": {
            "type": "object",
            "properties": {
                "pins": {"$ref": "qmk.definitions.v1#/mcu_pin_array"}
            }
        }
        "features_config": {
            "$ref": "qmk.definitions.v1#/boolean_array",
            "propertyNames": {"$ref": "qmk.definitions.v1#/snake_case"},
            "not": {"required": ["lto"]}
        },
    },
    "type": "object",
    "not": {"required": ["vendorId", "productId"]}, // reject via keys...
    "properties": {
        "keyboard_name": {"$ref": "qmk.definitions.v1#/text_identifier"},
        "keyboard_folder": {"$ref": "qmk.definitions.v1#/keyboard"},
        "maintainer": {"$ref": "qmk.definitions.v1#/text_identifier"},
        "manufacturer": {"$ref": "qmk.definitions.v1#/text_identifier"},
        "url": {
            "type": "string",
            "format": "uri"
        },
        "development_board": {
            "type": "string",
            "enum": ["promicro", "elite_c", "elite_pi", "proton_c", "kb2040", "promicro_rp2040", "blok", "michi", "bit_c_pro", "stemcell", "bluepill", "blackpill_f401", "blackpill_f411", "bonsai_c4", "helios", "liatris", "imera", "svlinky"]
        },
        "pin_compatible": {
            "type": "string",
            "enum": ["promicro", "elite_c"]
        },
        "processor": {
            "type": "string",
            "enum": [
                "cortex-m0",
                "cortex-m0plus",
                "cortex-m3",
                "cortex-m4",
                "cortex-m7",
                "cortex-m23",
                "cortex-m33",
                "cortex-m35p",
                "cortex-m55",
                "cortex-m85",
                "MKL26Z64",
                "MK20DX128",
                "MK20DX256",
                "MK64FX512",
                "MK66FX1M0",
                "RP2040",
                "STM32F042",
                "STM32F072",
                "STM32F103",
                "STM32F303",
                "STM32F401",
                "STM32F405",
                "STM32F407",
                "STM32F411",
                "STM32F446",
                "STM32G431",
                "STM32G474",
                "STM32H723",
                "STM32H733",
                "STM32L412",
                "STM32L422",
                "STM32L432",
                "STM32L433",
                "STM32L442",
                "STM32L443",
                "GD32VF103",
                "WB32F3G71",
                "WB32FQ95",
                "AT32F415",
                "atmega16u2",
                "atmega32u2",
                "atmega16u4",
                "atmega32u4",
                "at90usb162",
                "at90usb646",
                "at90usb647",
                "at90usb1286",
                "at90usb1287",
                "atmega32a",
                "atmega328p",
                "atmega328",
                "attiny85",
                "unknown"
            ]
        },
        "apa102": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "data_pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "clock_pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "default_brightness": {
                    "type": "integer",
                    "minimum": 0,
                    "maximum": 31
                }
            }
        },
        "audio": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "default": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "on": {"type": "boolean"},
                        "clicky": {"type": "boolean"}
                    }
                },
                "driver": {
                    "type": "string",
                    "enum": ["dac_additive", "dac_basic", "pwm_software", "pwm_hardware"]
                },
                "macro_beep": {"type": "boolean"},
                "pins": {"$ref": "qmk.definitions.v1#/mcu_pin_array"},
                "power_control": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "on_state": {"$ref": "qmk.definitions.v1#/bit"},
                        "pin": {"$ref": "qmk.definitions.v1#/mcu_pin"}
                    }
                },
                "voices": {"type": "boolean"}
            }
        },
        "backlight": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "driver": {
                    "type": "string",
                    "enum": ["custom", "pwm", "software", "timer"]
                },
                "default": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "on": {"type": "boolean"},
                        "breathing": {"type": "boolean"},
                        "brightness": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                    }
                },
                "breathing": {"type": "boolean"},
                "breathing_period": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "levels": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 31
                },
                "max_brightness": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "pins": {"$ref": "qmk.definitions.v1#/mcu_pin_array"},
                "on_state": {"$ref": "qmk.definitions.v1#/bit"},
                "as_caps_lock": {"type": "boolean"}
            }
        },
        "bluetooth": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "driver": {
                    "type": "string",
                    "enum": ["bluefruit_le", "custom", "rn42"]
                }
            }
        },
        "bootmagic":{
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "enabled": {"type": "boolean"},
                "matrix": {
                    "type": "array",
                    "minItems": 2,
                    "maxItems": 2,
                    "items": {
                        "type": "integer",
                        "minimum": 0
                    }
                }
            }
        },
        "board": {
            "type": "string",
            "minLength": 2,
            "pattern": "^[a-zA-Z_][0-9a-zA-Z_]*$"
        },
        "bootloader": {
            "type": "string",
            "enum": [
                "apm32-dfu",
                "at32-dfu",
                "atmel-dfu",
                "bootloadhid",
                "caterina",
                "custom",
                "gd32v-dfu",
                "halfkay",
                "kiibohd",
                "lufa-dfu",
                "lufa-ms",
                "md-boot",
                "qmk-dfu",
                "qmk-hid",
                "rp2040",
                "stm32-dfu",
                "stm32duino",
                "tinyuf2",
                "uf2boot",
                "unknown",
                "usbasploader",
                "wb32-dfu"
            ]
        },
        "bootloader_instructions": {
            "type": "string",
            "description": "Instructions for putting the keyboard into a mode that allows for firmware flashing."
        },
        "build": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "debounce_type": {
                    "type": "string",
                    "enum": ["asym_eager_defer_pk", "custom", "sym_defer_g", "sym_defer_pk", "sym_defer_pr", "sym_eager_pk", "sym_eager_pr"]
                },
                "firmware_format": {
                    "type": "string",
                    "enum": ["bin", "hex", "uf2"]
                },
                "lto": {"type": "boolean"}
            }
        },
        "diode_direction": {
            "type": "string",
            "enum": ["COL2ROW", "ROW2COL"]
        },
        "debounce": {"$ref": "qmk.definitions.v1#/unsigned_int"},
        "caps_word": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "enabled": {"type": "boolean"},
                "both_shifts_turns_on": {"type": "boolean"},
                "double_tap_shift_turns_on": {"type": "boolean"},
                "idle_timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "invert_on_shift": {"type": "boolean"}
            }
        },
        "combo": {
            "type": "object",
            "properties": {
                "count": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "term": {"$ref": "qmk.definitions.v1#/unsigned_int"}
            }
        },
        "community_layouts": {
            "type": "array",
            "items": {"$ref": "qmk.definitions.v1#/filename"}
        },
        "dip_switch": {
            "$ref": "#/definitions/dip_switch_config",
            "properties": {
                "enabled": {"type": "boolean"},
                "matrix_grid": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "array",
                        "minItems": 2,
                        "maxItems": 2,
                        "items": {
                            "type": "integer",
                            "minimum": 0
                        }
                    }
                }
            }
        },
        "eeprom": {
            "properties": {
                "driver": {"type": "string"},
                "wear_leveling": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "driver": {
                            "type": "string",
                            "enum": ["custom", "embedded_flash", "legacy", "rp2040_flash", "spi_flash"]
                        },
                        "backing_size": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                        "logical_size": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                    }
                }
            }
        },
        "encoder": {
            "$ref": "#/definitions/encoder_config",
            "properties": {
                "enabled": {"type": "boolean"}
            }
        },
        "features": { "$ref": "#/definitions/features_config" },
        "indicators": {
            "type": "object",
            "properties": {
                "caps_lock": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "num_lock": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "scroll_lock": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "compose": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "kana": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "on_state": {"$ref": "qmk.definitions.v1#/bit"}
            }
        },
        "joystick": {
            "type": "object",
            "properties": {
                "enabled": {"type": "boolean"},
                "driver": {"type": "string"},
                "button_count": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "axis_resolution": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "axes": {
                    "type": "object",
                    "propertyNames": {"enum": ["x", "y", "z", "rx", "ry", "rz"]}
                    "additionalProperties": {
                        "oneOf": [
                            {
                                "type": "object",
                                "properties": {
                                    "input_pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                                    "low": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                                    "rest": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                                    "high": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                                }
                            },
                            {
                                "type": "string",
                                "enum": ["virtual"]
                            }
                        ]
                    }
                }
            }
        },
        "keycodes": {"$ref": "qmk.definitions.v1#/keycode_decl_array"},
        "layer_lock": {
            "type": "object",
            "properties": {
                "timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"}
            }
        },
        "layout_aliases": {
            "type": "object",
            "additionalProperties": {"$ref": "qmk.definitions.v1#/layout_macro"}
        },
        "layouts": {
            "type": "object",
            "propertyNames": {"$ref": "qmk.definitions.v1#/layout_macro"},
            "additionalProperties": {
                "type": "object",
                "additionalProperties": false,
                "properties": {
                    "filename": {"type": "string"},
                    "c_macro": {"type": "boolean"},
                    "json_layout": {"type": "boolean"},
                    "layout": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "additionalProperties": false,
                            "required": ["x", "y"],
                            "properties": {
                                "encoder": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                                "label": {
                                    "type": "string",
                                    "pattern": "^[^\\n]*$"
                                },
                                "matrix": {
                                    "type": "array",
                                    "minItems": 2,
                                    "maxItems": 2,
                                    "items": {
                                        "type": "integer",
                                        "minimum": 0
                                    }
                                },
                                "r": {"$ref": "qmk.definitions.v1#/signed_decimal"},
                                "rx": {"$ref": "qmk.definitions.v1#/unsigned_decimal"},
                                "ry": {"$ref": "qmk.definitions.v1#/unsigned_decimal"},
                                "h": {"$ref": "qmk.definitions.v1#/key_unit"},
                                "w": {"$ref": "qmk.definitions.v1#/key_unit"},
                                "x": {"$ref": "qmk.definitions.v1#/key_unit"},
                                "y": {"$ref": "qmk.definitions.v1#/key_unit"},
                                "hand": {
                                    "type": "string",
                                    "enum": ["L", "R", "*"]
                                }
                            }
                        }
                    }
                }
            }
        },
        "haptic": {
            "type": "object",
            "properties": {
                "driver": {
                    "type": "string",
                    "enum": ["drv2605l", "solenoid"]
                }
            }
        },
        "leader_key": {
            "type": "object",
            "properties": {
                "timing": {"type": "boolean"},
                "strict_processing": {"type": "boolean"},
                "timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"}
            }
        },
        "matrix_pins": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "custom": {"type": "boolean"},
                "custom_lite": {"type": "boolean"},
                "ghost": {"type": "boolean"},
                "input_pressed_state": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "io_delay": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "direct": {
                    "type": "array",
                    "items": {"$ref": "qmk.definitions.v1#/mcu_pin_array"}
                },
                "cols": {"$ref": "qmk.definitions.v1#/mcu_pin_array"},
                "rows": {"$ref": "qmk.definitions.v1#/mcu_pin_array"}
            }
        },
        "modules": {
            "type": "array",
            "items": {
                "type": "string"
            }
        },
        "mouse_key": {
            "type": "object",
            "properties": {
                "enabled": {"type": "boolean"},
                "delay": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "interval": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "max_speed": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "time_to_max": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "wheel_delay": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
            }
        },
        "oneshot": {
            "type": "object",
            "properties": {
                "tap_toggle": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"}
            }
        },
        "led_matrix": {
            "type": "object",
            "properties": {
                "animations": {
                    "type": "object",
                    "propertyNames": {"$ref": "qmk.definitions.v1#/snake_case"},
                    "additionalProperties": {"type": "boolean"}
                },
                "default": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "on": {"type": "boolean"},
                        "animation": {"type": "string"},
                        "val": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "speed": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                    }
                },
                "driver": {
                    "type": "string",
                    "enum": [
                        "custom",
                        "is31fl3218",
                        "is31fl3236",
                        "is31fl3729",
                        "is31fl3731",
                        "is31fl3733",
                        "is31fl3736",
                        "is31fl3737",
                        "is31fl3741",
                        "is31fl3742a",
                        "is31fl3743a",
                        "is31fl3745",
                        "is31fl3746a",
                        "snled27351"
                    ]
                },
                "center_point": {
                    "type": "array",
                    "minItems": 2,
                    "maxItems": 2,
                    "items": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                },
                "max_brightness": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "val_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "speed_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "led_flush_limit": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "led_process_limit": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "react_on_keyup": {"type": "boolean"},
                "sleep": {"type": "boolean"},
                "split_count": {
                    "type": "array",
                    "minItems": 2,
                    "maxItems": 2,
                    "items": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                },
                "layout": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "additionalProperties": false,
                        "properties": {
                            "matrix": {
                                "type": "array",
                                "minItems": 2,
                                "maxItems": 2,
                                "items": {
                                    "type": "integer",
                                    "minimum": 0
                                }
                            },
                            "x": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                            "y": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                            "flags": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                        }
                    }
                }
            }
        },
        "rgb_matrix": {
            "type": "object",
            "properties": {
                "animations": {
                    "type": "object",
                    "propertyNames": {"$ref": "qmk.definitions.v1#/snake_case"},
                    "additionalProperties": {"type": "boolean"}
                },
                "default": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "on": {"type": "boolean"},
                        "animation": {"type": "string"},
                        "hue": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "sat": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "val": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "speed": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                    }
                },
                "driver": {
                    "type": "string",
                    "enum": [
                        "aw20216s",
                        "custom",
                        "is31fl3218",
                        "is31fl3236",
                        "is31fl3729",
                        "is31fl3731",
                        "is31fl3733",
                        "is31fl3736",
                        "is31fl3737",
                        "is31fl3741",
                        "is31fl3742a",
                        "is31fl3743a",
                        "is31fl3745",
                        "is31fl3746a",
                        "snled27351",
                        "ws2812"
                    ]
                },
                "center_point": {
                    "type": "array",
                    "minItems": 2,
                    "maxItems": 2,
                    "items": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                },
                "max_brightness": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "hue_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "sat_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "val_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "speed_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "led_flush_limit": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "led_process_limit": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "react_on_keyup": {"type": "boolean"},
                "sleep": {"type": "boolean"},
                "split_count": {
                    "type": "array",
                    "minItems": 2,
                    "maxItems": 2,
                    "items": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                },
                "layout": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "additionalProperties": false,
                        "properties": {
                            "matrix": {
                                "type": "array",
                                "minItems": 2,
                                "maxItems": 2,
                                "items": {
                                    "type": "integer",
                                    "minimum": 0
                                }
                            },
                            "x": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                            "y": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                            "flags": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                        }
                    }
                }
            }
        },
        "rgblight": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "animations": {
                    "type": "object",
                    "propertyNames": {"$ref": "qmk.definitions.v1#/snake_case"},
                    "additionalProperties": {"type": "boolean"}
                },
                "brightness_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "default": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "on": {"type": "boolean"},
                        "animation": {"type": "string"},
                        "hue": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "sat": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "val": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                        "speed": {"$ref": "qmk.definitions.v1#/unsigned_int_8"}
                    }
                },
                "driver": {
                    "type": "string",
                    "enum": ["apa102", "custom", "ws2812"]
                },
                "hue_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "layers": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "blink": {"type": "boolean"},
                        "enabled": {"type": "boolean"},
                        "max": {
                            "type": "integer",
                            "minimum": 1,
                            "maximum": 32
                        },
                        "override_rgb": {"type": "boolean"}
                    }
                },
                "led_count": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "led_map": {
                    "type": "array",
                    "minItems": 2,
                    "items": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                },
                "max_brightness": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "pin": {
                    "$ref": "qmk.definitions.v1#/mcu_pin",
                    "$comment": "Deprecated: use ws2812.pin instead"
                },
                "rgbw": {
                    "type": "boolean",
                    "$comment": "Deprecated: use ws2812.rgbw instead"
                },
                "saturation_steps": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "sleep": {"type": "boolean"},
                "split": {"type": "boolean"},
                "split_count": {
                    "type": "array",
                    "minItems": 2,
                    "maxItems": 2,
                    "items": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                }
            }
        },
        "secure": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "enabled": {"type": "boolean"},
                "unlock_timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "idle_timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "unlock_sequence": {
                    "type": "array",
                    "minItems": 1,
                    "maxItems": 5,
                    "items": {
                        "type": "array",
                        "minItems": 2,
                        "maxItems": 2,
                        "items": {
                            "type": "integer",
                            "minimum": 0
                        }
                    }
                }
            }
        },
        "stenography": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "enabled": {"type": "boolean"},
                "protocol": {
                    "type": "string",
                    "enum": ["all", "geminipr", "txbolt"]
                }
            }
        },
        "ps2": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "enabled": {"type": "boolean"},
                "mouse_enabled": {"type": "boolean"},
                "clock_pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "data_pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "driver": {
                    "type": "string",
                    "enum": ["busywait", "interrupt", "usart", "vendor"]
                }
            }
        },
        "split": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "enabled": {"type": "boolean"},
                "bootmagic":{
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "matrix": {
                            "type": "array",
                            "minItems": 2,
                            "maxItems": 2,
                            "items": {
                                "type": "integer",
                                "minimum": 0
                            }
                        }
                    }
                },
                "matrix_pins": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "right": {
                            "type": "object",
                            "additionalProperties": false,
                            "properties": {
                                "direct": {
                                    "type": "array",
                                    "items": {"$ref": "qmk.definitions.v1#/mcu_pin_array"}
                                },
                                "cols": {"$ref": "qmk.definitions.v1#/mcu_pin_array"},
                                "rows": {"$ref": "qmk.definitions.v1#/mcu_pin_array"},
                                "unused": {"$ref": "qmk.definitions.v1#/mcu_pin_array"}
                            }
                        }
                    }
                },
                "dip_switch": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "right": {
                            "$ref": "#/definitions/dip_switch_config"
                        }
                    }
                },
                "encoder": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "right": {
                            "$ref": "#/definitions/encoder_config"
                        }
                    }
                },
                "handedness": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                        "matrix_grid": {
                            "$ref": "qmk.definitions.v1#/mcu_pin_array",
                            "minItems": 2,
                            "maxItems": 2
                        }
                    }
                },
                "soft_serial_pin": {
                    "$ref": "qmk.definitions.v1#/mcu_pin",
                    "$comment": "Deprecated: use split.serial.pin instead"
                },
                "soft_serial_speed": {
                    "type": "integer",
                    "minimum": 0,
                    "maximum": 5
                },
                "serial": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "driver": {
                            "type": "string",
                            "enum": ["bitbang", "usart", "vendor"]
                        },
                        "pin": {"$ref": "qmk.definitions.v1#/mcu_pin"}
                    }
                },
                "transport": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "protocol": {
                            "type": "string",
                            "enum": ["custom", "i2c", "serial"]
                        },
                        "sync": {
                            "type": "object",
                            "additionalProperties": false,
                            "properties": {
                                "activity": {"type": "boolean"},
                                "detected_os": {"type": "boolean"},
                                "haptic": {"type": "boolean"},
                                "layer_state": {"type": "boolean"},
                                "indicators": {"type": "boolean"},
                                "matrix_state": {"type": "boolean"},
                                "modifiers": {"type": "boolean"},
                                "oled": {"type": "boolean"},
                                "st7565": {"type": "boolean"},
                                "wpm": {"type": "boolean"}
                            }
                        },
                        "watchdog": {"type": "boolean"},
                        "watchdog_timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                        "sync_matrix_state": {
                            "type": "boolean",
                            "$comment": "Deprecated: use sync.matrix_state instead"
                        },
                        "sync_modifiers": {
                            "type": "boolean",
                            "$comment": "Deprecated: use sync.modifiers instead"
                        }
                    }
                },
                "usb_detect": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "enabled": {"type": "boolean"},
                        "polling_interval": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                        "timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"}
                    }
                },
                "main": {
                    "type": "string",
                    "enum": ["eeprom", "left", "matrix_grid", "pin", "right"],
                    "$comment": "Deprecated: use config.h options for now"
                },
                "matrix_grid": {
                    "type": "array",
                    "items": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                    "$comment": "Deprecated: use split.handedness.matrix_grid instead"
                }
            }
        },
        "tags": {
            "type": "array",
            "items": {"type": "string"}
        },
        "tapping": {
            "type": "object",
            "properties": {
                "chordal_hold": {"type": "boolean"},
                "force_hold": {"type": "boolean"},
                "force_hold_per_key": {"type": "boolean"},
                "ignore_mod_tap_interrupt": {"type": "boolean"},
                "hold_on_other_key_press": {"type": "boolean"},
                "hold_on_other_key_press_per_key": {"type": "boolean"},
                "permissive_hold": {"type": "boolean"},
                "permissive_hold_per_key": {"type": "boolean"},
                "retro": {"type": "boolean"},
                "retro_per_key": {"type": "boolean"},
                "term": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "term_per_key": {"type": "boolean"},
                "toggle": {"$ref": "qmk.definitions.v1#/unsigned_int"}
            }
        },
        "usb": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "device_ver": {
                    "$ref": "qmk.definitions.v1#/hex_number_4d",
                    "$comment": "Deprecated: use device_version instead"
                },
                "device_version": {"$ref": "qmk.definitions.v1#/bcd_version"},
                "force_nkro": {"type": "boolean"},
                "pid": {"$ref": "qmk.definitions.v1#/hex_number_4d"},
                "vid": {"$ref": "qmk.definitions.v1#/hex_number_4d"},
                "max_power": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "no_startup_check": {"type": "boolean"},
                "polling_interval": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "shared_endpoint": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "keyboard": {"type": "boolean"},
                        "mouse": {"type": "boolean"}
                    }
                },
                "suspend_wakeup_delay": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "wait_for_enumeration": {"type": "boolean"}
            }
        },
        "qmk": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "keys_per_scan": {"$ref": "qmk.definitions.v1#/unsigned_int_8"},
                "tap_keycode_delay": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "tap_capslock_delay": {"$ref": "qmk.definitions.v1#/unsigned_int"},
                "locking": {
                    "type": "object",
                    "additionalProperties": false,
                    "properties": {
                        "enabled": {"type": "boolean"},
                        "resync": {"type": "boolean"}
                    }
                }
            }
        },
        "qmk_lufa_bootloader": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "esc_output": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "esc_input": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "led": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "speaker": {"$ref": "qmk.definitions.v1#/mcu_pin"}
            }
        },
        "ws2812": {
            "type": "object",
            "additionalProperties": false,
            "properties": {
                "driver": {
                    "type": "string",
                    "enum": ["bitbang", "custom", "i2c", "pwm", "spi", "vendor"]
                },
                "pin": {"$ref": "qmk.definitions.v1#/mcu_pin"},
                "rgbw": {"type": "boolean"},
                "i2c_address": {"$ref": "qmk.definitions.v1#/hex_number_2d"},
                "i2c_timeout": {"$ref": "qmk.definitions.v1#/unsigned_int"}
            }
        }
    }
}
