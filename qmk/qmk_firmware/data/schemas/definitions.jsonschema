{"$schema": "https://json-schema.org/draft/2020-12/schema#", "$id": "qmk.definitions.v1", "title": "Common definitions used across QMK's jsonschemas.", "type": "object", "bcd_version": {"type": "string", "pattern": "^[0-9]{1,2}\\.[0-9]\\.[0-9]$"}, "bit": {"type": "integer", "minimum": 0, "maximum": 1}, "boolean_array": {"type": "object", "additionalProperties": {"type": "boolean"}}, "filename": {"type": "string", "minLength": 1, "pattern": "^[0-9a-z_]*$"}, "hex_number_2d": {"type": "string", "pattern": "^0x[0-9A-F]{2}$"}, "hex_number_4d": {"type": "string", "pattern": "^0x[0-9A-F]{4}$"}, "json_file_path": {"type": "string", "pattern": "^[0-9a-z_/\\-]+\\.json$"}, "key_unit": {"type": "number", "minimum": 0}, "keyboard": {"type": "string", "pattern": "^[0-9a-z][0-9a-z_/]*$"}, "keyboard_keymap_tuple": {"type": "array", "prefixItems": [{"$ref": "#/keyboard"}, {"$ref": "#/filename"}], "minItems": 2, "maxItems": 2, "unevaluatedItems": false}, "keyboard_keymap_env": {"type": "array", "prefixItems": [{"$ref": "#/keyboard"}, {"$ref": "#/filename"}, {"$ref": "#/kvp_object"}], "minItems": 3, "maxItems": 3, "unevaluatedItems": false}, "keycode": {"type": "string", "minLength": 2, "maxLength": 50, "pattern": "^[A-Z][A-Zs_0-9]*$"}, "keycode_decl": {"type": "object", "required": ["key"], "properties": {"key": {"$ref": "#/keycode"}, "label": {"$ref": "#/text_identifier"}, "aliases": {"type": "array", "minItems": 1, "items": {"$ref": "#/keycode_short"}}}}, "keycode_decl_array": {"type": "array", "minItems": 1, "items": {"$ref": "#/keycode_decl"}}, "keycode_short": {"type": "string", "minLength": 2, "maxLength": 7, "pattern": "^[A-Z][A-Zs_0-9]*$"}, "kvp_object": {"type": "object", "additionalProperties": {"type": "string"}}, "layout_macro": {"oneOf": [{"type": "string", "enum": ["LAYOUT", "LAYOUT_1x2uC", "LAYOUT_1x2uL", "LAYOUT_1x2uR", "LAYOUT_2x2uC", "LAYOUT_2x3uC", "LAYOUT_625uC", "LAYOUT_ortho_3x12_1x2uC", "LAYOUT_ortho_4x12_1x2uC", "LAYOUT_ortho_4x12_1x2uL", "LAYOUT_ortho_4x12_1x2uR", "LAYOUT_ortho_5x12_1x2uC", "LAYOUT_ortho_5x12_2x2uC", "LAYOUT_ortho_5x14_1x2uC", "LAYOUT_ortho_5x14_1x2uL", "LAYOUT_ortho_5x14_1x2uR", "LAYOUT_planck_1x2uC", "LAYOUT_planck_1x2uL", "LAYOUT_planck_1x2uR", "LAYOUT_preonic_1x2uC", "LAYOUT_preonic_1x2uL", "LAYOUT_preonic_1x2uR"]}, {"type": "string", "pattern": "^LAYOUT_[0-9a-z_]*$"}]}, "mcu_pin": {"oneOf": [{"type": "string", "enum": ["NO_PIN"]}, {"type": "string", "pattern": "^[A-K]\\d{1,2}$"}, {"type": "string", "pattern": "^LINE_PIN\\d{1,2}$"}, {"type": "string", "pattern": "^GP\\d{1,2}$"}, {"type": "integer"}, {"type": "null"}]}, "mcu_pin_array": {"type": "array", "items": {"$ref": "#/mcu_pin"}}, "signed_decimal": {"type": "number"}, "signed_int": {"type": "integer"}, "signed_int_8": {"type": "integer", "minimum": -127, "maximum": 127}, "snake_case": {"type": "string", "pattern": "^[a-z][a-z0-9_]*$"}, "string_array": {"type": "array", "items": {"type": "string"}}, "string_object": {"type": "object", "additionalProperties": {"type": "string"}}, "text_identifier": {"type": "string", "minLength": 1, "maxLength": 250}, "unsigned_decimal": {"type": "number", "minimum": 0}, "unsigned_int": {"type": "integer", "minimum": 0}, "unsigned_int_8": {"type": "integer", "minimum": 0, "maximum": 255}}