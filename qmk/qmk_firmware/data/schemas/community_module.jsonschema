{
    "$schema": "https://json-schema.org/draft/2020-12/schema#",
    "$id": "qmk.community_module.v1",
    "title": "Community Module Information",
    "type": "object",
    "required": ["module_name", "maintainer"]
    "properties": {
        "module_name": {"$ref": "qmk.definitions.v1#/text_identifier"},
        "maintainer": {"$ref": "qmk.definitions.v1#/text_identifier"},
        "url": {
            "type": "string",
            "format": "uri"
        },
        "keycodes": {"$ref": "qmk.definitions.v1#/keycode_decl_array"},
        "features": {"$ref": "qmk.keyboard.v1#/definitions/features_config"},
    }
}
