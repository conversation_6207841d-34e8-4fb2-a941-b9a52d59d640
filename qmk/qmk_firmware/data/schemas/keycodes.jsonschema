{
    "$schema": "https://json-schema.org/draft/2020-12/schema#",
    "$id": "qmk.keycodes.v1",
    "title": "Keycode Information",
    "type": "object",
    "definitions": {
        "define": {
            "type": "string",
            "minLength": 2,
            "maxLength": 50,
            "pattern": "^[A-Z][A-Zs_0-9]*$"
        }
    },
    "properties": {
        "ranges": {
            "type": "object",
            "propertyNames": {
                "type": "string"
            },
            "additionalProperties": {
                "type": "object",
                "required": [
                    "define"
                ],
                "properties": {
                    "define": {"$ref": "#/definitions/define"}
                }
            }
        },
        "keycodes": {
            "type": "object",
            "propertyNames": {
                "$ref": "qmk.definitions.v1#/hex_number_4d"
            },
            "additionalProperties": {
                "type": "object", // use 'qmk.definitions.v1#/keycode_decl' when problem keycodes are removed
                "required": [
                    "key"
                ],
                "properties": {
                    "key": {"$ref": "#/definitions/define"},
                    "aliases": {
                        "type": "array",
                        "minItems": 1,
                        "items": {
                            "type": "string"
                        }
                    }
                }
            }
        }
    }
}
