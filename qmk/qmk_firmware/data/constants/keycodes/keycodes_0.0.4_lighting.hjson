{"keycodes": {"0x7810": {"group": "led_matrix", "key": "QK_LED_MATRIX_ON", "aliases": ["LM_ON"]}, "0x7811": {"group": "led_matrix", "key": "QK_LED_MATRIX_OFF", "aliases": ["LM_OFF"]}, "0x7812": {"group": "led_matrix", "key": "QK_LED_MATRIX_TOGGLE", "aliases": ["LM_TOGG"]}, "0x7813": {"group": "led_matrix", "key": "QK_LED_MATRIX_MODE_NEXT", "aliases": ["LM_NEXT"]}, "0x7814": {"group": "led_matrix", "key": "QK_LED_MATRIX_MODE_PREVIOUS", "aliases": ["LM_PREV"]}, "0x7815": {"group": "led_matrix", "key": "QK_LED_MATRIX_BRIGHTNESS_UP", "aliases": ["LM_BRIU"]}, "0x7816": {"group": "led_matrix", "key": "QK_LED_MATRIX_BRIGHTNESS_DOWN", "aliases": ["LM_BRID"]}, "0x7817": {"group": "led_matrix", "key": "QK_LED_MATRIX_SPEED_UP", "aliases": ["LM_SPDU"]}, "0x7818": {"group": "led_matrix", "key": "QK_LED_MATRIX_SPEED_DOWN", "aliases": ["LM_SPDD"]}, "0x7820": {"group": "underglow", "key": "QK_UNDERGLOW_TOGGLE", "aliases": ["UG_TOGG"]}, "0x7821": {"group": "underglow", "key": "QK_UNDERGLOW_MODE_NEXT", "aliases": ["!reset!", "UG_NEXT"]}, "0x7822": {"group": "underglow", "key": "QK_UNDERGLOW_MODE_PREVIOUS", "aliases": ["!reset!", "UG_PREV"]}, "0x7823": {"group": "underglow", "key": "QK_UNDERGLOW_HUE_UP", "aliases": ["UG_HUEU"]}, "0x7824": {"group": "underglow", "key": "QK_UNDERGLOW_HUE_DOWN", "aliases": ["UG_HUED"]}, "0x7825": {"group": "underglow", "key": "QK_UNDERGLOW_SATURATION_UP", "aliases": ["UG_SATU"]}, "0x7826": {"group": "underglow", "key": "QK_UNDERGLOW_SATURATION_DOWN", "aliases": ["UG_SATD"]}, "0x7827": {"group": "underglow", "key": "QK_UNDERGLOW_VALUE_UP", "aliases": ["UG_VALU"]}, "0x7828": {"group": "underglow", "key": "QK_UNDERGLOW_VALUE_DOWN", "aliases": ["UG_VALD"]}, "0x7829": {"group": "underglow", "key": "QK_UNDERGLOW_SPEED_UP", "aliases": ["UG_SPDU"]}, "0x782A": {"group": "underglow", "key": "QK_UNDERGLOW_SPEED_DOWN", "aliases": ["UG_SPDD"]}, "0x7840": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_ON", "aliases": ["RM_ON"]}, "0x7841": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_OFF", "aliases": ["RM_OFF"]}, "0x7842": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_TOGGLE", "aliases": ["RM_TOGG"]}, "0x7843": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_MODE_NEXT", "aliases": ["RM_NEXT"]}, "0x7844": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_MODE_PREVIOUS", "aliases": ["RM_PREV"]}, "0x7845": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_HUE_UP", "aliases": ["RM_HUEU"]}, "0x7846": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_HUE_DOWN", "aliases": ["RM_HUED"]}, "0x7847": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_SATURATION_UP", "aliases": ["RM_SATU"]}, "0x7848": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_SATURATION_DOWN", "aliases": ["RM_SATD"]}, "0x7849": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_VALUE_UP", "aliases": ["RM_VALU"]}, "0x784A": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_VALUE_DOWN", "aliases": ["RM_VALD"]}, "0x784B": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_SPEED_UP", "aliases": ["RM_SPDU"]}, "0x784C": {"group": "rgb_matrix", "key": "QK_RGB_MATRIX_SPEED_DOWN", "aliases": ["RM_SPDD"]}}}