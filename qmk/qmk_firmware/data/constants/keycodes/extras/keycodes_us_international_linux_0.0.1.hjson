{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ [ │ ] │  \  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ´ │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "US_DGRV",
            "label": "` (dead)",
        }
        "KC_1": {
            "key": "US_1",
            "label": "1",
        }
        "KC_2": {
            "key": "US_2",
            "label": "2",
        }
        "KC_3": {
            "key": "US_3",
            "label": "3",
        }
        "KC_4": {
            "key": "US_4",
            "label": "4",
        }
        "KC_5": {
            "key": "US_5",
            "label": "5",
        }
        "KC_6": {
            "key": "US_6",
            "label": "6",
        }
        "KC_7": {
            "key": "US_7",
            "label": "7",
        }
        "KC_8": {
            "key": "US_8",
            "label": "8",
        }
        "KC_9": {
            "key": "US_9",
            "label": "9",
        }
        "KC_0": {
            "key": "US_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "US_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "US_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "US_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "US_W",
            "label": "W",
        }
        "KC_E": {
            "key": "US_E",
            "label": "E",
        }
        "KC_R": {
            "key": "US_R",
            "label": "R",
        }
        "KC_T": {
            "key": "US_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "US_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "US_U",
            "label": "U",
        }
        "KC_I": {
            "key": "US_I",
            "label": "I",
        }
        "KC_O": {
            "key": "US_O",
            "label": "O",
        }
        "KC_P": {
            "key": "US_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "US_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "US_RBRC",
            "label": "]",
        }
        "KC_BSLS": {
            "key": "US_BSLS",
            "label": "\\",
        }
        "KC_A": {
            "key": "US_A",
            "label": "A",
        }
        "KC_S": {
            "key": "US_S",
            "label": "S",
        }
        "KC_D": {
            "key": "US_D",
            "label": "D",
        }
        "KC_F": {
            "key": "US_F",
            "label": "F",
        }
        "KC_G": {
            "key": "US_G",
            "label": "G",
        }
        "KC_H": {
            "key": "US_H",
            "label": "H",
        }
        "KC_J": {
            "key": "US_J",
            "label": "J",
        }
        "KC_K": {
            "key": "US_K",
            "label": "K",
        }
        "KC_L": {
            "key": "US_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "US_SCLN",
            "label": ";",
        }
        "KC_QUOT": {
            "key": "US_ACUT",
            "label": "´ (dead)",
        }
        "KC_Z": {
            "key": "US_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "US_X",
            "label": "X",
        }
        "KC_C": {
            "key": "US_C",
            "label": "C",
        }
        "KC_V": {
            "key": "US_V",
            "label": "V",
        }
        "KC_B": {
            "key": "US_B",
            "label": "B",
        }
        "KC_N": {
            "key": "US_N",
            "label": "N",
        }
        "KC_M": {
            "key": "US_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "US_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "US_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "US_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │ : │ ¨ │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(US_DGRV)": {
            "key": "US_DTIL",
            "label": "~ (dead)",
        }
        "S(US_1)": {
            "key": "US_EXLM",
            "label": "!",
        }
        "S(US_2)": {
            "key": "US_AT",
            "label": "@",
        }
        "S(US_3)": {
            "key": "US_HASH",
            "label": "#",
        }
        "S(US_4)": {
            "key": "US_DLR",
            "label": "$",
        }
        "S(US_5)": {
            "key": "US_PERC",
            "label": "%",
        }
        "S(US_6)": {
            "key": "US_DCIR",
            "label": "^ (dead)",
        }
        "S(US_7)": {
            "key": "US_AMPR",
            "label": "&",
        }
        "S(US_8)": {
            "key": "US_ASTR",
            "label": "*",
        }
        "S(US_9)": {
            "key": "US_LPRN",
            "label": "(",
        }
        "S(US_0)": {
            "key": "US_RPRN",
            "label": ")",
        }
        "S(US_MINS)": {
            "key": "US_UNDS",
            "label": "_",
        }
        "S(US_EQL)": {
            "key": "US_PLUS",
            "label": "+",
        }
        "S(US_LBRC)": {
            "key": "US_LCBR",
            "label": "{",
        }
        "S(US_RBRC)": {
            "key": "US_RCBR",
            "label": "}",
        }
        "S(US_BSLS)": {
            "key": "US_PIPE",
            "label": "|",
        }
        "S(US_SCLN)": {
            "key": "US_COLN",
            "label": ":",
        }
        "S(US_ACUT)": {
            "key": "US_DIAE",
            "label": "¨ (dead)",
        }
        "S(US_COMM)": {
            "key": "US_LABK",
            "label": "<",
        }
        "S(US_DOT)": {
            "key": "US_RABK",
            "label": ">",
        }
        "S(US_SLSH)": {
            "key": "US_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ ¡ │ ² │ ³ │ ¤ │ € │ ¼ │ ½ │ ¾ │ ‘ │ ’ │ ¥ │ × │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Ä │ Å │ É │ ® │ Þ │ Ü │ Ú │ Í │ Ó │ Ö │ « │ » │  ¬  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ Á │ ß │ Ð │   │   │   │   │ Œ │ Ø │ ¶ │ ' │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Æ │   │ © │   │   │ Ñ │ µ │ Ç │ ˙ │ ¿ │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(US_DGRV)": {
            "key": "US_GRV",
            "label": "`",
        }
        "ALGR(US_1)": {
            "key": "US_IEXL",
            "label": "¡",
        }
        "ALGR(US_2)": {
            "key": "US_SUP2",
            "label": "²",
        }
        "ALGR(US_3)": {
            "key": "US_SUP3",
            "label": "³",
        }
        "ALGR(US_4)": {
            "key": "US_CURR",
            "label": "¤",
        }
        "ALGR(US_5)": {
            "key": "US_EURO",
            "label": "€",
        }
        "ALGR(US_6)": {
            "key": "US_QRTR",
            "label": "¼",
        }
        "ALGR(US_7)": {
            "key": "US_HALF",
            "label": "½",
        }
        "ALGR(US_8)": {
            "key": "US_TQTR",
            "label": "¾",
        }
        "ALGR(US_9)": {
            "key": "US_LSQU",
            "label": "‘",
        }
        "ALGR(US_0)": {
            "key": "US_RSQU",
            "label": "’",
        }
        "ALGR(US_MINS)": {
            "key": "US_YEN",
            "label": "¥",
        }
        "ALGR(US_EQL)": {
            "key": "US_MUL",
            "label": "×",
        }
        "ALGR(US_Q)": {
            "key": "US_ADIA",
            "label": "Ä",
        }
        "ALGR(US_W)": {
            "key": "US_ARNG",
            "label": "Å",
        }
        "ALGR(US_E)": {
            "key": "US_EACU",
            "label": "É",
        }
        "ALGR(US_R)": {
            "key": "US_REGD",
            "label": "®",
        }
        "ALGR(US_T)": {
            "key": "US_THRN",
            "label": "Þ",
        }
        "ALGR(US_Y)": {
            "key": "US_UDIA",
            "label": "Ü",
        }
        "ALGR(US_U)": {
            "key": "US_UACU",
            "label": "Ú",
        }
        "ALGR(US_I)": {
            "key": "US_IACU",
            "label": "Í",
        }
        "ALGR(US_O)": {
            "key": "US_OACU",
            "label": "Ó",
        }
        "ALGR(US_P)": {
            "key": "US_ODIA",
            "label": "Ö",
        }
        "ALGR(US_LBRC)": {
            "key": "US_LDAQ",
            "label": "«",
        }
        "ALGR(US_RBRC)": {
            "key": "US_RDAQ",
            "label": "»",
        }
        "ALGR(US_BSLS)": {
            "key": "US_NOT",
            "label": "¬",
        }
        "ALGR(US_A)": {
            "key": "US_AACU",
            "label": "Á",
        }
        "ALGR(US_S)": {
            "key": "US_SS",
            "label": "ß",
        }
        "ALGR(US_D)": {
            "key": "US_ETH",
            "label": "Ð",
        }
        "ALGR(US_K)": {
            "key": "US_OE",
            "label": "Œ",
        }
        "ALGR(US_L)": {
            "key": "US_OSTR",
            "label": "Ø",
        }
        "ALGR(US_SCLN)": {
            "key": "US_PILC",
            "label": "¶",
        }
        "ALGR(US_ACUT)": {
            "key": "US_QUOT",
            "label": "'",
        }
        "ALGR(US_Z)": {
            "key": "US_AE",
            "label": "Æ",
        }
        "ALGR(US_C)": {
            "key": "US_COPY",
            "label": "©",
        }
        "ALGR(US_N)": {
            "key": "US_NTIL",
            "label": "Ñ",
        }
        "ALGR(US_M)": {
            "key": "US_MICR",
            "label": "µ",
        }
        "ALGR(US_COMM)": {
            "key": "US_CCED",
            "label": "Ç",
        }
        "ALGR(US_DOT)": {
            "key": "US_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(US_SLSH)": {
            "key": "US_IQUE",
            "label": "¿",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ¹ │ ˝ │ ¯ │ £ │ ¸ │ ^ │ ̛  │ ˛ │ ˘ │ ° │ ̣  │ ÷ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ “ │ ” │  ¦  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │ § │   │   │   │   │   │   │   │ ° │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │ ¢ │   │   │   │   │   │ ˇ │ ̉  │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(US_DGRV))": {
            "key": "US_TILD",
            "label": "~",
        }
        "S(ALGR(US_1))": {
            "key": "US_SUP1",
            "label": "¹",
        }
        "S(ALGR(US_2))": {
            "key": "US_DACU",
            "label": "˝ (dead)",
        }
        "S(ALGR(US_3))": {
            "key": "US_MACR",
            "label": "¯ (dead)",
        }
        "S(ALGR(US_4))": {
            "key": "US_PND",
            "label": "£",
        }
        "S(ALGR(US_5))": {
            "key": "US_CEDL",
            "label": "¸ (dead)",
        }
        "S(ALGR(US_6))": {
            "key": "US_CIRC",
            "label": "^",
        }
        "S(ALGR(US_7))": {
            "key": "US_HORN",
            "label": "̛ (dead)",
        }
        "S(ALGR(US_8))": {
            "key": "US_OGON",
            "label": "˛ (dead)",
        }
        "S(ALGR(US_9))": {
            "key": "US_BREV",
            "label": "˘ (dead)",
        }
        "S(ALGR(US_0))": {
            "key": "US_RNGA",
            "label": "° (dead)",
        }
        "S(ALGR(US_MINS))": {
            "key": "US_DOTB",
            "label": "̣ (dead)",
        }
        "S(ALGR(US_EQL))": {
            "key": "US_DIV",
            "label": "÷",
        }
        "S(ALGR(US_LBRC))": {
            "key": "US_LDQU",
            "label": "“",
        }
        "S(ALGR(US_RBRC))": {
            "key": "US_RDQU",
            "label": "”",
        }
        "S(ALGR(US_BSLS))": {
            "key": "US_BRKP",
            "label": "¦",
        }
        "S(ALGR(US_S))": {
            "key": "US_SECT",
            "label": "§",
        }
        "S(ALGR(US_SCLN))": {
            "key": "US_DEG",
            "label": "°",
        }
        "S(ALGR(US_ACUT))": {
            "key": "US_DQUO",
            "label": "\"",
        }
        "S(ALGR(US_C))": {
            "key": "US_CENT",
            "label": "¢",
        }
        "S(ALGR(US_DOT))": {
            "key": "US_CARN",
            "label": "ˇ (dead)",
        }
        "S(ALGR(US_SLSH))": {
            "key": "US_HOKA",
            "label": "̉ (dead)",
        }
    }
}