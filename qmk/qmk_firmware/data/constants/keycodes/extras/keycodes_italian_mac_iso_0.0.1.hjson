{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ \ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ ì │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ è │ + │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ò │ à │ ù │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "KC_GRV": {
            "key": "IT_BSLS",
            "label": "\\",
        }
        "KC_1": {
            "key": "IT_1",
            "label": "1",
        }
        "KC_2": {
            "key": "IT_2",
            "label": "2",
        }
        "KC_3": {
            "key": "IT_3",
            "label": "3",
        }
        "KC_4": {
            "key": "IT_4",
            "label": "4",
        }
        "KC_5": {
            "key": "IT_5",
            "label": "5",
        }
        "KC_6": {
            "key": "IT_6",
            "label": "6",
        }
        "KC_7": {
            "key": "IT_7",
            "label": "7",
        }
        "KC_8": {
            "key": "IT_8",
            "label": "8",
        }
        "KC_9": {
            "key": "IT_9",
            "label": "9",
        }
        "KC_0": {
            "key": "IT_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "IT_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "IT_IGRV",
            "label": "ì",
        }
        "KC_Q": {
            "key": "IT_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "IT_W",
            "label": "W",
        }
        "KC_E": {
            "key": "IT_E",
            "label": "E",
        }
        "KC_R": {
            "key": "IT_R",
            "label": "R",
        }
        "KC_T": {
            "key": "IT_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "IT_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "IT_U",
            "label": "U",
        }
        "KC_I": {
            "key": "IT_I",
            "label": "I",
        }
        "KC_O": {
            "key": "IT_O",
            "label": "O",
        }
        "KC_P": {
            "key": "IT_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "IT_EGRV",
            "label": "è",
        }
        "KC_RBRC": {
            "key": "IT_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "IT_A",
            "label": "A",
        }
        "KC_S": {
            "key": "IT_S",
            "label": "S",
        }
        "KC_D": {
            "key": "IT_D",
            "label": "D",
        }
        "KC_F": {
            "key": "IT_F",
            "label": "F",
        }
        "KC_G": {
            "key": "IT_G",
            "label": "G",
        }
        "KC_H": {
            "key": "IT_H",
            "label": "H",
        }
        "KC_J": {
            "key": "IT_J",
            "label": "J",
        }
        "KC_K": {
            "key": "IT_K",
            "label": "K",
        }
        "KC_L": {
            "key": "IT_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "IT_OGRV",
            "label": "ò",
        }
        "KC_QUOT": {
            "key": "IT_AGRV",
            "label": "à",
        }
        "KC_NUHS": {
            "key": "IT_UGRV",
            "label": "ù",
        }
        "KC_NUBS": {
            "key": "IT_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "IT_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "IT_X",
            "label": "X",
        }
        "KC_C": {
            "key": "IT_C",
            "label": "C",
        }
        "KC_V": {
            "key": "IT_V",
            "label": "V",
        }
        "KC_B": {
            "key": "IT_B",
            "label": "B",
        }
        "KC_N": {
            "key": "IT_N",
            "label": "N",
        }
        "KC_M": {
            "key": "IT_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "IT_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "IT_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "IT_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ | │ ! │ " │ £ │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ ^ │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │   │   │   │   │   │   │   │   │   │   │ é │ * │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │   │   │   │   │   │   │   │   │ ç │ ° │ § │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(IT_BSLS)": {
            "key": "IT_PIPE",
            "label": "|",
        }
        "S(IT_1)": {
            "key": "IT_EXLM",
            "label": "!",
        }
        "S(IT_2)": {
            "key": "IT_DQUO",
            "label": "\"",
        }
        "S(IT_3)": {
            "key": "IT_PND",
            "label": "£",
        }
        "S(IT_4)": {
            "key": "IT_DLR",
            "label": "$",
        }
        "S(IT_5)": {
            "key": "IT_PERC",
            "label": "%",
        }
        "S(IT_6)": {
            "key": "IT_AMPR",
            "label": "&",
        }
        "S(IT_7)": {
            "key": "IT_SLSH",
            "label": "/",
        }
        "S(IT_8)": {
            "key": "IT_LPRN",
            "label": "(",
        }
        "S(IT_9)": {
            "key": "IT_RPRN",
            "label": ")",
        }
        "S(IT_0)": {
            "key": "IT_EQL",
            "label": "=",
        }
        "S(IT_QUOT)": {
            "key": "IT_QUES",
            "label": "?",
        }
        "S(IT_IGRV)": {
            "key": "IT_CIRC",
            "label": "^",
        }
        "S(IT_EGRV)": {
            "key": "IT_EACU",
            "label": "é",
        }
        "S(IT_PLUS)": {
            "key": "IT_ASTR",
            "label": "*",
        }
        "S(IT_OGRV)": {
            "key": "IT_LCCE",
            "label": "ç",
        }
        "S(IT_AGRV)": {
            "key": "IT_DEG",
            "label": "°",
        }
        "S(IT_UGRV)": {
            "key": "IT_SECT",
            "label": "§",
        }
        "S(IT_LABK)": {
            "key": "IT_RABK",
            "label": ">",
        }
        "S(IT_COMM)": {
            "key": "IT_SCLN",
            "label": ";",
        }
        "S(IT_DOT)": {
            "key": "IT_COLN",
            "label": ":",
        }
        "S(IT_MINS)": {
            "key": "IT_UNDS",
            "label": "_",
        }
/* Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ ` │ « │ “ │ ‘ │ ¥ │ ~ │ ‹ │ ÷ │ ´ │ ` │ ≠ │ ¡ │ ˆ │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ „ │ Ω │ € │ ® │ ™ │ Æ │ ¨ │ Œ │ Ø │ π │ [ │ ] │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ Å │ ß │ ∂ │ ƒ │ ∞ │ ∆ │ ª │ º │ ¬ │ @ │ # │ ¶ │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≤ │ ∑ │ † │ © │ √ │ ∫ │ ˜ │ µ │ … │ • │ – │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "A(IT_BSLS)": {
            "key": "IT_GRV",
            "label": "`",
        }
        "A(IT_1)": {
            "key": "IT_LDAQ",
            "label": "«",
        }
        "A(IT_2)": {
            "key": "IT_LDQU",
            "label": "“",
        }
        "A(IT_3)": {
            "key": "IT_LSQU",
            "label": "‘",
        }
        "A(IT_4)": {
            "key": "IT_YEN",
            "label": "¥",
        }
        "A(IT_5)": {
            "key": "IT_TILD",
            "label": "~",
        }
        "A(IT_6)": {
            "key": "IT_LSAQ",
            "label": "‹",
        }
        "A(IT_7)": {
            "key": "IT_DIV",
            "label": "÷",
        }
        "A(IT_8)": {
            "key": "IT_ACUT",
            "label": "´ (dead)",
        }
        "A(IT_9)": {
            "key": "IT_DGRV",
            "label": "` (dead)",
        }
        "A(IT_0)": {
            "key": "IT_NEQL",
            "label": "≠",
        }
        "A(IT_QUOT)": {
            "key": "IT_IEXL",
            "label": "¡",
        }
        "A(IT_IGRV)": {
            "key": "IT_DCIR",
            "label": "ˆ (dead)",
        }
        "A(IT_Q)": {
            "key": "IT_DLQU",
            "label": "„",
        }
        "A(IT_W)": {
            "key": "IT_OMEG",
            "label": "Ω",
        }
        "A(IT_E)": {
            "key": "IT_EURO",
            "label": "€",
        }
        "A(IT_R)": {
            "key": "IT_REGD",
            "label": "®",
        }
        "A(IT_T)": {
            "key": "IT_TM",
            "label": "™",
        }
        "A(IT_Y)": {
            "key": "IT_AE",
            "label": "Æ",
        }
        "A(IT_U)": {
            "key": "IT_DIAE",
            "label": "¨ (dead)",
        }
        "A(IT_I)": {
            "key": "IT_OE",
            "label": "Œ",
        }
        "A(IT_O)": {
            "key": "IT_OSTR",
            "label": "Ø",
        }
        "A(IT_P)": {
            "key": "IT_PI",
            "label": "π",
        }
        "A(IT_EGRV)": {
            "key": "IT_LBRC",
            "label": "[",
        }
        "A(IT_PLUS)": {
            "key": "IT_RBRC",
            "label": "]",
        }
        "A(IT_A)": {
            "key": "IT_ARNG",
            "label": "Å",
        }
        "A(IT_S)": {
            "key": "IT_SS",
            "label": "ß",
        }
        "A(IT_D)": {
            "key": "IT_PDIF",
            "label": "∂",
        }
        "A(IT_F)": {
            "key": "IT_FHK",
            "label": "ƒ",
        }
        "A(IT_G)": {
            "key": "IT_INFN",
            "label": "∞",
        }
        "A(IT_H)": {
            "key": "IT_INCR",
            "label": "∆",
        }
        "A(IT_J)": {
            "key": "IT_FORD",
            "label": "ª",
        }
        "A(IT_K)": {
            "key": "IT_MORD",
            "label": "º",
        }
        "A(IT_L)": {
            "key": "IT_NOT",
            "label": "¬",
        }
        "A(IT_OGRV)": {
            "key": "IT_AT",
            "label": "@",
        }
        "A(IT_AGRV)": {
            "key": "IT_HASH",
            "label": "#",
        }
        "A(IT_UGRV)": {
            "key": "IT_PILC",
            "label": "¶",
        }
        "A(IT_LABK)": {
            "key": "IT_LTEQ",
            "label": "≤",
        }
        "A(IT_Z)": {
            "key": "IT_NARS",
            "label": "∑",
        }
        "A(IT_X)": {
            "key": "IT_DAGG",
            "label": "†",
        }
        "A(IT_C)": {
            "key": "IT_COPY",
            "label": "©",
        }
        "A(IT_V)": {
            "key": "IT_SQRT",
            "label": "√",
        }
        "A(IT_B)": {
            "key": "IT_INTG",
            "label": "∫",
        }
        "A(IT_N)": {
            "key": "IT_STIL",
            "label": "˜ (dead)",
        }
        "A(IT_M)": {
            "key": "IT_MICR",
            "label": "µ",
        }
        "A(IT_COMM)": {
            "key": "IT_ELLP",
            "label": "…",
        }
        "A(IT_DOT)": {
            "key": "IT_BULT",
            "label": "•",
        }
        "A(IT_MINS)": {
            "key": "IT_NDSH",
            "label": "–",
        }
/* Shift+Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ ı │ » │ ” │ ’ │ ¢ │ ‰ │ › │ ⁄ │  │   │ ≈ │ ¿ │ ± │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ ‚ │ À │ È │ Ì │ Ò │   │ Ù │   │   │ ∏ │ { │ } │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │ ¯ │ ˘ │ ˙ │ ˚ │ ¸ │ ˝ │ ˛ │ ˇ │ Ç │   │ ◊ │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≥ │   │ ‡ │ Á │ É │ Í │ Ó │ Ú │   │ · │ — │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(A(IT_BSLS))": {
            "key": "IT_DLSI",
            "label": "ı",
        }
        "S(A(IT_1))": {
            "key": "IT_RDAQ",
            "label": "»",
        }
        "S(A(IT_2))": {
            "key": "IT_RDQU",
            "label": "”",
        }
        "S(A(IT_3))": {
            "key": "IT_RSQU",
            "label": "’",
        }
        "S(A(IT_4))": {
            "key": "IT_CENT",
            "label": "¢",
        }
        "S(A(IT_5))": {
            "key": "IT_PERM",
            "label": "‰",
        }
        "S(A(IT_6))": {
            "key": "IT_RSAQ",
            "label": "›",
        }
        "S(A(IT_7))": {
            "key": "IT_FRSL",
            "label": "⁄",
        }
        "S(A(IT_8))": {
            "key": "IT_APPL",
            "label": " (Apple logo)",
        }
        "S(A(IT_0))": {
            "key": "IT_AEQL",
            "label": "≈",
        }
        "S(A(IT_QUOT))": {
            "key": "IT_IQUE",
            "label": "¿",
        }
        "S(A(IT_IGRV))": {
            "key": "IT_PLMN",
            "label": "±",
        }
        "S(A(IT_Q))": {
            "key": "IT_SLQU",
            "label": "‚",
        }
        "S(A(IT_W))": {
            "key": "IT_CAGR",
            "label": "À",
        }
        "S(A(IT_E))": {
            "key": "IT_CEGR",
            "label": "È",
        }
        "S(A(IT_R))": {
            "key": "IT_CIGR",
            "label": "Ì",
        }
        "S(A(IT_T))": {
            "key": "IT_COGR",
            "label": "Ò",
        }
        "S(A(IT_U))": {
            "key": "IT_CUGR",
            "label": "Ù",
        }
        "S(A(IT_P))": {
            "key": "IT_NARP",
            "label": "∏",
        }
        "S(A(IT_EGRV))": {
            "key": "IT_LCBR",
            "label": "{",
        }
        "S(A(IT_PLUS))": {
            "key": "IT_RCBR",
            "label": "}",
        }
        "S(A(IT_S))": {
            "key": "IT_MACR",
            "label": "¯",
        }
        "S(A(IT_D))": {
            "key": "IT_BREV",
            "label": "˘",
        }
        "S(A(IT_F))": {
            "key": "IT_DOTA",
            "label": "˙",
        }
        "S(A(IT_G))": {
            "key": "IT_RNGA",
            "label": "˚",
        }
        "S(A(IT_H))": {
            "key": "IT_CEDL",
            "label": "¸",
        }
        "S(A(IT_J))": {
            "key": "IT_DACU",
            "label": "˝",
        }
        "S(A(IT_K))": {
            "key": "IT_OGON",
            "label": "˛",
        }
        "S(A(IT_L))": {
            "key": "IT_CARN",
            "label": "ˇ",
        }
        "S(A(IT_OGRV))": {
            "key": "IT_CCCE",
            "label": "Ç",
        }
        "S(A(IT_UGRV))": {
            "key": "IT_LOZN",
            "label": "◊",
        }
        "S(A(IT_LABK))": {
            "key": "IT_GTEQ",
            "label": "≥",
        }
        "S(A(IT_X))": {
            "key": "IT_DDAG",
            "label": "‡",
        }
        "S(A(IT_C))": {
            "key": "IT_CAAC",
            "label": "Á",
        }
        "S(A(IT_V))": {
            "key": "IT_CEAC",
            "label": "É",
        }
        "S(A(IT_B))": {
            "key": "IT_CIAC",
            "label": "Í",
        }
        "S(A(IT_N))": {
            "key": "IT_COAC",
            "label": "Ó",
        }
        "S(A(IT_M))": {
            "key": "IT_CUAC",
            "label": "Ú",
        }
        "S(A(IT_DOT))": {
            "key": "IT_MDDT",
            "label": "·",
        }
        "S(A(IT_MINS))": {
            "key": "IT_MDSH",
            "label": "—",
        }
    }
}