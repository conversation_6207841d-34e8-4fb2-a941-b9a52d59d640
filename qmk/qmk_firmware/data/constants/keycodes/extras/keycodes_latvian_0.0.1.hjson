{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ' │ \ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "LV_GRV",
            "label": "`",
        }
        "KC_1": {
            "key": "LV_1",
            "label": "1",
        }
        "KC_2": {
            "key": "LV_2",
            "label": "2",
        }
        "KC_3": {
            "key": "LV_3",
            "label": "3",
        }
        "KC_4": {
            "key": "LV_4",
            "label": "4",
        }
        "KC_5": {
            "key": "LV_5",
            "label": "5",
        }
        "KC_6": {
            "key": "LV_6",
            "label": "6",
        }
        "KC_7": {
            "key": "LV_7",
            "label": "7",
        }
        "KC_8": {
            "key": "LV_8",
            "label": "8",
        }
        "KC_9": {
            "key": "LV_9",
            "label": "9",
        }
        "KC_0": {
            "key": "LV_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "LV_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "LV_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "LV_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "LV_W",
            "label": "W",
        }
        "KC_E": {
            "key": "LV_E",
            "label": "E",
        }
        "KC_R": {
            "key": "LV_R",
            "label": "R",
        }
        "KC_T": {
            "key": "LV_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "LV_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "LV_U",
            "label": "U",
        }
        "KC_I": {
            "key": "LV_I",
            "label": "I",
        }
        "KC_O": {
            "key": "LV_O",
            "label": "O",
        }
        "KC_P": {
            "key": "LV_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "LV_LBRC",
            "label": "[",
        }
        "KC_RBRC": {
            "key": "LV_RBRC",
            "label": "]",
        }
        "KC_A": {
            "key": "LV_A",
            "label": "A",
        }
        "KC_S": {
            "key": "LV_S",
            "label": "S",
        }
        "KC_D": {
            "key": "LV_D",
            "label": "D",
        }
        "KC_F": {
            "key": "LV_F",
            "label": "F",
        }
        "KC_G": {
            "key": "LV_G",
            "label": "G",
        }
        "KC_H": {
            "key": "LV_H",
            "label": "H",
        }
        "KC_J": {
            "key": "LV_J",
            "label": "J",
        }
        "KC_K": {
            "key": "LV_K",
            "label": "K",
        }
        "KC_L": {
            "key": "LV_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "LV_SCLN",
            "label": ";",
        }
        "KC_QUOT": {
            "key": "LV_QUOT",
            "label": "' (dead)",
        }
        "KC_NUHS": {
            "key": "LV_BSLS",
            "label": "\\",
        }
        "KC_NUBS": {
            "key": "LV_NUBS",
            "label": "\\",
        }
        "KC_Z": {
            "key": "LV_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "LV_X",
            "label": "X",
        }
        "KC_C": {
            "key": "LV_C",
            "label": "C",
        }
        "KC_V": {
            "key": "LV_V",
            "label": "V",
        }
        "KC_B": {
            "key": "LV_B",
            "label": "B",
        }
        "KC_N": {
            "key": "LV_N",
            "label": "N",
        }
        "KC_M": {
            "key": "LV_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "LV_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "LV_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "LV_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ : │ " │ | │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(LV_GRV)": {
            "key": "LV_TILD",
            "label": "~",
        }
        "S(LV_1)": {
            "key": "LV_EXLM",
            "label": "!",
        }
        "S(LV_2)": {
            "key": "LV_AT",
            "label": "@",
        }
        "S(LV_3)": {
            "key": "LV_HASH",
            "label": "#",
        }
        "S(LV_4)": {
            "key": "LV_DLR",
            "label": "$",
        }
        "S(LV_5)": {
            "key": "LV_PERC",
            "label": "%",
        }
        "S(LV_6)": {
            "key": "LV_CIRC",
            "label": "^",
        }
        "S(LV_7)": {
            "key": "LV_AMPR",
            "label": "&",
        }
        "S(LV_8)": {
            "key": "LV_ASTR",
            "label": "*",
        }
        "S(LV_9)": {
            "key": "LV_LPRN",
            "label": "(",
        }
        "S(LV_0)": {
            "key": "LV_RPRN",
            "label": ")",
        }
        "S(LV_MINS)": {
            "key": "LV_UNDS",
            "label": "_",
        }
        "S(LV_EQL)": {
            "key": "LV_PLUS",
            "label": "+",
        }
        "S(LV_LBRC)": {
            "key": "LV_LCBR",
            "label": "{",
        }
        "S(LV_RBRC)": {
            "key": "LV_RCBR",
            "label": "}",
        }
        "S(LV_SCLN)": {
            "key": "LV_COLN",
            "label": ":",
        }
        "S(LV_QUOT)": {
            "key": "LV_DQUO",
            "label": "\" (dead)",
        }
        "S(LV_BSLS)": {
            "key": "LV_PIPE",
            "label": "|",
        }
        "S(LV_COMM)": {
            "key": "LV_LABK",
            "label": "<",
        }
        "S(LV_DOT)": {
            "key": "LV_RABK",
            "label": ">",
        }
        "S(LV_SLSH)": {
            "key": "LV_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ - │   │ « │ » │ € │   │ ’ │   │   │   │   │ – │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ Ē │ Ŗ │   │   │ Ū │ Ī │ Ō │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Ā │ Š │   │   │ Ģ │   │   │ Ķ │ Ļ │   │ ´ │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │ Ž │   │ Č │   │   │ Ņ │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(LV_GRV)": {
            "key": "LV_SHYP",
            "label": "­ (soft hyphen)",
        }
        "ALGR(LV_1)": {
            "key": "LV_NBSP",
            "label": "(non-breaking space)",
        }
        "ALGR(LV_2)": {
            "key": "LV_LDAQ",
            "label": "«",
        }
        "ALGR(LV_3)": {
            "key": "LV_RDAQ",
            "label": "»",
        }
        "ALGR(LV_4)": {
            "key": "LV_EURO",
            "label": "€",
        }
        "ALGR(LV_6)": {
            "key": "LV_RSQU",
            "label": "’",
        }
        "ALGR(LV_MINS)": {
            "key": "LV_NDSH",
            "label": "–",
        }
        "ALGR(LV_E)": {
            "key": "LV_EMAC",
            "label": "Ē",
        }
        "ALGR(LV_R)": {
            "key": "LV_RCED",
            "label": "Ŗ",
        }
        "ALGR(LV_U)": {
            "key": "LV_UMAC",
            "label": "Ū",
        }
        "ALGR(LV_I)": {
            "key": "LV_IMAC",
            "label": "Ī",
        }
        "ALGR(LV_O)": {
            "key": "LV_OMAC",
            "label": "Ō",
        }
        "ALGR(LV_A)": {
            "key": "LV_AMAC",
            "label": "Ā",
        }
        "ALGR(LV_S)": {
            "key": "LV_SCAR",
            "label": "Š",
        }
        "ALGR(LV_G)": {
            "key": "LV_GCED",
            "label": "Ģ",
        }
        "ALGR(LV_K)": {
            "key": "LV_KCED",
            "label": "Ķ",
        }
        "ALGR(LV_L)": {
            "key": "LV_LCED",
            "label": "Ļ",
        }
        "ALGR(LV_QUOT)": {
            "key": "LV_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(LV_Z)": {
            "key": "LV_ZCAR",
            "label": "Ž",
        }
        "ALGR(LV_C)": {
            "key": "LV_CCAR",
            "label": "Č",
        }
        "ALGR(LV_N)": {
            "key": "LV_NCED",
            "label": "Ņ",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │ § │ ° │   │ ± │ × │   │   │ — │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ¨ │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(LV_4))": {
            "key": "LV_SECT",
            "label": "§",
        }
        "S(ALGR(LV_5))": {
            "key": "LV_DEG",
            "label": "°",
        }
        "S(ALGR(LV_7))": {
            "key": "LV_PLMN",
            "label": "±",
        }
        "S(ALGR(LV_8))": {
            "key": "LV_MUL",
            "label": "×",
        }
        "S(ALGR(LV_MINS))": {
            "key": "LV_MDSH",
            "label": "—",
        }
        "S(ALGR(LV_QUOT))": {
            "key": "LV_DIAE",
            "label": "¨ (dead)",
        }
    }
}