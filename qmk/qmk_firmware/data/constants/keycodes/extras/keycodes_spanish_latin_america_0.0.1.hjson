{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ | │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ ¿ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ ´ │ + │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ñ │ { │ } │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "ES_PIPE",
            "label": "|",
        }
        "KC_1": {
            "key": "ES_1",
            "label": "1",
        }
        "KC_2": {
            "key": "ES_2",
            "label": "2",
        }
        "KC_3": {
            "key": "ES_3",
            "label": "3",
        }
        "KC_4": {
            "key": "ES_4",
            "label": "4",
        }
        "KC_5": {
            "key": "ES_5",
            "label": "5",
        }
        "KC_6": {
            "key": "ES_6",
            "label": "6",
        }
        "KC_7": {
            "key": "ES_7",
            "label": "7",
        }
        "KC_8": {
            "key": "ES_8",
            "label": "8",
        }
        "KC_9": {
            "key": "ES_9",
            "label": "9",
        }
        "KC_0": {
            "key": "ES_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "ES_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "ES_IQUE",
            "label": "¿",
        }
        "KC_Q": {
            "key": "ES_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "ES_W",
            "label": "W",
        }
        "KC_E": {
            "key": "ES_E",
            "label": "E",
        }
        "KC_R": {
            "key": "ES_R",
            "label": "R",
        }
        "KC_T": {
            "key": "ES_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "ES_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "ES_U",
            "label": "U",
        }
        "KC_I": {
            "key": "ES_I",
            "label": "I",
        }
        "KC_O": {
            "key": "ES_O",
            "label": "O",
        }
        "KC_P": {
            "key": "ES_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "ES_ACUT",
            "label": "´ (dead)",
        }
        "KC_RBRC": {
            "key": "ES_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "ES_A",
            "label": "A",
        }
        "KC_S": {
            "key": "ES_S",
            "label": "S",
        }
        "KC_D": {
            "key": "ES_D",
            "label": "D",
        }
        "KC_F": {
            "key": "ES_F",
            "label": "F",
        }
        "KC_G": {
            "key": "ES_G",
            "label": "G",
        }
        "KC_H": {
            "key": "ES_H",
            "label": "H",
        }
        "KC_J": {
            "key": "ES_J",
            "label": "J",
        }
        "KC_K": {
            "key": "ES_K",
            "label": "K",
        }
        "KC_L": {
            "key": "ES_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "ES_NTIL",
            "label": "Ñ",
        }
        "KC_QUOT": {
            "key": "ES_LCBR",
            "label": "{",
        }
        "KC_NUHS": {
            "key": "ES_RCBR",
            "label": "}",
        }
        "KC_NUBS": {
            "key": "ES_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "ES_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "ES_X",
            "label": "X",
        }
        "KC_C": {
            "key": "ES_C",
            "label": "C",
        }
        "KC_V": {
            "key": "ES_V",
            "label": "V",
        }
        "KC_B": {
            "key": "ES_B",
            "label": "B",
        }
        "KC_N": {
            "key": "ES_N",
            "label": "N",
        }
        "KC_M": {
            "key": "ES_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "ES_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "ES_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "ES_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ° │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ ¡ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ ¨ │ * │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ [ │ ] │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ES_PIPE)": {
            "key": "ES_MORD",
            "label": "°",
        }
        "S(ES_1)": {
            "key": "ES_EXLM",
            "label": "!",
        }
        "S(ES_2)": {
            "key": "ES_DQUO",
            "label": "\"",
        }
        "S(ES_3)": {
            "key": "ES_NUMB",
            "label": "#",
        }
        "S(ES_4)": {
            "key": "ES_DLR",
            "label": "$",
        }
        "S(ES_5)": {
            "key": "ES_PERC",
            "label": "%",
        }
        "S(ES_6)": {
            "key": "ES_AMPR",
            "label": "&",
        }
        "S(ES_7)": {
            "key": "ES_SLSH",
            "label": "/",
        }
        "S(ES_8)": {
            "key": "ES_LPRN",
            "label": "(",
        }
        "S(ES_9)": {
            "key": "ES_RPRN",
            "label": ")",
        }
        "S(ES_0)": {
            "key": "ES_EQL",
            "label": "=",
        }
        "S(ES_QUOT)": {
            "key": "ES_QUES",
            "label": "?",
        }
        "S(ES_IQUE)": {
            "key": "ES_IEXL",
            "label": "¡",
        }
        "S(ES_ACUT)": {
            "key": "ES_DIAE",
            "label": "¨ (dead)",
        }
        "S(ES_PLUS)": {
            "key": "ES_ASTR",
            "label": "*",
        }
        "S(ES_LCBR)": {
            "key": "ES_LBRC",
            "label": "[",
        }
        "S(ES_RCBR)": {
            "key": "ES_RBRC",
            "label": "]",
        }
        "S(ES_LABK)": {
            "key": "ES_RABK",
            "label": ">",
        }
        "S(ES_COMM)": {
            "key": "ES_SCLN",
            "label": ";",
        }
        "S(ES_DOT)": {
            "key": "ES_COLN",
            "label": ":",
        }
        "S(ES_MINS)": {
            "key": "ES_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¬ │   │   │   │   │   │   │   │   │   │   │ \ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ @ │   │   │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ^ │ ` │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(ES_PIPE)": {
            "key": "ES_NOT",
            "label": "¬",
        }
        "ALGR(ES_QUOT)": {
            "key": "ES_BSLS",
            "label": "\\",
        }
        "ALGR(ES_Q)": {
            "key": "ES_AT",
            "label": "@",
        }
        "ALGR(ES_PLUS)": {
            "key": "ES_TILD",
            "label": "~",
        }
        "ALGR(ES_LCBR)": {
            "key": "ES_CIRC",
            "label": "^",
        }
        "ALGR(KC_NUHS)": {
            "key": "ES_GRV",
            "label": "`",
        }
    }
}