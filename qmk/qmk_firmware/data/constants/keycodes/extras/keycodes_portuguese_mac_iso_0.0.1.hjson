{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ § │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ + │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ º │ ´ │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ç │ ~ │ \ │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "KC_GRV": {
            "key": "PT_SECT",
            "label": "§",
        }
        "KC_1": {
            "key": "PT_1",
            "label": "1",
        }
        "KC_2": {
            "key": "PT_2",
            "label": "2",
        }
        "KC_3": {
            "key": "PT_3",
            "label": "3",
        }
        "KC_4": {
            "key": "PT_4",
            "label": "4",
        }
        "KC_5": {
            "key": "PT_5",
            "label": "5",
        }
        "KC_6": {
            "key": "PT_6",
            "label": "6",
        }
        "KC_7": {
            "key": "PT_7",
            "label": "7",
        }
        "KC_8": {
            "key": "PT_8",
            "label": "8",
        }
        "KC_9": {
            "key": "PT_9",
            "label": "9",
        }
        "KC_0": {
            "key": "PT_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "PT_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "PT_PLUS",
            "label": "+",
        }
        "KC_Q": {
            "key": "PT_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "PT_W",
            "label": "W",
        }
        "KC_E": {
            "key": "PT_E",
            "label": "E",
        }
        "KC_R": {
            "key": "PT_R",
            "label": "R",
        }
        "KC_T": {
            "key": "PT_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "PT_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "PT_U",
            "label": "U",
        }
        "KC_I": {
            "key": "PT_I",
            "label": "I",
        }
        "KC_O": {
            "key": "PT_O",
            "label": "O",
        }
        "KC_P": {
            "key": "PT_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "PT_MORD",
            "label": "º",
        }
        "KC_RBRC": {
            "key": "PT_ACUT",
            "label": "´ (dead)",
        }
        "KC_A": {
            "key": "PT_A",
            "label": "A",
        }
        "KC_S": {
            "key": "PT_S",
            "label": "S",
        }
        "KC_D": {
            "key": "PT_D",
            "label": "D",
        }
        "KC_F": {
            "key": "PT_F",
            "label": "F",
        }
        "KC_G": {
            "key": "PT_G",
            "label": "G",
        }
        "KC_H": {
            "key": "PT_H",
            "label": "H",
        }
        "KC_J": {
            "key": "PT_J",
            "label": "J",
        }
        "KC_K": {
            "key": "PT_K",
            "label": "K",
        }
        "KC_L": {
            "key": "PT_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "PT_CCED",
            "label": "Ç",
        }
        "KC_QUOT": {
            "key": "PT_TILD",
            "label": "~ (dead)",
        }
        "KC_NUHS": {
            "key": "PT_BSLS",
            "label": "\\",
        }
        "KC_NUBS": {
            "key": "PT_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "PT_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "PT_X",
            "label": "X",
        }
        "KC_C": {
            "key": "PT_C",
            "label": "C",
        }
        "KC_V": {
            "key": "PT_V",
            "label": "V",
        }
        "KC_B": {
            "key": "PT_B",
            "label": "B",
        }
        "KC_N": {
            "key": "PT_N",
            "label": "N",
        }
        "KC_M": {
            "key": "PT_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "PT_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "PT_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "PT_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │ ± │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ * │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │   │   │   │   │   │   │   │   │   │   │ ª │ ` │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │   │   │   │   │   │   │   │   │   │ ^ │ | │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(PT_SECT)": {
            "key": "PT_PLMN",
            "label": "±",
        }
        "S(PT_1)": {
            "key": "PT_EXLM",
            "label": "!",
        }
        "S(PT_2)": {
            "key": "PT_DQUO",
            "label": "\"",
        }
        "S(PT_3)": {
            "key": "PT_HASH",
            "label": "#",
        }
        "S(PT_4)": {
            "key": "PT_DLR",
            "label": "$",
        }
        "S(PT_5)": {
            "key": "PT_PERC",
            "label": "%",
        }
        "S(PT_6)": {
            "key": "PT_AMPR",
            "label": "&",
        }
        "S(PT_7)": {
            "key": "PT_SLSH",
            "label": "/",
        }
        "S(PT_8)": {
            "key": "PT_LPRN",
            "label": "(",
        }
        "S(PT_9)": {
            "key": "PT_RPRN",
            "label": ")",
        }
        "S(PT_0)": {
            "key": "PT_EQL",
            "label": "=",
        }
        "S(PT_QUOT)": {
            "key": "PT_QUES",
            "label": "?",
        }
        "S(PT_PLUS)": {
            "key": "PT_ASTR",
            "label": "*",
        }
        "S(PT_MORD)": {
            "key": "PT_FORD",
            "label": "ª",
        }
        "S(PT_ACUT)": {
            "key": "PT_GRV",
            "label": "` (dead)",
        }
        "S(PT_TILD)": {
            "key": "PT_CIRC",
            "label": "^ (dead)",
        }
        "S(PT_BSLS)": {
            "key": "PT_PIPE",
            "label": "|",
        }
        "S(PT_LABK)": {
            "key": "PT_RABK",
            "label": ">",
        }
        "S(PT_COMM)": {
            "key": "PT_SCLN",
            "label": ";",
        }
        "S(PT_DOT)": {
            "key": "PT_COLN",
            "label": ":",
        }
        "S(PT_MINS)": {
            "key": "PT_UNDS",
            "label": "_",
        }
/* Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │   │  │ @ │ € │ £ │ ‰ │ ¶ │ ÷ │ [ │ ] │ ≠ │   │   │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │ Œ │ ∑ │ Æ │ ® │ ™ │ ¥ │ † │ ı │ Ø │ π │ ° │ ¨ │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │ Å │ ß │ ∂ │ ƒ │ ˙ │ ˇ │ ¯ │ „ │ ‘ │ ¸ │ ˜ │ ‹ │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≤ │ Ω │ « │ © │ √ │ ∫ │ ¬ │ µ │ “ │ … │ — │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "A(PT_1)": {
            "key": "PT_APPL",
            "label": " (Apple logo)",
        }
        "A(PT_2)": {
            "key": "PT_AT",
            "label": "@",
        }
        "A(PT_3)": {
            "key": "PT_EURO",
            "label": "€",
        }
        "A(PT_4)": {
            "key": "PT_PND",
            "label": "£",
        }
        "A(PT_5)": {
            "key": "PT_PERM",
            "label": "‰",
        }
        "A(PT_6)": {
            "key": "PT_PILC",
            "label": "¶",
        }
        "A(PT_7)": {
            "key": "PT_DIV",
            "label": "÷",
        }
        "A(PT_8)": {
            "key": "PT_LBRC",
            "label": "[",
        }
        "A(PT_9)": {
            "key": "PT_RBRC",
            "label": "]",
        }
        "A(PT_0)": {
            "key": "PT_NEQL",
            "label": "≠",
        }
        "A(PT_Q)": {
            "key": "PT_OE",
            "label": "Œ",
        }
        "A(PT_W)": {
            "key": "PT_NARS",
            "label": "∑",
        }
        "A(PT_E)": {
            "key": "PT_AE",
            "label": "Æ",
        }
        "A(PT_R)": {
            "key": "PT_REGD",
            "label": "®",
        }
        "A(PT_T)": {
            "key": "PT_TM",
            "label": "™",
        }
        "A(PT_Y)": {
            "key": "PT_YEN",
            "label": "¥",
        }
        "A(PT_U)": {
            "key": "PT_DAGG",
            "label": "†",
        }
        "A(PT_I)": {
            "key": "PT_DLSI",
            "label": "ı",
        }
        "A(PT_O)": {
            "key": "PT_OSTR",
            "label": "Ø",
        }
        "A(PT_P)": {
            "key": "PT_PI",
            "label": "π",
        }
        "A(PT_MORD)": {
            "key": "PT_DEG",
            "label": "°",
        }
        "A(PT_ACUT)": {
            "key": "PT_DIAE",
            "label": "¨ (dead)",
        }
        "A(PT_A)": {
            "key": "PT_ARNG",
            "label": "å",
        }
        "A(PT_S)": {
            "key": "PT_SS",
            "label": "ß",
        }
        "A(PT_D)": {
            "key": "PT_PDIF",
            "label": "∂",
        }
        "A(PT_F)": {
            "key": "PT_FHK",
            "label": "ƒ",
        }
        "A(PT_G)": {
            "key": "PT_DOTA",
            "label": "˙",
        }
        "A(PT_H)": {
            "key": "PT_CARN",
            "label": "ˇ",
        }
        "A(PT_J)": {
            "key": "PT_MACR",
            "label": "¯",
        }
        "A(PT_K)": {
            "key": "PT_DLQU",
            "label": "„",
        }
        "A(PT_L)": {
            "key": "PT_LSQU",
            "label": "‘",
        }
        "A(PT_CCED)": {
            "key": "PT_CEDL",
            "label": "¸",
        }
        "A(PT_TILD)": {
            "key": "PT_STIL",
            "label": "˜ (dead)",
        }
        "A(PT_BSLS)": {
            "key": "PT_LSAQ",
            "label": "‹",
        }
        "A(PT_LABK)": {
            "key": "PT_LTEQ",
            "label": "≤",
        }
        "A(PT_Z)": {
            "key": "PT_OMEG",
            "label": "Ω",
        }
        "A(PT_X)": {
            "key": "PT_LDAQ",
            "label": "«",
        }
        "A(PT_C)": {
            "key": "PT_COPY",
            "label": "©",
        }
        "A(PT_V)": {
            "key": "PT_SQRT",
            "label": "√",
        }
        "A(PT_B)": {
            "key": "PT_INTG",
            "label": "∫",
        }
        "A(PT_N)": {
            "key": "PT_NOT",
            "label": "¬",
        }
        "A(PT_M)": {
            "key": "PT_MICR",
            "label": "µ",
        }
        "A(PT_COMM)": {
            "key": "PT_LDQU",
            "label": "“",
        }
        "A(PT_DOT)": {
            "key": "PT_ELLP",
            "label": "…",
        }
        "A(PT_MINS)": {
            "key": "PT_MDSH",
            "label": "—",
        }
/* Shift+Alted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬─────┐
 * │   │ ¡ │ ﬁ │ ﬂ │ ¢ │ ∞ │ • │ ⁄ │ { │ } │ ≈ │ ¿ │ ◊ │     │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬───┤
 * │     │   │   │   │   │   │   │ ‡ │ ˚ │   │ ∏ │   │ ˝ │   │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐  │
 * │      │   │   │ ∆ │   │   │   │   │ ‚ │ ’ │ ˛ │ ˆ │ › │  │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴──┤
 * │    │ ≥ │   │ » │   │   │   │   │   │ ” │ · │ – │        │
 * ├────┴┬──┴─┬─┴───┼───┴───┴───┴───┴───┴───┼───┴─┬─┴──┬─────┤
 * │     │    │     │                       │     │    │     │
 * └─────┴────┴─────┴───────────────────────┴─────┴────┴─────┘
 */
        "S(A(PT_1))": {
            "key": "PT_IEXL",
            "label": "¡",
        }
        "S(A(PT_2))": {
            "key": "PT_FI",
            "label": "ﬁ",
        }
        "S(A(PT_3))": {
            "key": "PT_FL",
            "label": "ﬂ",
        }
        "S(A(PT_4))": {
            "key": "PT_CENT",
            "label": "¢",
        }
        "S(A(PT_5))": {
            "key": "PT_INFN",
            "label": "∞",
        }
        "S(A(PT_6))": {
            "key": "PT_BULT",
            "label": "•",
        }
        "S(A(PT_7))": {
            "key": "PT_FRSL",
            "label": "⁄",
        }
        "S(A(PT_8))": {
            "key": "PT_LCBR",
            "label": "{",
        }
        "S(A(PT_9))": {
            "key": "PT_RCBR",
            "label": "}",
        }
        "S(A(PT_0))": {
            "key": "PT_AEQL",
            "label": "≈",
        }
        "S(A(PT_QUOT))": {
            "key": "PT_IQUE",
            "label": "¿",
        }
        "S(A(PT_PLUS))": {
            "key": "PT_LOZN",
            "label": "◊",
        }
        "S(A(PT_U))": {
            "key": "PT_DDAG",
            "label": "‡",
        }
        "S(A(PT_I))": {
            "key": "PT_RNGA",
            "label": "˚",
        }
        "S(A(PT_P))": {
            "key": "PT_NARP",
            "label": "∏",
        }
        "S(A(PT_ACUT))": {
            "key": "PT_DACU",
            "label": "˝",
        }
        "S(A(PT_D))": {
            "key": "PT_INCR",
            "label": "∆",
        }
        "S(A(PT_K))": {
            "key": "PT_SLQU",
            "label": "‚",
        }
        "S(A(PT_L))": {
            "key": "PT_RSQU",
            "label": "’",
        }
        "S(A(PT_CCED))": {
            "key": "PT_OGON",
            "label": "˛",
        }
        "S(A(PT_TILD))": {
            "key": "PT_DCIR",
            "label": "ˆ (dead)",
        }
        "S(A(PT_BSLS))": {
            "key": "PT_RSAQ",
            "label": "›",
        }
        "S(A(PT_LABK))": {
            "key": "PT_GTEQ",
            "label": "≥",
        }
        "S(A(PT_X))": {
            "key": "PT_RDAQ",
            "label": "»",
        }
        "S(A(PT_COMM))": {
            "key": "PT_RDQU",
            "label": "”",
        }
        "S(A(PT_DOT))": {
            "key": "PT_MDDT",
            "label": "·",
        }
        "S(A(PT_MINS))": {
            "key": "PT_NDSH",
            "label": "–",
        }
    }
}