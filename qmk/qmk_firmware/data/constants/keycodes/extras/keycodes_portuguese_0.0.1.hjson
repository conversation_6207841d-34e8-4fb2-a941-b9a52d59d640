{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ \ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ « │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ + │ ´ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ç │ º │ ~ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "PT_BSLS",
            "label": "\\",
        }
        "KC_1": {
            "key": "PT_1",
            "label": "1",
        }
        "KC_2": {
            "key": "PT_2",
            "label": "2",
        }
        "KC_3": {
            "key": "PT_3",
            "label": "3",
        }
        "KC_4": {
            "key": "PT_4",
            "label": "4",
        }
        "KC_5": {
            "key": "PT_5",
            "label": "5",
        }
        "KC_6": {
            "key": "PT_6",
            "label": "6",
        }
        "KC_7": {
            "key": "PT_7",
            "label": "7",
        }
        "KC_8": {
            "key": "PT_8",
            "label": "8",
        }
        "KC_9": {
            "key": "PT_9",
            "label": "9",
        }
        "KC_0": {
            "key": "PT_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "PT_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "PT_LDAQ",
            "label": "«",
        }
        "KC_Q": {
            "key": "PT_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "PT_W",
            "label": "W",
        }
        "KC_E": {
            "key": "PT_E",
            "label": "E",
        }
        "KC_R": {
            "key": "PT_R",
            "label": "R",
        }
        "KC_T": {
            "key": "PT_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "PT_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "PT_U",
            "label": "U",
        }
        "KC_I": {
            "key": "PT_I",
            "label": "I",
        }
        "KC_O": {
            "key": "PT_O",
            "label": "O",
        }
        "KC_P": {
            "key": "PT_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "PT_PLUS",
            "label": "+",
        }
        "KC_RBRC": {
            "key": "PT_ACUT",
            "label": "´ (dead)",
        }
        "KC_A": {
            "key": "PT_A",
            "label": "A",
        }
        "KC_S": {
            "key": "PT_S",
            "label": "S",
        }
        "KC_D": {
            "key": "PT_D",
            "label": "D",
        }
        "KC_F": {
            "key": "PT_F",
            "label": "F",
        }
        "KC_G": {
            "key": "PT_G",
            "label": "G",
        }
        "KC_H": {
            "key": "PT_H",
            "label": "H",
        }
        "KC_J": {
            "key": "PT_J",
            "label": "J",
        }
        "KC_K": {
            "key": "PT_K",
            "label": "K",
        }
        "KC_L": {
            "key": "PT_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "PT_CCED",
            "label": "Ç",
        }
        "KC_QUOT": {
            "key": "PT_MORD",
            "label": "º",
        }
        "KC_NUHS": {
            "key": "PT_TILD",
            "label": "~ (dead)",
        }
        "KC_NUBS": {
            "key": "PT_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "PT_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "PT_X",
            "label": "X",
        }
        "KC_C": {
            "key": "PT_C",
            "label": "C",
        }
        "KC_V": {
            "key": "PT_V",
            "label": "V",
        }
        "KC_B": {
            "key": "PT_B",
            "label": "B",
        }
        "KC_N": {
            "key": "PT_N",
            "label": "N",
        }
        "KC_M": {
            "key": "PT_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "PT_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "PT_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "PT_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ | │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ » │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ * │ ` │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ª │ ^ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(PT_BSLS)": {
            "key": "PT_PIPE",
            "label": "|",
        }
        "S(PT_1)": {
            "key": "PT_EXLM",
            "label": "!",
        }
        "S(PT_2)": {
            "key": "PT_DQUO",
            "label": "\"",
        }
        "S(PT_3)": {
            "key": "PT_HASH",
            "label": "#",
        }
        "S(PT_4)": {
            "key": "PT_DLR",
            "label": "$",
        }
        "S(PT_5)": {
            "key": "PT_PERC",
            "label": "%",
        }
        "S(PT_6)": {
            "key": "PT_AMPR",
            "label": "&",
        }
        "S(PT_7)": {
            "key": "PT_SLSH",
            "label": "/",
        }
        "S(PT_8)": {
            "key": "PT_LPRN",
            "label": "(",
        }
        "S(PT_9)": {
            "key": "PT_RPRN",
            "label": ")",
        }
        "S(PT_0)": {
            "key": "PT_EQL",
            "label": "=",
        }
        "S(PT_QUOT)": {
            "key": "PT_QUES",
            "label": "?",
        }
        "S(PT_LDAQ)": {
            "key": "PT_RDAQ",
            "label": "»",
        }
        "S(PT_PLUS)": {
            "key": "PT_ASTR",
            "label": "*",
        }
        "S(PT_ACUT)": {
            "key": "PT_GRV",
            "label": "` (dead)",
        }
        "S(PT_MORD)": {
            "key": "PT_FORD",
            "label": "ª",
        }
        "S(PT_TILD)": {
            "key": "PT_CIRC",
            "label": "^ (dead)",
        }
        "S(PT_LABK)": {
            "key": "PT_RABK",
            "label": ">",
        }
        "S(PT_COMM)": {
            "key": "PT_SCLN",
            "label": ";",
        }
        "S(PT_DOT)": {
            "key": "PT_COLN",
            "label": ":",
        }
        "S(PT_MINS)": {
            "key": "PT_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ @ │ £ │ § │   │   │ { │ [ │ ] │ } │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │   │ ¨ │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(PT_2)": {
            "key": "PT_AT",
            "label": "@",
        }
        "ALGR(PT_3)": {
            "key": "PT_PND",
            "label": "£",
        }
        "ALGR(PT_4)": {
            "key": "PT_SECT",
            "label": "§",
        }
        "ALGR(PT_7)": {
            "key": "PT_LCBR",
            "label": "{",
        }
        "ALGR(PT_8)": {
            "key": "PT_LBRC",
            "label": "[",
        }
        "ALGR(PT_9)": {
            "key": "PT_RBRC",
            "label": "]",
        }
        "ALGR(PT_0)": {
            "key": "PT_RCBR",
            "label": "}",
        }
        "ALGR(PT_PLUS)": {
            "key": "PT_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(PT_E)": {
            "key": "PT_EURO",
            "label": "€",
        }
    }
}