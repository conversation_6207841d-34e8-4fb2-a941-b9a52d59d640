{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ § │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ + │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Å │ ¨ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ö │ Ä │ ' │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "SE_SECT",
            "label": "§",
        }
        "KC_1": {
            "key": "SE_1",
            "label": "1",
        }
        "KC_2": {
            "key": "SE_2",
            "label": "2",
        }
        "KC_3": {
            "key": "SE_3",
            "label": "3",
        }
        "KC_4": {
            "key": "SE_4",
            "label": "4",
        }
        "KC_5": {
            "key": "SE_5",
            "label": "5",
        }
        "KC_6": {
            "key": "SE_6",
            "label": "6",
        }
        "KC_7": {
            "key": "SE_7",
            "label": "7",
        }
        "KC_8": {
            "key": "SE_8",
            "label": "8",
        }
        "KC_9": {
            "key": "SE_9",
            "label": "9",
        }
        "KC_0": {
            "key": "SE_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "SE_PLUS",
            "label": "+",
        }
        "KC_EQL": {
            "key": "SE_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "SE_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "SE_W",
            "label": "W",
        }
        "KC_E": {
            "key": "SE_E",
            "label": "E",
        }
        "KC_R": {
            "key": "SE_R",
            "label": "R",
        }
        "KC_T": {
            "key": "SE_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "SE_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "SE_U",
            "label": "U",
        }
        "KC_I": {
            "key": "SE_I",
            "label": "I",
        }
        "KC_O": {
            "key": "SE_O",
            "label": "O",
        }
        "KC_P": {
            "key": "SE_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "SE_ARNG",
            "label": "Å",
        }
        "KC_RBRC": {
            "key": "SE_DIAE",
            "label": "¨ (dead)",
        }
        "KC_A": {
            "key": "SE_A",
            "label": "A",
        }
        "KC_S": {
            "key": "SE_S",
            "label": "S",
        }
        "KC_D": {
            "key": "SE_D",
            "label": "D",
        }
        "KC_F": {
            "key": "SE_F",
            "label": "F",
        }
        "KC_G": {
            "key": "SE_G",
            "label": "G",
        }
        "KC_H": {
            "key": "SE_H",
            "label": "H",
        }
        "KC_J": {
            "key": "SE_J",
            "label": "J",
        }
        "KC_K": {
            "key": "SE_K",
            "label": "K",
        }
        "KC_L": {
            "key": "SE_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "SE_ODIA",
            "label": "Ö",
        }
        "KC_QUOT": {
            "key": "SE_ADIA",
            "label": "Ä",
        }
        "KC_NUHS": {
            "key": "SE_QUOT",
            "label": "'",
        }
        "KC_NUBS": {
            "key": "SE_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "SE_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "SE_X",
            "label": "X",
        }
        "KC_C": {
            "key": "SE_C",
            "label": "C",
        }
        "KC_V": {
            "key": "SE_V",
            "label": "V",
        }
        "KC_B": {
            "key": "SE_B",
            "label": "B",
        }
        "KC_N": {
            "key": "SE_N",
            "label": "N",
        }
        "KC_M": {
            "key": "SE_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "SE_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "SE_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "SE_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ½ │ ! │ " │ # │ ¤ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ^ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(SE_SECT)": {
            "key": "SE_HALF",
            "label": "½",
        }
        "S(SE_1)": {
            "key": "SE_EXLM",
            "label": "!",
        }
        "S(SE_2)": {
            "key": "SE_DQUO",
            "label": "\"",
        }
        "S(SE_3)": {
            "key": "SE_HASH",
            "label": "#",
        }
        "S(SE_4)": {
            "key": "SE_CURR",
            "label": "¤",
        }
        "S(SE_5)": {
            "key": "SE_PERC",
            "label": "%",
        }
        "S(SE_6)": {
            "key": "SE_AMPR",
            "label": "&",
        }
        "S(SE_7)": {
            "key": "SE_SLSH",
            "label": "/",
        }
        "S(SE_8)": {
            "key": "SE_LPRN",
            "label": "(",
        }
        "S(SE_9)": {
            "key": "SE_RPRN",
            "label": ")",
        }
        "S(SE_0)": {
            "key": "SE_EQL",
            "label": "=",
        }
        "S(SE_PLUS)": {
            "key": "SE_QUES",
            "label": "?",
        }
        "S(SE_ACUT)": {
            "key": "SE_GRV",
            "label": "` (dead)",
        }
        "S(SE_DIAE)": {
            "key": "SE_CIRC",
            "label": "^ (dead)",
        }
        "S(SE_QUOT)": {
            "key": "SE_ASTR",
            "label": "*",
        }
        "S(SE_LABK)": {
            "key": "SE_RABK",
            "label": ">",
        }
        "S(SE_COMM)": {
            "key": "SE_SCLN",
            "label": ";",
        }
        "S(SE_DOT)": {
            "key": "SE_COLN",
            "label": ":",
        }
        "S(SE_MINS)": {
            "key": "SE_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ @ │ £ │ $ │ € │   │ { │ [ │ ] │ } │ \ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │ µ │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(SE_2)": {
            "key": "SE_AT",
            "label": "@",
        }
        "ALGR(SE_3)": {
            "key": "SE_PND",
            "label": "£",
        }
        "ALGR(SE_4)": {
            "key": "SE_DLR",
            "label": "$",
        }
        "ALGR(SE_5)": {
            "key": "SE_EURO",
            "label": "€",
        }
        "ALGR(SE_7)": {
            "key": "SE_LCBR",
            "label": "{",
        }
        "ALGR(SE_8)": {
            "key": "SE_LBRC",
            "label": "[",
        }
        "ALGR(SE_9)": {
            "key": "SE_RBRC",
            "label": "]",
        }
        "ALGR(SE_0)": {
            "key": "SE_RCBR",
            "label": "}",
        }
        "ALGR(SE_PLUS)": {
            "key": "SE_BSLS",
            "label": "\\",
        }
        "ALGR(SE_DIAE)": {
            "key": "SE_TILD",
            "label": "~ (dead)",
        }
        "ALGR(SE_LABK)": {
            "key": "SE_PIPE",
            "label": "|",
        }
        "ALGR(SE_M)": {
            "key": "SE_MICR",
            "label": "µ",
        }
    }
}