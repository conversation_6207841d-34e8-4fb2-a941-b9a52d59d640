{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ # │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ ^ │ ¸ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ` │ < │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ « │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ É │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "FR_HASH",
            "label": "#",
        }
        "KC_1": {
            "key": "FR_1",
            "label": "1",
        }
        "KC_2": {
            "key": "FR_2",
            "label": "2",
        }
        "KC_3": {
            "key": "FR_3",
            "label": "3",
        }
        "KC_4": {
            "key": "FR_4",
            "label": "4",
        }
        "KC_5": {
            "key": "FR_5",
            "label": "5",
        }
        "KC_6": {
            "key": "FR_6",
            "label": "6",
        }
        "KC_7": {
            "key": "FR_7",
            "label": "7",
        }
        "KC_8": {
            "key": "FR_8",
            "label": "8",
        }
        "KC_9": {
            "key": "FR_9",
            "label": "9",
        }
        "KC_0": {
            "key": "FR_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "FR_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "FR_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "FR_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "FR_W",
            "label": "W",
        }
        "KC_E": {
            "key": "FR_E",
            "label": "E",
        }
        "KC_R": {
            "key": "FR_R",
            "label": "R",
        }
        "KC_T": {
            "key": "FR_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "FR_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "FR_U",
            "label": "U",
        }
        "KC_I": {
            "key": "FR_I",
            "label": "I",
        }
        "KC_O": {
            "key": "FR_O",
            "label": "O",
        }
        "KC_P": {
            "key": "FR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "FR_DCIR",
            "label": "^ (dead)",
        }
        "KC_RBRC": {
            "key": "FR_CEDL",
            "label": "¸ (dead)",
        }
        "KC_A": {
            "key": "FR_A",
            "label": "A",
        }
        "KC_S": {
            "key": "FR_S",
            "label": "S",
        }
        "KC_D": {
            "key": "FR_D",
            "label": "D",
        }
        "KC_F": {
            "key": "FR_F",
            "label": "F",
        }
        "KC_G": {
            "key": "FR_G",
            "label": "G",
        }
        "KC_H": {
            "key": "FR_H",
            "label": "H",
        }
        "KC_J": {
            "key": "FR_J",
            "label": "J",
        }
        "KC_K": {
            "key": "FR_K",
            "label": "K",
        }
        "KC_L": {
            "key": "FR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "FR_SCLN",
            "label": ";",
        }
        "KC_QUOT": {
            "key": "FR_DGRV",
            "label": "` (dead)",
        }
        "KC_NUHS": {
            "key": "FR_LABK",
            "label": "<",
        }
        "KC_NUBS": {
            "key": "FR_LDAQ",
            "label": "«",
        }
        "KC_Z": {
            "key": "FR_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "FR_X",
            "label": "X",
        }
        "KC_C": {
            "key": "FR_C",
            "label": "C",
        }
        "KC_V": {
            "key": "FR_V",
            "label": "V",
        }
        "KC_B": {
            "key": "FR_B",
            "label": "B",
        }
        "KC_N": {
            "key": "FR_N",
            "label": "N",
        }
        "KC_M": {
            "key": "FR_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "FR_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "FR_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "FR_EACU",
            "label": "É",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ | │ ! │ " │ / │ $ │ % │ ? │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ¨ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ : │   │ > │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ » │   │   │   │   │   │   │   │ ' │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(FR_HASH)": {
            "key": "FR_PIPE",
            "label": "|",
        }
        "S(FR_1)": {
            "key": "FR_EXLM",
            "label": "!",
        }
        "S(FR_2)": {
            "key": "FR_DQUO",
            "label": "\"",
        }
        "S(FR_3)": {
            "key": "FR_SLSH",
            "label": "/",
        }
        "S(FR_4)": {
            "key": "FR_DLR",
            "label": "$",
        }
        "S(FR_5)": {
            "key": "FR_PERC",
            "label": "%",
        }
        "S(FR_6)": {
            "key": "FR_QUES",
            "label": "?",
        }
        "S(FR_7)": {
            "key": "FR_AMPR",
            "label": "&",
        }
        "S(FR_8)": {
            "key": "FR_ASTR",
            "label": "*",
        }
        "S(FR_9)": {
            "key": "FR_LPRN",
            "label": "(",
        }
        "S(FR_0)": {
            "key": "FR_RPRN",
            "label": ")",
        }
        "S(FR_MINS)": {
            "key": "FR_UNDS",
            "label": "_",
        }
        "S(FR_EQL)": {
            "key": "FR_PLUS",
            "label": "+",
        }
        "S(FR_CEDL)": {
            "key": "FR_DIAE",
            "label": "¨ (dead)",
        }
        "S(FR_SCLN)": {
            "key": "FR_COLN",
            "label": ":",
        }
        "S(FR_LABK)": {
            "key": "FR_RABK",
            "label": ">",
        }
        "S(FR_LDAQ)": {
            "key": "FR_RDAQ",
            "label": "»",
        }
        "S(FR_COMM)": {
            "key": "FR_QUOT",
            "label": "'",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ \ │ ± │ @ │ £ │ ¢ │ ¤ │ ¬ │ ¦ │ ² │ ³ │ ¼ │ ½ │ ¾ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │ § │ ¶ │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ ~ │ { │ } │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ ° │   │   │   │   │   │   │ µ │ ¯ │ - │ ´ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(FR_HASH)": {
            "key": "FR_BSLS",
            "label": "\\",
        }
        "ALGR(FR_1)": {
            "key": "FR_PLMN",
            "label": "±",
        }
        "ALGR(FR_2)": {
            "key": "FR_AT",
            "label": "@",
        }
        "ALGR(FR_3)": {
            "key": "FR_PND",
            "label": "£",
        }
        "ALGR(FR_4)": {
            "key": "FR_CENT",
            "label": "¢",
        }
        "ALGR(FR_5)": {
            "key": "FR_CURR",
            "label": "¤",
        }
        "ALGR(FR_6)": {
            "key": "FR_NOT",
            "label": "¬",
        }
        "ALGR(FR_7)": {
            "key": "FR_BRKP",
            "label": "¦",
        }
        "ALGR(FR_8)": {
            "key": "FR_SUP2",
            "label": "²",
        }
        "ALGR(FR_9)": {
            "key": "FR_SUP3",
            "label": "³",
        }
        "ALGR(FR_0)": {
            "key": "FR_QRTR",
            "label": "¼",
        }
        "ALGR(FR_MINS)": {
            "key": "FR_HALF",
            "label": "½",
        }
        "ALGR(FR_EQL)": {
            "key": "FR_TQTR",
            "label": "¾",
        }
        "ALGR(FR_O)": {
            "key": "FR_SECT",
            "label": "§",
        }
        "ALGR(FR_P)": {
            "key": "FR_PARA",
            "label": "¶",
        }
        "ALGR(FR_DCIR)": {
            "key": "FR_LBRC",
            "label": "[",
        }
        "ALGR(FR_CEDL)": {
            "key": "FR_RBRC",
            "label": "]",
        }
        "ALGR(FR_SCLN)": {
            "key": "FR_TILD",
            "label": "~",
        }
        "ALGR(FR_DGRV)": {
            "key": "FR_LCBR",
            "label": "{",
        }
        "ALGR(FR_LABK)": {
            "key": "FR_RCBR",
            "label": "}",
        }
        "ALGR(FR_LDAQ)": {
            "key": "FR_DEG",
            "label": "°",
        }
        "ALGR(FR_M)": {
            "key": "FR_MICR",
            "label": "µ",
        }
        "ALGR(FR_COMM)": {
            "key": "FR_MACR",
            "label": "¯",
        }
        "ALGR(FR_DOT)": {
            "key": "FR_SHYP",
            "label": "­ (soft hyphen)",
        }
        "ALGR(FR_EACU)": {
            "key": "FR_ACUT",
            "label": "´ (dead)",
        }
    }
}
