{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ˇ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ + │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Ü │ Õ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ö │ Ä │ ' │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "EE_CARN",
            "label": "ˇ (dead)",
        }
        "KC_1": {
            "key": "EE_1",
            "label": "1",
        }
        "KC_2": {
            "key": "EE_2",
            "label": "2",
        }
        "KC_3": {
            "key": "EE_3",
            "label": "3",
        }
        "KC_4": {
            "key": "EE_4",
            "label": "4",
        }
        "KC_5": {
            "key": "EE_5",
            "label": "5",
        }
        "KC_6": {
            "key": "EE_6",
            "label": "6",
        }
        "KC_7": {
            "key": "EE_7",
            "label": "7",
        }
        "KC_8": {
            "key": "EE_8",
            "label": "8",
        }
        "KC_9": {
            "key": "EE_9",
            "label": "9",
        }
        "KC_0": {
            "key": "EE_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "EE_PLUS",
            "label": "+",
        }
        "KC_EQL": {
            "key": "EE_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "EE_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "EE_W",
            "label": "W",
        }
        "KC_E": {
            "key": "EE_E",
            "label": "E",
        }
        "KC_R": {
            "key": "EE_R",
            "label": "R",
        }
        "KC_T": {
            "key": "EE_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "EE_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "EE_U",
            "label": "U",
        }
        "KC_I": {
            "key": "EE_I",
            "label": "I",
        }
        "KC_O": {
            "key": "EE_O",
            "label": "O",
        }
        "KC_P": {
            "key": "EE_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "EE_UDIA",
            "label": "Ü",
        }
        "KC_RBRC": {
            "key": "EE_OTIL",
            "label": "Õ",
        }
        "KC_A": {
            "key": "EE_A",
            "label": "A",
        }
        "KC_S": {
            "key": "EE_S",
            "label": "S",
        }
        "KC_D": {
            "key": "EE_D",
            "label": "D",
        }
        "KC_F": {
            "key": "EE_F",
            "label": "F",
        }
        "KC_G": {
            "key": "EE_G",
            "label": "G",
        }
        "KC_H": {
            "key": "EE_H",
            "label": "H",
        }
        "KC_J": {
            "key": "EE_J",
            "label": "J",
        }
        "KC_K": {
            "key": "EE_K",
            "label": "K",
        }
        "KC_L": {
            "key": "EE_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "EE_ODIA",
            "label": "Ö",
        }
        "KC_QUOT": {
            "key": "EE_ADIA",
            "label": "Ä",
        }
        "KC_NUHS": {
            "key": "EE_QUOT",
            "label": "'",
        }
        "KC_NUBS": {
            "key": "EE_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "EE_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "EE_X",
            "label": "X",
        }
        "KC_C": {
            "key": "EE_C",
            "label": "C",
        }
        "KC_V": {
            "key": "EE_V",
            "label": "V",
        }
        "KC_B": {
            "key": "EE_B",
            "label": "B",
        }
        "KC_N": {
            "key": "EE_N",
            "label": "N",
        }
        "KC_M": {
            "key": "EE_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "EE_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "EE_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "EE_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ " │ # │ ¤ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(EE_CARN)": {
            "key": "EE_TILD",
            "label": "~ (dead)",
        }
        "S(EE_1)": {
            "key": "EE_EXLM",
            "label": "!",
        }
        "S(EE_2)": {
            "key": "EE_DQUO",
            "label": "\"",
        }
        "S(EE_3)": {
            "key": "EE_HASH",
            "label": "#",
        }
        "S(EE_4)": {
            "key": "EE_CURR",
            "label": "¤",
        }
        "S(EE_5)": {
            "key": "EE_PERC",
            "label": "%",
        }
        "S(EE_6)": {
            "key": "EE_AMPR",
            "label": "&",
        }
        "S(EE_7)": {
            "key": "EE_SLSH",
            "label": "/",
        }
        "S(EE_8)": {
            "key": "EE_LPRN",
            "label": "(",
        }
        "S(EE_9)": {
            "key": "EE_RPRN",
            "label": ")",
        }
        "S(EE_0)": {
            "key": "EE_EQL",
            "label": "=",
        }
        "S(EE_PLUS)": {
            "key": "EE_QUES",
            "label": "?",
        }
        "S(EE_ACUT)": {
            "key": "EE_GRV",
            "label": "` (dead)",
        }
        "S(EE_QUOT)": {
            "key": "EE_ASTR",
            "label": "*",
        }
        "S(EE_LABK)": {
            "key": "EE_RABK",
            "label": ">",
        }
        "S(EE_COMM)": {
            "key": "EE_SCLN",
            "label": ";",
        }
        "S(EE_DOT)": {
            "key": "EE_COLN",
            "label": ":",
        }
        "S(EE_MINS)": {
            "key": "EE_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ @ │ £ │ $ │ € │   │ { │ [ │ ] │ } │ \ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ § │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │ š │   │   │   │   │   │   │   │   │ ^ │ ½ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │ ž │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(EE_2)": {
            "key": "EE_AT",
            "label": "@",
        }
        "ALGR(EE_3)": {
            "key": "EE_PND",
            "label": "£",
        }
        "ALGR(EE_4)": {
            "key": "EE_DLR",
            "label": "$",
        }
        "ALGR(EE_5)": {
            "key": "EE_EURO",
            "label": "€",
        }
        "ALGR(EE_7)": {
            "key": "EE_LCBR",
            "label": "{",
        }
        "ALGR(EE_8)": {
            "key": "EE_LBRC",
            "label": "[",
        }
        "ALGR(EE_9)": {
            "key": "EE_RBRC",
            "label": "]",
        }
        "ALGR(EE_0)": {
            "key": "EE_RCBR",
            "label": "}",
        }
        "ALGR(EE_PLUS)": {
            "key": "EE_BSLS",
            "label": "\\",
        }
        "ALGR(EE_OTIL)": {
            "key": "EE_SECT",
            "label": "§",
        }
        "ALGR(EE_S)": {
            "key": "EE_SCAR",
            "label": "š",
        }
        "ALGR(EE_ADIA)": {
            "key": "EE_CIRC",
            "label": "^ (dead)",
        }
        "ALGR(EE_QUOT)": {
            "key": "EE_HALF",
            "label": "½",
        }
        "ALGR(EE_LABK)": {
            "key": "EE_PIPE",
            "label": "|",
        }
        "ALGR(EE_Z)": {
            "key": "EE_ZCAR",
            "label": "ž",
        }
    }
}