{
    "aliases": {
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │  |  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │ : │ " │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(KC_GRAVE)": {
            "key": "KC_TILD",
            "label": "~",
            "aliases": [
                "KC_TILDE"
            ]
        },
        "S(KC_1)": {
            "key": "KC_EXLM",
            "label": "!",
            "aliases": [
                "KC_EXCLAIM"
            ]
        },
        "S(KC_2)": {
            "key": "KC_AT",
            "label": "@"
        },
        "S(KC_3)": {
            "key": "KC_HASH",
            "label": "#"
        },
        "S(KC_4)": {
            "key": "KC_DLR",
            "label": "$",
            "aliases": [
                "KC_DOLLAR"
            ]
        },
        "S(KC_5)": {
            "key": "KC_PERC",
            "label": "%",
            "aliases": [
                "KC_PERCENT"
            ]
        },
        "S(KC_6)": {
            "key": "KC_CIRC",
            "label": "^",
            "aliases": [
                "KC_CIRCUMFLEX"
            ]
        },
        "S(KC_7)": {
            "key": "KC_AMPR",
            "label": "&",
            "aliases": [
                "KC_AMPERSAND"
            ]
        },
        "S(KC_8)": {
            "key": "KC_ASTR",
            "label": "*",
            "aliases": [
                "KC_ASTERISK"
            ]
        },
        "S(KC_9)": {
            "key": "KC_LPRN",
            "label": "(",
            "aliases": [
                "KC_LEFT_PAREN"
            ]
        },
        "S(KC_0)": {
            "key": "KC_RPRN",
            "label": ")",
            "aliases": [
                "KC_RIGHT_PAREN"
            ]
        },
        "S(KC_MINUS)": {
            "key": "KC_UNDS",
            "label": "_",
            "aliases": [
                "KC_UNDERSCORE"
            ]
        },
        "S(KC_EQUAL)": {
            "key": "KC_PLUS",
            "label": "+"
        },
        "S(KC_LEFT_BRACKET)": {
            "key": "KC_LCBR",
            "label": "{",
            "aliases": [
                "KC_LEFT_CURLY_BRACE"
            ]
        },
        "S(KC_RIGHT_BRACKET)": {
            "key": "KC_RCBR",
            "label": "}",
            "aliases": [
                "KC_RIGHT_CURLY_BRACE"
            ]
        },
        "S(KC_BACKSLASH)": {
            "key": "KC_PIPE",
            "label": "|"
        },
        "S(KC_SEMICOLON)": {
            "key": "KC_COLN",
            "label": ":",
            "aliases": [
                "KC_COLON"
            ]
        },
        "S(KC_QUOTE)": {
            "key": "KC_DQUO",
            "label": "\"",
            "aliases": [
                "KC_DOUBLE_QUOTE",
                "KC_DQT"
            ]
        },
        "S(KC_COMMA)": {
            "key": "KC_LABK",
            "label": "<",
            "aliases": [
                "KC_LEFT_ANGLE_BRACKET",
                "KC_LT"
            ]
        },
        "S(KC_DOT)": {
            "key": "KC_RABK",
            "label": ">",
            "aliases": [
                "KC_RIGHT_ANGLE_BRACKET",
                "KC_GT"
            ]
        },
        "S(KC_SLASH)": {
            "key": "KC_QUES",
            "label": "?",
            "aliases": [
                "KC_QUESTION"
            ]
        }
    }
}
