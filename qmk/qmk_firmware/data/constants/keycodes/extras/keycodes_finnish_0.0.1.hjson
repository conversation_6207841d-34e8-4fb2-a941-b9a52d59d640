{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ § │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ + │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Å │ ¨ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ö │ Ä │ ' │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "FI_SECT",
            "label": "§",
        }
        "KC_1": {
            "key": "FI_1",
            "label": "1",
        }
        "KC_2": {
            "key": "FI_2",
            "label": "2",
        }
        "KC_3": {
            "key": "FI_3",
            "label": "3",
        }
        "KC_4": {
            "key": "FI_4",
            "label": "4",
        }
        "KC_5": {
            "key": "FI_5",
            "label": "5",
        }
        "KC_6": {
            "key": "FI_6",
            "label": "6",
        }
        "KC_7": {
            "key": "FI_7",
            "label": "7",
        }
        "KC_8": {
            "key": "FI_8",
            "label": "8",
        }
        "KC_9": {
            "key": "FI_9",
            "label": "9",
        }
        "KC_0": {
            "key": "FI_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "FI_PLUS",
            "label": "+",
        }
        "KC_EQL": {
            "key": "FI_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "FI_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "FI_W",
            "label": "W",
        }
        "KC_E": {
            "key": "FI_E",
            "label": "E",
        }
        "KC_R": {
            "key": "FI_R",
            "label": "R",
        }
        "KC_T": {
            "key": "FI_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "FI_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "FI_U",
            "label": "U",
        }
        "KC_I": {
            "key": "FI_I",
            "label": "I",
        }
        "KC_O": {
            "key": "FI_O",
            "label": "O",
        }
        "KC_P": {
            "key": "FI_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "FI_ARNG",
            "label": "Å",
        }
        "KC_RBRC": {
            "key": "FI_DIAE",
            "label": "¨ (dead)",
        }
        "KC_A": {
            "key": "FI_A",
            "label": "A",
        }
        "KC_S": {
            "key": "FI_S",
            "label": "S",
        }
        "KC_D": {
            "key": "FI_D",
            "label": "D",
        }
        "KC_F": {
            "key": "FI_F",
            "label": "F",
        }
        "KC_G": {
            "key": "FI_G",
            "label": "G",
        }
        "KC_H": {
            "key": "FI_H",
            "label": "H",
        }
        "KC_J": {
            "key": "FI_J",
            "label": "J",
        }
        "KC_K": {
            "key": "FI_K",
            "label": "K",
        }
        "KC_L": {
            "key": "FI_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "FI_ODIA",
            "label": "Ö",
        }
        "KC_QUOT": {
            "key": "FI_ADIA",
            "label": "Ä",
        }
        "KC_NUHS": {
            "key": "FI_QUOT",
            "label": "'",
        }
        "KC_NUBS": {
            "key": "FI_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "FI_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "FI_X",
            "label": "X",
        }
        "KC_C": {
            "key": "FI_C",
            "label": "C",
        }
        "KC_V": {
            "key": "FI_V",
            "label": "V",
        }
        "KC_B": {
            "key": "FI_B",
            "label": "B",
        }
        "KC_N": {
            "key": "FI_N",
            "label": "N",
        }
        "KC_M": {
            "key": "FI_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "FI_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "FI_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "FI_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ½ │ ! │ " │ # │ ¤ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ^ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(FI_SECT)": {
            "key": "FI_HALF",
            "label": "½",
        }
        "S(FI_1)": {
            "key": "FI_EXLM",
            "label": "!",
        }
        "S(FI_2)": {
            "key": "FI_DQUO",
            "label": "\"",
        }
        "S(FI_3)": {
            "key": "FI_HASH",
            "label": "#",
        }
        "S(FI_4)": {
            "key": "FI_CURR",
            "label": "¤",
        }
        "S(FI_5)": {
            "key": "FI_PERC",
            "label": "%",
        }
        "S(FI_6)": {
            "key": "FI_AMPR",
            "label": "&",
        }
        "S(FI_7)": {
            "key": "FI_SLSH",
            "label": "/",
        }
        "S(FI_8)": {
            "key": "FI_LPRN",
            "label": "(",
        }
        "S(FI_9)": {
            "key": "FI_RPRN",
            "label": ")",
        }
        "S(FI_0)": {
            "key": "FI_EQL",
            "label": "=",
        }
        "S(FI_PLUS)": {
            "key": "FI_QUES",
            "label": "?",
        }
        "S(FI_ACUT)": {
            "key": "FI_GRV",
            "label": "` (dead)",
        }
        "S(FI_DIAE)": {
            "key": "FI_CIRC",
            "label": "^ (dead)",
        }
        "S(FI_QUOT)": {
            "key": "FI_ASTR",
            "label": "*",
        }
        "S(FI_LABK)": {
            "key": "FI_RABK",
            "label": ">",
        }
        "S(FI_COMM)": {
            "key": "FI_SCLN",
            "label": ";",
        }
        "S(FI_DOT)": {
            "key": "FI_COLN",
            "label": ":",
        }
        "S(FI_MINS)": {
            "key": "FI_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ @ │ £ │ $ │ € │   │ { │ [ │ ] │ } │ \ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │ µ │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(FI_2)": {
            "key": "FI_AT",
            "label": "@",
        }
        "ALGR(FI_3)": {
            "key": "FI_PND",
            "label": "£",
        }
        "ALGR(FI_4)": {
            "key": "FI_DLR",
            "label": "$",
        }
        "ALGR(FI_5)": {
            "key": "FI_EURO",
            "label": "€",
        }
        "ALGR(FI_7)": {
            "key": "FI_LCBR",
            "label": "{",
        }
        "ALGR(FI_8)": {
            "key": "FI_LBRC",
            "label": "[",
        }
        "ALGR(FI_9)": {
            "key": "FI_RBRC",
            "label": "]",
        }
        "ALGR(FI_0)": {
            "key": "FI_RCBR",
            "label": "}",
        }
        "ALGR(FI_PLUS)": {
            "key": "FI_BSLS",
            "label": "\\",
        }
        "ALGR(FI_DIAE)": {
            "key": "FI_TILD",
            "label": "~ (dead)",
        }
        "ALGR(FI_LABK)": {
            "key": "FI_PIPE",
            "label": "|",
        }
        "ALGR(FI_M)": {
            "key": "FI_MICR",
            "label": "µ",
        }
    }
}