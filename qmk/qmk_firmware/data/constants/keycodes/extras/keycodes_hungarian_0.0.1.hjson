{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ 0 │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ Ö │ Ü │ Ó │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ Ő │ Ú │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ É │ Á │ Ű │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ Í │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "HU_0",
            "label": "0",
        }
        "KC_1": {
            "key": "HU_1",
            "label": "1",
        }
        "KC_2": {
            "key": "HU_2",
            "label": "2",
        }
        "KC_3": {
            "key": "HU_3",
            "label": "3",
        }
        "KC_4": {
            "key": "HU_4",
            "label": "4",
        }
        "KC_5": {
            "key": "HU_5",
            "label": "5",
        }
        "KC_6": {
            "key": "HU_6",
            "label": "6",
        }
        "KC_7": {
            "key": "HU_7",
            "label": "7",
        }
        "KC_8": {
            "key": "HU_8",
            "label": "8",
        }
        "KC_9": {
            "key": "HU_9",
            "label": "9",
        }
        "KC_0": {
            "key": "HU_ODIA",
            "label": "Ö",
        }
        "KC_MINS": {
            "key": "HU_UDIA",
            "label": "Ü",
        }
        "KC_EQL": {
            "key": "HU_OACU",
            "label": "Ó",
        }
        "KC_Q": {
            "key": "HU_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "HU_W",
            "label": "W",
        }
        "KC_E": {
            "key": "HU_E",
            "label": "E",
        }
        "KC_R": {
            "key": "HU_R",
            "label": "R",
        }
        "KC_T": {
            "key": "HU_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "HU_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "HU_U",
            "label": "U",
        }
        "KC_I": {
            "key": "HU_I",
            "label": "I",
        }
        "KC_O": {
            "key": "HU_O",
            "label": "O",
        }
        "KC_P": {
            "key": "HU_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "HU_ODAC",
            "label": "Ő",
        }
        "KC_RBRC": {
            "key": "HU_UACU",
            "label": "Ú",
        }
        "KC_A": {
            "key": "HU_A",
            "label": "A",
        }
        "KC_S": {
            "key": "HU_S",
            "label": "S",
        }
        "KC_D": {
            "key": "HU_D",
            "label": "D",
        }
        "KC_F": {
            "key": "HU_F",
            "label": "F",
        }
        "KC_G": {
            "key": "HU_G",
            "label": "G",
        }
        "KC_H": {
            "key": "HU_H",
            "label": "H",
        }
        "KC_J": {
            "key": "HU_J",
            "label": "J",
        }
        "KC_K": {
            "key": "HU_K",
            "label": "K",
        }
        "KC_L": {
            "key": "HU_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "HU_EACU",
            "label": "É",
        }
        "KC_QUOT": {
            "key": "HU_AACU",
            "label": "Á",
        }
        "KC_NUHS": {
            "key": "HU_UDAC",
            "label": "Ű",
        }
        "KC_NUBS": {
            "key": "HU_IACU",
            "label": "Í",
        }
        "KC_Z": {
            "key": "HU_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "HU_X",
            "label": "X",
        }
        "KC_C": {
            "key": "HU_C",
            "label": "C",
        }
        "KC_V": {
            "key": "HU_V",
            "label": "V",
        }
        "KC_B": {
            "key": "HU_B",
            "label": "B",
        }
        "KC_N": {
            "key": "HU_N",
            "label": "N",
        }
        "KC_M": {
            "key": "HU_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "HU_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "HU_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "HU_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ § │ ' │ " │ + │ ! │ % │ / │ = │ ( │ ) │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │ ? │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(HU_0)": {
            "key": "HU_SECT",
            "label": "§",
        }
        "S(HU_1)": {
            "key": "HU_QUOT",
            "label": "'",
        }
        "S(HU_2)": {
            "key": "HU_DQUO",
            "label": "\"",
        }
        "S(HU_3)": {
            "key": "HU_PLUS",
            "label": "+",
        }
        "S(HU_4)": {
            "key": "HU_EXLM",
            "label": "!",
        }
        "S(HU_5)": {
            "key": "HU_PERC",
            "label": "%",
        }
        "S(HU_6)": {
            "key": "HU_SLSH",
            "label": "/",
        }
        "S(HU_7)": {
            "key": "HU_EQL",
            "label": "=",
        }
        "S(HU_8)": {
            "key": "HU_LPRN",
            "label": "(",
        }
        "S(HU_9)": {
            "key": "HU_RPRN",
            "label": ")",
        }
        "S(HU_COMM)": {
            "key": "HU_QUES",
            "label": "?",
        }
        "S(HU_DOT)": {
            "key": "HU_COLN",
            "label": ":",
        }
        "S(HU_MINS)": {
            "key": "HU_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ~ │ ˇ │ ^ │ ˘ │ ° │ ˛ │ ` │ ˙ │ ´ │ ˝ │ ¨ │ ¸ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ \ │ | │ Ä │   │   │   │ € │   │   │   │ ÷ │ × │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ ä │ đ │ Đ │ [ │ ] │   │   │ ł │ Ł │ $ │ ß │ ¤ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ > │ # │ & │ @ │ { │ } │   │ ; │   │ * │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(HU_1)": {
            "key": "HU_TILD",
            "label": "~",
        }
        "ALGR(HU_2)": {
            "key": "HU_CARN",
            "label": "ˇ (dead)",
        }
        "ALGR(HU_3)": {
            "key": "HU_CIRC",
            "label": "^ (dead)",
        }
        "ALGR(HU_4)": {
            "key": "HU_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(HU_5)": {
            "key": "HU_RNGA",
            "label": "° (dead)",
        }
        "ALGR(HU_6)": {
            "key": "HU_OGON",
            "label": "˛ (dead)",
        }
        "ALGR(HU_7)": {
            "key": "HU_GRV",
            "label": "`",
        }
        "ALGR(HU_8)": {
            "key": "HU_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(HU_9)": {
            "key": "HU_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(HU_ODIA)": {
            "key": "HU_DACU",
            "label": "˝ (dead)",
        }
        "ALGR(HU_UDIA)": {
            "key": "HU_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(HU_OACU)": {
            "key": "HU_CEDL",
            "label": "¸ (dead)",
        }
        "ALGR(HU_Q)": {
            "key": "HU_BSLS",
            "label": "\\",
        }
        "ALGR(HU_W)": {
            "key": "HU_PIPE",
            "label": "|",
        }
        "ALGR(HU_E)": {
            "key": "HU_CADI",
            "label": "Ä",
        }
        "ALGR(HU_U)": {
            "key": "HU_EURO",
            "label": "€",
        }
        "ALGR(HU_ODAC)": {
            "key": "HU_DIV",
            "label": "÷",
        }
        "ALGR(HU_UACU)": {
            "key": "HU_MUL",
            "label": "×",
        }
        "ALGR(HU_A)": {
            "key": "HU_LADI",
            "label": "ä",
        }
        "ALGR(HU_S)": {
            "key": "HU_LDST",
            "label": "đ",
        }
        "ALGR(HU_D)": {
            "key": "HU_CDST",
            "label": "Đ",
        }
        "ALGR(HU_F)": {
            "key": "HU_LBRC",
            "label": "[",
        }
        "ALGR(HU_G)": {
            "key": "HU_RBRC",
            "label": "]",
        }
        "ALGR(HU_K)": {
            "key": "HU_LLST",
            "label": "ł",
        }
        "ALGR(HU_L)": {
            "key": "HU_CLST",
            "label": "Ł",
        }
        "ALGR(HU_EACU)": {
            "key": "HU_DLR",
            "label": "$",
        }
        "ALGR(HU_AACU)": {
            "key": "HU_SS",
            "label": "ß",
        }
        "ALGR(HU_UDAC)": {
            "key": "HU_CURR",
            "label": "¤",
        }
        "ALGR(HU_IACU)": {
            "key": "HU_LABK",
            "label": "<",
        }
        "ALGR(HU_Y)": {
            "key": "HU_RABK",
            "label": ">",
        }
        "ALGR(HU_X)": {
            "key": "HU_HASH",
            "label": "#",
        }
        "ALGR(HU_C)": {
            "key": "HU_AMPR",
            "label": "&",
        }
        "ALGR(HU_V)": {
            "key": "HU_AT",
            "label": "@",
        }
        "ALGR(HU_B)": {
            "key": "HU_LCBR",
            "label": "{",
        }
        "ALGR(HU_N)": {
            "key": "HU_RCBR",
            "label": "}",
        }
        "ALGR(HU_COMM)": {
            "key": "HU_SCLN",
            "label": ";",
        }
        "ALGR(HU_MINS)": {
            "key": "HU_ASTR",
            "label": "*",
        }
    }
}