{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ \ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ' │ ì │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ è │ + │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ò │ à │ ù │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "IT_BSLS",
            "label": "\\",
        }
        "KC_1": {
            "key": "IT_1",
            "label": "1",
        }
        "KC_2": {
            "key": "IT_2",
            "label": "2",
        }
        "KC_3": {
            "key": "IT_3",
            "label": "3",
        }
        "KC_4": {
            "key": "IT_4",
            "label": "4",
        }
        "KC_5": {
            "key": "IT_5",
            "label": "5",
        }
        "KC_6": {
            "key": "IT_6",
            "label": "6",
        }
        "KC_7": {
            "key": "IT_7",
            "label": "7",
        }
        "KC_8": {
            "key": "IT_8",
            "label": "8",
        }
        "KC_9": {
            "key": "IT_9",
            "label": "9",
        }
        "KC_0": {
            "key": "IT_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "IT_QUOT",
            "label": "'",
        }
        "KC_EQL": {
            "key": "IT_IGRV",
            "label": "ì",
        }
        "KC_Q": {
            "key": "IT_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "IT_W",
            "label": "W",
        }
        "KC_E": {
            "key": "IT_E",
            "label": "E",
        }
        "KC_R": {
            "key": "IT_R",
            "label": "R",
        }
        "KC_T": {
            "key": "IT_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "IT_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "IT_U",
            "label": "U",
        }
        "KC_I": {
            "key": "IT_I",
            "label": "I",
        }
        "KC_O": {
            "key": "IT_O",
            "label": "O",
        }
        "KC_P": {
            "key": "IT_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "IT_EGRV",
            "label": "è",
        }
        "KC_RBRC": {
            "key": "IT_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "IT_A",
            "label": "A",
        }
        "KC_S": {
            "key": "IT_S",
            "label": "S",
        }
        "KC_D": {
            "key": "IT_D",
            "label": "D",
        }
        "KC_F": {
            "key": "IT_F",
            "label": "F",
        }
        "KC_G": {
            "key": "IT_G",
            "label": "G",
        }
        "KC_H": {
            "key": "IT_H",
            "label": "H",
        }
        "KC_J": {
            "key": "IT_J",
            "label": "J",
        }
        "KC_K": {
            "key": "IT_K",
            "label": "K",
        }
        "KC_L": {
            "key": "IT_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "IT_OGRV",
            "label": "ò",
        }
        "KC_QUOT": {
            "key": "IT_AGRV",
            "label": "à",
        }
        "KC_NUHS": {
            "key": "IT_UGRV",
            "label": "ù",
        }
        "KC_NUBS": {
            "key": "IT_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "IT_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "IT_X",
            "label": "X",
        }
        "KC_C": {
            "key": "IT_C",
            "label": "C",
        }
        "KC_B": {
            "key": "IT_B",
            "label": "B",
        }
        "KC_V": {
            "key": "IT_V",
            "label": "V",
        }
        "KC_N": {
            "key": "IT_N",
            "label": "N",
        }
        "KC_M": {
            "key": "IT_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "IT_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "IT_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "IT_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ | │ ! │ " │ £ │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ ^ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ é │ * │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ ç │ ° │ § │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(IT_BSLS)": {
            "key": "IT_PIPE",
            "label": "|",
        }
        "S(IT_1)": {
            "key": "IT_EXLM",
            "label": "!",
        }
        "S(IT_2)": {
            "key": "IT_DQUO",
            "label": "\"",
        }
        "S(IT_3)": {
            "key": "IT_PND",
            "label": "£",
        }
        "S(IT_4)": {
            "key": "IT_DLR",
            "label": "$",
        }
        "S(IT_5)": {
            "key": "IT_PERC",
            "label": "%",
        }
        "S(IT_6)": {
            "key": "IT_AMPR",
            "label": "&",
        }
        "S(IT_7)": {
            "key": "IT_SLSH",
            "label": "/",
        }
        "S(IT_8)": {
            "key": "IT_LPRN",
            "label": "(",
        }
        "S(IT_9)": {
            "key": "IT_RPRN",
            "label": ")",
        }
        "S(IT_0)": {
            "key": "IT_EQL",
            "label": "=",
        }
        "S(IT_QUOT)": {
            "key": "IT_QUES",
            "label": "?",
        }
        "S(IT_IGRV)": {
            "key": "IT_CIRC",
            "label": "^",
        }
        "S(IT_EGRV)": {
            "key": "IT_EACU",
            "label": "é",
        }
        "S(IT_PLUS)": {
            "key": "IT_ASTR",
            "label": "*",
        }
        "S(IT_OGRV)": {
            "key": "IT_CCED",
            "label": "ç",
        }
        "S(IT_AGRV)": {
            "key": "IT_DEG",
            "label": "°",
        }
        "S(IT_UGRV)": {
            "key": "IT_SECT",
            "label": "§",
        }
        "S(IT_LABK)": {
            "key": "IT_RABK",
            "label": ">",
        }
        "S(IT_DOT)": {
            "key": "IT_COLN",
            "label": ":",
        }
        "S(IT_COMM)": {
            "key": "IT_SCLN",
            "label": ";",
        }
        "S(IT_MINS)": {
            "key": "IT_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │   │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │   │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ @ │ # │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(IT_E)": {
            "key": "IT_EURO",
            "label": "€",
        }
        "ALGR(IT_EGRV)": {
            "key": "IT_LBRC",
            "label": "[",
        }
        "ALGR(IT_PLUS)": {
            "key": "IT_RBRC",
            "label": "]",
        }
        "ALGR(IT_OGRV)": {
            "key": "IT_AT",
            "label": "@",
        }
        "ALGR(IT_AGRV)": {
            "key": "IT_HASH",
            "label": "#",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │   │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(IT_EGRV))": {
            "key": "IT_LCBR",
            "label": "{",
        }
        "S(ALGR(IT_PLUS))": {
            "key": "IT_RCBR",
            "label": "}",
        }
    }
}