{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ² │ & │ é │ " │ ' │ ( │ § │ è │ ! │ ç │ à │ ) │ - │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ A │ Z │ E │ R │ T │ Y │ U │ I │ O │ P │ ^ │ $ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Q │ S │ D │ F │ G │ H │ J │ K │ L │ M │ ù │ µ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ W │ X │ C │ V │ B │ N │ , │ ; │ : │ = │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "BE_SUP2",
            "label": "²",
        }
        "KC_1": {
            "key": "BE_AMPR",
            "label": "&",
        }
        "KC_2": {
            "key": "BE_EACU",
            "label": "é",
        }
        "KC_3": {
            "key": "BE_DQUO",
            "label": "\"",
        }
        "KC_4": {
            "key": "BE_QUOT",
            "label": "'",
        }
        "KC_5": {
            "key": "BE_LPRN",
            "label": "(",
        }
        "KC_6": {
            "key": "BE_SECT",
            "label": "§",
        }
        "KC_7": {
            "key": "BE_EGRV",
            "label": "è",
        }
        "KC_8": {
            "key": "BE_EXLM",
            "label": "!",
        }
        "KC_9": {
            "key": "BE_CCED",
            "label": "ç",
        }
        "KC_0": {
            "key": "BE_AGRV",
            "label": "à",
        }
        "KC_MINS": {
            "key": "BE_RPRN",
            "label": ")",
        }
        "KC_EQL": {
            "key": "BE_MINS",
            "label": "-",
        }
        "KC_Q": {
            "key": "BE_A",
            "label": "A",
        }
        "KC_W": {
            "key": "BE_Z",
            "label": "Z",
        }
        "KC_E": {
            "key": "BE_E",
            "label": "E",
        }
        "KC_R": {
            "key": "BE_R",
            "label": "R",
        }
        "KC_T": {
            "key": "BE_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "BE_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "BE_U",
            "label": "U",
        }
        "KC_I": {
            "key": "BE_I",
            "label": "I",
        }
        "KC_O": {
            "key": "BE_O",
            "label": "O",
        }
        "KC_P": {
            "key": "BE_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "BE_DCIR",
            "label": "^ (dead)",
        }
        "KC_RBRC": {
            "key": "BE_DLR",
            "label": "$",
        }
        "KC_A": {
            "key": "BE_Q",
            "label": "Q",
        }
        "KC_S": {
            "key": "BE_S",
            "label": "S",
        }
        "KC_D": {
            "key": "BE_D",
            "label": "D",
        }
        "KC_F": {
            "key": "BE_F",
            "label": "F",
        }
        "KC_G": {
            "key": "BE_G",
            "label": "G",
        }
        "KC_H": {
            "key": "BE_H",
            "label": "H",
        }
        "KC_J": {
            "key": "BE_J",
            "label": "J",
        }
        "KC_K": {
            "key": "BE_K",
            "label": "K",
        }
        "KC_L": {
            "key": "BE_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "BE_M",
            "label": "M",
        }
        "KC_QUOT": {
            "key": "BE_UGRV",
            "label": "ù",
        }
        "KC_NUHS": {
            "key": "BE_MICR",
            "label": "µ",
        }
        "KC_NUBS": {
            "key": "BE_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "BE_W",
            "label": "W",
        }
        "KC_X": {
            "key": "BE_X",
            "label": "X",
        }
        "KC_C": {
            "key": "BE_C",
            "label": "C",
        }
        "KC_V": {
            "key": "BE_V",
            "label": "V",
        }
        "KC_B": {
            "key": "BE_B",
            "label": "B",
        }
        "KC_N": {
            "key": "BE_N",
            "label": "N",
        }
        "KC_M": {
            "key": "BE_COMM",
            "label": ",",
        }
        "KC_COMM": {
            "key": "BE_SCLN",
            "label": ";",
        }
        "KC_DOT": {
            "key": "BE_COLN",
            "label": ":",
        }
        "KC_SLSH": {
            "key": "BE_EQL",
            "label": "=",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ³ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ° │ _ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ ¨ │ * │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ % │ £ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │ ? │ . │ / │ + │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(BE_SUP2)": {
            "key": "BE_SUP3",
            "label": "³",
        }
        "S(BE_AMPR)": {
            "key": "BE_1",
            "label": "1",
        }
        "S(BE_EACU)": {
            "key": "BE_2",
            "label": "2",
        }
        "S(BE_DQUO)": {
            "key": "BE_3",
            "label": "3",
        }
        "S(BE_QUOT)": {
            "key": "BE_4",
            "label": "4",
        }
        "S(BE_LPRN)": {
            "key": "BE_5",
            "label": "5",
        }
        "S(BE_SECT)": {
            "key": "BE_6",
            "label": "6",
        }
        "S(BE_EGRV)": {
            "key": "BE_7",
            "label": "7",
        }
        "S(BE_EXLM)": {
            "key": "BE_8",
            "label": "8",
        }
        "S(BE_CCED)": {
            "key": "BE_9",
            "label": "9",
        }
        "S(BE_AGRV)": {
            "key": "BE_0",
            "label": "0",
        }
        "S(BE_RPRN)": {
            "key": "BE_DEG",
            "label": "°",
        }
        "S(BE_MINS)": {
            "key": "BE_UNDS",
            "label": "_",
        }
        "S(BE_DCIR)": {
            "key": "BE_DIAE",
            "label": "¨ (dead)",
        }
        "S(BE_DLR)": {
            "key": "BE_ASTR",
            "label": "*",
        }
        "S(BE_UGRV)": {
            "key": "BE_PERC",
            "label": "%",
        }
        "S(BE_MICR)": {
            "key": "BE_PND",
            "label": "£",
        }
        "S(BE_LABK)": {
            "key": "BE_RABK",
            "label": ">",
        }
        "S(BE_COMM)": {
            "key": "BE_QUES",
            "label": "?",
        }
        "S(BE_SCLN)": {
            "key": "BE_DOT",
            "label": ".",
        }
        "S(BE_COLN)": {
            "key": "BE_SLSH",
            "label": "/",
        }
        "S(BE_EQL)": {
            "key": "BE_PLUS",
            "label": "+",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ | │ @ │ # │   │   │ ^ │   │   │ { │ } │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │   │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ´ │ ` │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │   │   │   │   │   │   │   │   │   │ ~ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(BE_AMPR)": {
            "key": "BE_PIPE",
            "label": "|",
        }
        "ALGR(BE_EACU)": {
            "key": "BE_AT",
            "label": "@",
        }
        "ALGR(BE_DQUO)": {
            "key": "BE_HASH",
            "label": "#",
        }
        "ALGR(BE_SECT)": {
            "key": "BE_CIRC",
            "label": "^",
        }
        "ALGR(BE_CCED)": {
            "key": "BE_LCBR",
            "label": "{",
        }
        "ALGR(BE_AGRV)": {
            "key": "BE_RCBR",
            "label": "}",
        }
        "ALGR(BE_E)": {
            "key": "BE_EURO",
            "label": "€",
        }
        "ALGR(BE_DCIR)": {
            "key": "BE_LBRC",
            "label": "[",
        }
        "ALGR(BE_DLR)": {
            "key": "BE_RBRC",
            "label": "]",
        }
        "ALGR(BE_UGRV)": {
            "key": "BE_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(BE_MICR)": {
            "key": "BE_GRV",
            "label": "` (dead)",
        }
        "ALGR(BE_LABK)": {
            "key": "BE_BSLS",
            "label": "\\",
        }
        "ALGR(BE_EQL)": {
            "key": "BE_TILD",
            "label": "~",
        }
    }
}