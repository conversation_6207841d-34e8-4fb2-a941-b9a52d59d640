{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ „ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Ă │ Î │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ș │ Ț │ Â │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "RO_DLQU",
            "label": "„",
        }
        "KC_1": {
            "key": "RO_1",
            "label": "1",
        }
        "KC_2": {
            "key": "RO_2",
            "label": "2",
        }
        "KC_3": {
            "key": "RO_3",
            "label": "3",
        }
        "KC_4": {
            "key": "RO_4",
            "label": "4",
        }
        "KC_5": {
            "key": "RO_5",
            "label": "5",
        }
        "KC_6": {
            "key": "RO_6",
            "label": "6",
        }
        "KC_7": {
            "key": "RO_7",
            "label": "7",
        }
        "KC_8": {
            "key": "RO_8",
            "label": "8",
        }
        "KC_9": {
            "key": "RO_9",
            "label": "9",
        }
        "KC_0": {
            "key": "RO_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "RO_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "RO_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "RO_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "RO_W",
            "label": "W",
        }
        "KC_E": {
            "key": "RO_E",
            "label": "E",
        }
        "KC_R": {
            "key": "RO_R",
            "label": "R",
        }
        "KC_T": {
            "key": "RO_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "RO_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "RO_U",
            "label": "U",
        }
        "KC_I": {
            "key": "RO_I",
            "label": "I",
        }
        "KC_O": {
            "key": "RO_O",
            "label": "O",
        }
        "KC_P": {
            "key": "RO_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "RO_ABRV",
            "label": "Ă",
        }
        "KC_RBRC": {
            "key": "RO_ICIR",
            "label": "Î",
        }
        "KC_A": {
            "key": "RO_A",
            "label": "A",
        }
        "KC_S": {
            "key": "RO_S",
            "label": "S",
        }
        "KC_D": {
            "key": "RO_D",
            "label": "D",
        }
        "KC_F": {
            "key": "RO_F",
            "label": "F",
        }
        "KC_G": {
            "key": "RO_G",
            "label": "G",
        }
        "KC_H": {
            "key": "RO_H",
            "label": "H",
        }
        "KC_J": {
            "key": "RO_J",
            "label": "J",
        }
        "KC_K": {
            "key": "RO_K",
            "label": "K",
        }
        "KC_L": {
            "key": "RO_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "RO_SCOM",
            "label": "Ș",
        }
        "KC_QUOT": {
            "key": "RO_TCOM",
            "label": "Ț",
        }
        "KC_NUHS": {
            "key": "RO_ACIR",
            "label": "Â",
        }
        "KC_NUBS": {
            "key": "RO_BSLS",
            "label": "\\",
        }
        "KC_Z": {
            "key": "RO_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "RO_X",
            "label": "X",
        }
        "KC_C": {
            "key": "RO_C",
            "label": "C",
        }
        "KC_V": {
            "key": "RO_V",
            "label": "V",
        }
        "KC_B": {
            "key": "RO_B",
            "label": "B",
        }
        "KC_N": {
            "key": "RO_N",
            "label": "N",
        }
        "KC_M": {
            "key": "RO_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "RO_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "RO_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "RO_SLSH",
            "label": "/",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ” │ ! │ @ │ # │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │   │ ; │ : │ ? │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(RO_DLQU)": {
            "key": "RO_RDQU",
            "label": "”",
        }
        "S(RO_1)": {
            "key": "RO_EXLM",
            "label": "!",
        }
        "S(RO_2)": {
            "key": "RO_AT",
            "label": "@",
        }
        "S(RO_3)": {
            "key": "RO_HASH",
            "label": "#",
        }
        "S(RO_4)": {
            "key": "RO_DLR",
            "label": "$",
        }
        "S(RO_5)": {
            "key": "RO_PERC",
            "label": "%",
        }
        "S(RO_6)": {
            "key": "RO_CIRC",
            "label": "^",
        }
        "S(RO_7)": {
            "key": "RO_AMPR",
            "label": "&",
        }
        "S(RO_8)": {
            "key": "RO_ASTR",
            "label": "*",
        }
        "S(RO_9)": {
            "key": "RO_LPRN",
            "label": "(",
        }
        "S(RO_0)": {
            "key": "RO_RPRN",
            "label": ")",
        }
        "S(RO_MINS)": {
            "key": "RO_UNDS",
            "label": "_",
        }
        "S(RO_EQL)": {
            "key": "RO_PLUS",
            "label": "+",
        }
        "S(RO_BSLS)": {
            "key": "RO_PIPE",
            "label": "|",
        }
        "S(RO_COMM)": {
            "key": "RO_SCLN",
            "label": ";",
        }
        "S(RO_DOT)": {
            "key": "RO_COLN",
            "label": ":",
        }
        "S(RO_SLSH)": {
            "key": "RO_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ ~ │ ˇ │ ^ │ ˘ │ ° │ ˛ │ ` │ ˙ │ ´ │ ˝ │ ¨ │ ¸ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ € │   │   │   │   │   │   │ § │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │ ß │ Đ │   │   │   │   │   │ Ł │   │ ' │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │ © │   │   │   │   │ < │ > │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(RO_DLQU)": {
            "key": "RO_GRV",
            "label": "`",
        }
        "ALGR(RO_1)": {
            "key": "RO_DTIL",
            "label": "~ (dead)",
        }
        "ALGR(RO_2)": {
            "key": "RO_CARN",
            "label": "ˇ (dead)",
        }
        "ALGR(RO_3)": {
            "key": "RO_DCIR",
            "label": "^ (dead)",
        }
        "ALGR(RO_4)": {
            "key": "RO_BREV",
            "label": "˘ (dead)",
        }
        "ALGR(RO_5)": {
            "key": "RO_RNGA",
            "label": "° (dead)",
        }
        "ALGR(RO_6)": {
            "key": "RO_OGON",
            "label": "˛ (dead)",
        }
        "ALGR(RO_7)": {
            "key": "RO_DGRV",
            "label": "` (dead)",
        }
        "ALGR(RO_8)": {
            "key": "RO_DOTA",
            "label": "˙ (dead)",
        }
        "ALGR(RO_9)": {
            "key": "RO_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(RO_0)": {
            "key": "RO_DACU",
            "label": "˝ (dead)",
        }
        "ALGR(RO_MINS)": {
            "key": "RO_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(RO_EQL)": {
            "key": "RO_CEDL",
            "label": "¸ (dead)",
        }
        "ALGR(RO_E)": {
            "key": "RO_EURO",
            "label": "€",
        }
        "ALGR(RO_P)": {
            "key": "RO_SECT",
            "label": "§",
        }
        "ALGR(RO_ABRV)": {
            "key": "RO_LBRC",
            "label": "[",
        }
        "ALGR(RO_ICIR)": {
            "key": "RO_RBRC",
            "label": "]",
        }
        "ALGR(RO_S)": {
            "key": "RO_SS",
            "label": "ß",
        }
        "ALGR(RO_D)": {
            "key": "RO_DSTR",
            "label": "Đ",
        }
        "ALGR(RO_L)": {
            "key": "RO_LSTR",
            "label": "Ł",
        }
        "ALGR(RO_TCOM)": {
            "key": "RO_QUOT",
            "label": "'",
        }
        "ALGR(RO_C)": {
            "key": "RO_COPY",
            "label": "©",
        }
        "ALGR(RO_COMM)": {
            "key": "RO_LABK",
            "label": "<",
        }
        "ALGR(RO_DOT)": {
            "key": "RO_RABK",
            "label": ">",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ~ │   │   │   │   │   │   │   │   │   │   │ – │ ± │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ " │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │ « │ » │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(RO_DLQU))": {
            "key": "RO_TILD",
            "label": "~",
        }
        "S(ALGR(RO_MINS))": {
            "key": "RO_NDSH",
            "label": "–",
        }
        "S(ALGR(RO_EQL))": {
            "key": "RO_PLMN",
            "label": "±",
        }
        "S(ALGR(RO_ABRV))": {
            "key": "RO_LCBR",
            "label": "{",
        }
        "S(ALGR(RO_ICIR))": {
            "key": "RO_RCBR",
            "label": "}",
        }
        "S(ALGR(RO_TCOM))": {
            "key": "RO_DQUO",
            "label": "\"",
        }
        "S(ALGR(RO_COMM))": {
            "key": "RO_LDAQ",
            "label": "«",
        }
        "S(ALGR(RO_DOT))": {
            "key": "RO_RDAQ",
            "label": "»",
        }
    }
}