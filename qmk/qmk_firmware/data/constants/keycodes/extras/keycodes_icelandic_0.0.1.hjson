{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ° │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ Ö │ - │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Ð │ ' │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Æ │ ´ │ + │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ Þ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "IS_RNGA",
            "label": "° (dead)",
        }
        "KC_1": {
            "key": "IS_1",
            "label": "1",
        }
        "KC_2": {
            "key": "IS_2",
            "label": "2",
        }
        "KC_3": {
            "key": "IS_3",
            "label": "3",
        }
        "KC_4": {
            "key": "IS_4",
            "label": "4",
        }
        "KC_5": {
            "key": "IS_5",
            "label": "5",
        }
        "KC_6": {
            "key": "IS_6",
            "label": "6",
        }
        "KC_7": {
            "key": "IS_7",
            "label": "7",
        }
        "KC_8": {
            "key": "IS_8",
            "label": "8",
        }
        "KC_9": {
            "key": "IS_9",
            "label": "9",
        }
        "KC_0": {
            "key": "IS_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "IS_ODIA",
            "label": "Ö",
        }
        "KC_EQL": {
            "key": "IS_MINS",
            "label": "-",
        }
        "KC_Q": {
            "key": "IS_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "IS_W",
            "label": "W",
        }
        "KC_E": {
            "key": "IS_E",
            "label": "E",
        }
        "KC_R": {
            "key": "IS_R",
            "label": "R",
        }
        "KC_T": {
            "key": "IS_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "IS_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "IS_U",
            "label": "U",
        }
        "KC_I": {
            "key": "IS_I",
            "label": "I",
        }
        "KC_O": {
            "key": "IS_O",
            "label": "O",
        }
        "KC_P": {
            "key": "IS_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "IS_ETH",
            "label": "Ð",
        }
        "KC_RBRC": {
            "key": "IS_QUOT",
            "label": "'",
        }
        "KC_A": {
            "key": "IS_A",
            "label": "A",
        }
        "KC_S": {
            "key": "IS_S",
            "label": "S",
        }
        "KC_D": {
            "key": "IS_D",
            "label": "D",
        }
        "KC_F": {
            "key": "IS_F",
            "label": "F",
        }
        "KC_G": {
            "key": "IS_G",
            "label": "G",
        }
        "KC_H": {
            "key": "IS_H",
            "label": "H",
        }
        "KC_J": {
            "key": "IS_J",
            "label": "J",
        }
        "KC_K": {
            "key": "IS_K",
            "label": "K",
        }
        "KC_L": {
            "key": "IS_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "IS_AE",
            "label": "Æ",
        }
        "KC_QUOT": {
            "key": "IS_ACUT",
            "label": "´ (dead)",
        }
        "KC_NUHS": {
            "key": "IS_PLUS",
            "label": "+",
        }
        "KC_NUBS": {
            "key": "IS_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "IS_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "IS_X",
            "label": "X",
        }
        "KC_C": {
            "key": "IS_C",
            "label": "C",
        }
        "KC_V": {
            "key": "IS_V",
            "label": "V",
        }
        "KC_B": {
            "key": "IS_B",
            "label": "B",
        }
        "KC_N": {
            "key": "IS_N",
            "label": "N",
        }
        "KC_M": {
            "key": "IS_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "IS_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "IS_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "IS_THRN",
            "label": "Þ",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¨ │ ! │ " │ # │ $ │ % │ & │ / │ ( │ ) │ = │   │ _ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ? │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(IS_RNGA)": {
            "key": "IS_DIAE",
            "label": "¨ (dead)",
        }
        "S(IS_1)": {
            "key": "IS_EXLM",
            "label": "!",
        }
        "S(IS_2)": {
            "key": "IS_DQUO",
            "label": "\"",
        }
        "S(IS_3)": {
            "key": "IS_HASH",
            "label": "#",
        }
        "S(IS_4)": {
            "key": "IS_DLR",
            "label": "$",
        }
        "S(IS_5)": {
            "key": "IS_PERC",
            "label": "%",
        }
        "S(IS_6)": {
            "key": "IS_AMPR",
            "label": "&",
        }
        "S(IS_7)": {
            "key": "IS_SLSH",
            "label": "/",
        }
        "S(IS_8)": {
            "key": "IS_LPRN",
            "label": "(",
        }
        "S(IS_9)": {
            "key": "IS_RPRN",
            "label": ")",
        }
        "S(IS_0)": {
            "key": "IS_EQL",
            "label": "=",
        }
        "S(IS_MINS)": {
            "key": "IS_UNDS",
            "label": "_",
        }
        "S(IS_QUOT)": {
            "key": "IS_QUES",
            "label": "?",
        }
        "S(IS_PLUS)": {
            "key": "IS_ASTR",
            "label": "*",
        }
        "S(IS_LABK)": {
            "key": "IS_RABK",
            "label": ">",
        }
        "S(IS_COMM)": {
            "key": "IS_SCLN",
            "label": ";",
        }
        "S(IS_DOT)": {
            "key": "IS_COLN",
            "label": ":",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ° │   │   │   │   │   │   │ { │ [ │ ] │ } │ \ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ @ │   │ € │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ^ │ ` │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │ µ │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(IS_RNGA)": {
            "key": "IS_DEG",
            "label": "°",
        }
        "ALGR(IS_7)": {
            "key": "IS_LCBR",
            "label": "{",
        }
        "ALGR(IS_8)": {
            "key": "IS_LBRC",
            "label": "[",
        }
        "ALGR(IS_9)": {
            "key": "IS_RBRC",
            "label": "]",
        }
        "ALGR(IS_0)": {
            "key": "IS_RCBR",
            "label": "}",
        }
        "ALGR(IS_ODIA)": {
            "key": "IS_BSLS",
            "label": "\\",
        }
        "ALGR(IS_Q)": {
            "key": "IS_AT",
            "label": "@",
        }
        "ALGR(IS_E)": {
            "key": "IS_EURO",
            "label": "€",
        }
        "ALGR(IS_QUOT)": {
            "key": "IS_TILD",
            "label": "~",
        }
        "ALGR(IS_ACUT)": {
            "key": "IS_CIRC",
            "label": "^ (dead)",
        }
        "ALGR(IS_PLUS)": {
            "key": "IS_GRV",
            "label": "` (dead)",
        }
        "ALGR(IS_LABK)": {
            "key": "IS_PIPE",
            "label": "|",
        }
        "ALGR(IS_M)": {
            "key": "IS_MICR",
            "label": "µ",
        }
    }
}