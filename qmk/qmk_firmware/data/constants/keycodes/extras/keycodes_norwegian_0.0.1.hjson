{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ | │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ + │ \ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ Å │ ¨ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ø │ Æ │ ' │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "NO_PIPE",
            "label": "|",
        }
        "KC_1": {
            "key": "NO_1",
            "label": "1",
        }
        "KC_2": {
            "key": "NO_2",
            "label": "2",
        }
        "KC_3": {
            "key": "NO_3",
            "label": "3",
        }
        "KC_4": {
            "key": "NO_4",
            "label": "4",
        }
        "KC_5": {
            "key": "NO_5",
            "label": "5",
        }
        "KC_6": {
            "key": "NO_6",
            "label": "6",
        }
        "KC_7": {
            "key": "NO_7",
            "label": "7",
        }
        "KC_8": {
            "key": "NO_8",
            "label": "8",
        }
        "KC_9": {
            "key": "NO_9",
            "label": "9",
        }
        "KC_0": {
            "key": "NO_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "NO_PLUS",
            "label": "+",
        }
        "KC_EQL": {
            "key": "NO_BSLS",
            "label": "\\",
        }
        "KC_Q": {
            "key": "NO_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "NO_W",
            "label": "W",
        }
        "KC_E": {
            "key": "NO_E",
            "label": "E",
        }
        "KC_R": {
            "key": "NO_R",
            "label": "R",
        }
        "KC_T": {
            "key": "NO_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "NO_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "NO_U",
            "label": "U",
        }
        "KC_I": {
            "key": "NO_I",
            "label": "I",
        }
        "KC_O": {
            "key": "NO_O",
            "label": "O",
        }
        "KC_P": {
            "key": "NO_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "NO_ARNG",
            "label": "Å",
        }
        "KC_RBRC": {
            "key": "NO_DIAE",
            "label": "¨ (dead)",
        }
        "KC_A": {
            "key": "NO_A",
            "label": "A",
        }
        "KC_S": {
            "key": "NO_S",
            "label": "S",
        }
        "KC_D": {
            "key": "NO_D",
            "label": "D",
        }
        "KC_F": {
            "key": "NO_F",
            "label": "F",
        }
        "KC_G": {
            "key": "NO_G",
            "label": "G",
        }
        "KC_H": {
            "key": "NO_H",
            "label": "H",
        }
        "KC_J": {
            "key": "NO_J",
            "label": "J",
        }
        "KC_K": {
            "key": "NO_K",
            "label": "K",
        }
        "KC_L": {
            "key": "NO_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "NO_OSTR",
            "label": "Ø",
        }
        "KC_QUOT": {
            "key": "NO_AE",
            "label": "Æ",
        }
        "KC_NUHS": {
            "key": "NO_QUOT",
            "label": "'",
        }
        "KC_NUBS": {
            "key": "NO_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "NO_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "NO_X",
            "label": "X",
        }
        "KC_C": {
            "key": "NO_C",
            "label": "C",
        }
        "KC_V": {
            "key": "NO_V",
            "label": "V",
        }
        "KC_B": {
            "key": "NO_B",
            "label": "B",
        }
        "KC_N": {
            "key": "NO_N",
            "label": "N",
        }
        "KC_M": {
            "key": "NO_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "NO_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "NO_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "NO_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ § │ ! │ " │ # │ ¤ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ^ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ * │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(NO_PIPE)": {
            "key": "NO_SECT",
            "label": "§",
        }
        "S(NO_1)": {
            "key": "NO_EXLM",
            "label": "!",
        }
        "S(NO_2)": {
            "key": "NO_DQUO",
            "label": "\"",
        }
        "S(NO_3)": {
            "key": "NO_HASH",
            "label": "#",
        }
        "S(NO_4)": {
            "key": "NO_CURR",
            "label": "¤",
        }
        "S(NO_5)": {
            "key": "NO_PERC",
            "label": "%",
        }
        "S(NO_6)": {
            "key": "NO_AMPR",
            "label": "&",
        }
        "S(NO_7)": {
            "key": "NO_SLSH",
            "label": "/",
        }
        "S(NO_8)": {
            "key": "NO_LPRN",
            "label": "(",
        }
        "S(NO_9)": {
            "key": "NO_RPRN",
            "label": ")",
        }
        "S(NO_0)": {
            "key": "NO_EQL",
            "label": "=",
        }
        "S(NO_PLUS)": {
            "key": "NO_QUES",
            "label": "?",
        }
        "S(NO_BSLS)": {
            "key": "NO_GRV",
            "label": "` (dead)",
        }
        "S(NO_DIAE)": {
            "key": "NO_CIRC",
            "label": "^ (dead)",
        }
        "S(NO_QUOT)": {
            "key": "NO_ASTR",
            "label": "*",
        }
        "S(NO_LABK)": {
            "key": "NO_RABK",
            "label": ">",
        }
        "S(NO_COMM)": {
            "key": "NO_SCLN",
            "label": ";",
        }
        "S(NO_DOT)": {
            "key": "NO_COLN",
            "label": ":",
        }
        "S(NO_MINS)": {
            "key": "NO_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ @ │ £ │ $ │ € │   │ { │ [ │ ] │ } │   │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │ µ │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(NO_2)": {
            "key": "NO_AT",
            "label": "@",
        }
        "ALGR(NO_3)": {
            "key": "NO_PND",
            "label": "£",
        }
        "ALGR(NO_4)": {
            "key": "NO_DLR",
            "label": "$",
        }
        "ALGR(NO_5)": {
            "key": "NO_EURO",
            "label": "€",
        }
        "ALGR(NO_7)": {
            "key": "NO_LCBR",
            "label": "{",
        }
        "ALGR(NO_8)": {
            "key": "NO_LBRC",
            "label": "[",
        }
        "ALGR(NO_9)": {
            "key": "NO_RBRC",
            "label": "]",
        }
        "ALGR(NO_0)": {
            "key": "NO_RCBR",
            "label": "}",
        }
        "ALGR(NO_BSLS)": {
            "key": "NO_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(NO_DIAE)": {
            "key": "NO_TILD",
            "label": "~ (dead)",
        }
        "ALGR(NO_M)": {
            "key": "NO_MICR",
            "label": "µ",
        }
    }
}