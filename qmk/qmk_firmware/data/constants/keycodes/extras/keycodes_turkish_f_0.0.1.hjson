{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ + │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ / │ - │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ F │ G │ Ğ │ I │ O │ D │ R │ N │ H │ P │ Q │ W │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ U │ İ │ E │ A │ Ü │ T │ K │ M │ L │ Y │ Ş │ X │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ J │ Ö │ V │ C │ Ç │ Z │ S │ B │ . │ , │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "TR_PLUS",
            "label": "+",
        }
        "KC_1": {
            "key": "TR_1",
            "label": "1",
        }
        "KC_2": {
            "key": "TR_2",
            "label": "2",
        }
        "KC_3": {
            "key": "TR_3",
            "label": "3",
        }
        "KC_4": {
            "key": "TR_4",
            "label": "4",
        }
        "KC_5": {
            "key": "TR_5",
            "label": "5",
        }
        "KC_6": {
            "key": "TR_6",
            "label": "6",
        }
        "KC_7": {
            "key": "TR_7",
            "label": "7",
        }
        "KC_8": {
            "key": "TR_8",
            "label": "8",
        }
        "KC_9": {
            "key": "TR_9",
            "label": "9",
        }
        "KC_0": {
            "key": "TR_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "TR_SLSH",
            "label": "/",
        }
        "KC_EQL": {
            "key": "TR_MINS",
            "label": "-",
        }
        "KC_Q": {
            "key": "TR_F",
            "label": "F",
        }
        "KC_W": {
            "key": "TR_G",
            "label": "G",
        }
        "KC_E": {
            "key": "TR_GBRV",
            "label": "Ğ",
        }
        "KC_R": {
            "key": "TR_I",
            "label": "I",
        }
        "KC_T": {
            "key": "TR_O",
            "label": "O",
        }
        "KC_Y": {
            "key": "TR_D",
            "label": "D",
        }
        "KC_U": {
            "key": "TR_R",
            "label": "R",
        }
        "KC_I": {
            "key": "TR_N",
            "label": "N",
        }
        "KC_O": {
            "key": "TR_H",
            "label": "H",
        }
        "KC_P": {
            "key": "TR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "TR_Q",
            "label": "Q",
        }
        "KC_RBRC": {
            "key": "TR_W",
            "label": "W",
        }
        "KC_A": {
            "key": "TR_U",
            "label": "U",
        }
        "KC_S": {
            "key": "TR_IDOT",
            "label": "İ",
        }
        "KC_D": {
            "key": "TR_E",
            "label": "E",
        }
        "KC_F": {
            "key": "TR_A",
            "label": "A",
        }
        "KC_G": {
            "key": "TR_UDIA",
            "label": "Ü",
        }
        "KC_H": {
            "key": "TR_T",
            "label": "T",
        }
        "KC_J": {
            "key": "TR_K",
            "label": "K",
        }
        "KC_K": {
            "key": "TR_M",
            "label": "M",
        }
        "KC_L": {
            "key": "TR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "TR_Y",
            "label": "Y",
        }
        "KC_QUOT": {
            "key": "TR_SCED",
            "label": "Ş",
        }
        "KC_NUHS": {
            "key": "TR_X",
            "label": "X",
        }
        "KC_NUBS": {
            "key": "TR_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "TR_J",
            "label": "J",
        }
        "KC_X": {
            "key": "TR_ODIA",
            "label": "Ö",
        }
        "KC_C": {
            "key": "TR_V",
            "label": "V",
        }
        "KC_V": {
            "key": "TR_C",
            "label": "C",
        }
        "KC_B": {
            "key": "TR_CCED",
            "label": "Ç",
        }
        "KC_N": {
            "key": "TR_Z",
            "label": "Z",
        }
        "KC_M": {
            "key": "TR_S",
            "label": "S",
        }
        "KC_COMM": {
            "key": "TR_B",
            "label": "B",
        }
        "KC_DOT": {
            "key": "TR_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "TR_COMM",
            "label": ",",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ * │ ! │ " │ ^ │ $ │ % │ & │ ' │ ( │ ) │ = │ ? │ _ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │   │ : │ ; │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(TR_PLUS)": {
            "key": "TR_ASTR",
            "label": "*",
        }
        "S(TR_1)": {
            "key": "TR_EXLM",
            "label": "!",
        }
        "S(TR_2)": {
            "key": "TR_DQUO",
            "label": "\"",
        }
        "S(TR_3)": {
            "key": "TR_CIRC",
            "label": "^ (dead)",
        }
        "S(TR_4)": {
            "key": "TR_DLR",
            "label": "$",
        }
        "S(TR_5)": {
            "key": "TR_PERC",
            "label": "%",
        }
        "S(TR_6)": {
            "key": "TR_AMPR",
            "label": "&",
        }
        "S(TR_7)": {
            "key": "TR_QUOT",
            "label": "'",
        }
        "S(TR_8)": {
            "key": "TR_LPRN",
            "label": "(",
        }
        "S(TR_9)": {
            "key": "TR_RPRN",
            "label": ")",
        }
        "S(TR_0)": {
            "key": "TR_EQL",
            "label": "=",
        }
        "S(TR_SLSH)": {
            "key": "TR_QUES",
            "label": "?",
        }
        "S(TR_MINS)": {
            "key": "TR_UNDS",
            "label": "_",
        }
        "S(TR_LABK)": {
            "key": "TR_RABK",
            "label": ">",
        }
        "S(TR_DOT)": {
            "key": "TR_COLN",
            "label": ":",
        }
        "S(TR_COMM)": {
            "key": "TR_SCLN",
            "label": ";",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¬ │ ¹ │ ² │ # │ ¼ │ ½ │ ¾ │ { │ [ │ ] │ } │ \ │ | │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ @ │   │   │ ¶ │   │ ¥ │   │   │ Ø │ £ │ ¨ │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Æ │ ß │ € │   │   │ ₺ │   │   │   │ ´ │   │ ` │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │ « │ » │ ¢ │   │   │   │ µ │ × │ ÷ │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(TR_PLUS)": {
            "key": "TR_NOT",
            "label": "¬",
        }
        "ALGR(TR_1)": {
            "key": "TR_SUP1",
            "label": "¹",
        }
        "ALGR(TR_2)": {
            "key": "TR_SUP2",
            "label": "²",
        }
        "ALGR(TR_3)": {
            "key": "TR_HASH",
            "label": "#",
        }
        "ALGR(TR_4)": {
            "key": "TR_QRTR",
            "label": "¼",
        }
        "ALGR(TR_5)": {
            "key": "TR_HALF",
            "label": "½",
        }
        "ALGR(TR_6)": {
            "key": "TR_TQTR",
            "label": "¾",
        }
        "ALGR(TR_7)": {
            "key": "TR_LCBR",
            "label": "{",
        }
        "ALGR(TR_8)": {
            "key": "TR_LBRC",
            "label": "[",
        }
        "ALGR(TR_9)": {
            "key": "TR_RBRC",
            "label": "]",
        }
        "ALGR(TR_0)": {
            "key": "TR_RCBR",
            "label": "}",
        }
        "ALGR(TR_SLSH)": {
            "key": "TR_BSLS",
            "label": "\\",
        }
        "ALGR(TR_MINS)": {
            "key": "TR_PIPE",
            "label": "|",
        }
        "ALGR(TR_F)": {
            "key": "TR_AT",
            "label": "@",
        }
        "ALGR(TR_I)": {
            "key": "TR_PILC",
            "label": "¶",
        }
        "ALGR(TR_D)": {
            "key": "TR_YEN",
            "label": "¥",
        }
        "ALGR(TR_H)": {
            "key": "TR_OSTR",
            "label": "Ø",
        }
        "ALGR(TR_P)": {
            "key": "TR_PND",
            "label": "£",
        }
        "ALGR(TR_Q)": {
            "key": "TR_DIAE",
            "label": "¨ (dead)",
        }
        "ALGR(TR_W)": {
            "key": "TR_TILD",
            "label": "~ (dead)",
        }
        "ALGR(TR_U)": {
            "key": "TR_AE",
            "label": "Æ",
        }
        "ALGR(TR_IDOT)": {
            "key": "TR_SS",
            "label": "ß",
        }
        "ALGR(TR_E)": {
            "key": "TR_EURO",
            "label": "€",
        }
        "ALGR(TR_T)": {
            "key": "TR_LIRA",
            "label": "₺",
        }
        "ALGR(TR_Y)": {
            "key": "TR_ACUT",
            "label": "´ (dead)",
        }
        "ALGR(TR_X)": {
            "key": "TR_GRV",
            "label": "` (dead)",
        }
        "ALGR(TR_J)": {
            "key": "TR_LDAQ",
            "label": "«",
        }
        "ALGR(TR_ODIA)": {
            "key": "TR_RDAQ",
            "label": "»",
        }
        "ALGR(TR_V)": {
            "key": "TR_CENT",
            "label": "¢",
        }
        "ALGR(TR_S)": {
            "key": "TR_MICR",
            "label": "µ",
        }
        "ALGR(TR_B)": {
            "key": "TR_MUL",
            "label": "×",
        }
        "ALGR(TR_DOT)": {
            "key": "TR_DIV",
            "label": "÷",
        }
        "ALGR(TR_COMM)": {
            "key": "TR_SHYP",
            "label": "­ (soft hyphen)",
        }
/* Shift+AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │ ³ │ ¤ │   │   │   │   │   │   │ ¿ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │ ® │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │ § │   │ ª │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ ¦ │   │   │ © │   │   │   │ º │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(ALGR(TR_3))": {
            "key": "TR_SUP3",
            "label": "³",
        }
        "S(ALGR(TR_4))": {
            "key": "TR_CURR",
            "label": "¤",
        }
        "S(ALGR(TR_SLSH))": {
            "key": "TR_IQUE",
            "label": "¿",
        }
        "S(ALGR(TR_I))": {
            "key": "TR_REGD",
            "label": "®",
        }
        "S(ALGR(TR_IDOT))": {
            "key": "TR_SECT",
            "label": "§",
        }
        "S(ALGR(TR_A))": {
            "key": "TR_FORD",
            "label": "ª",
        }
        "S(ALGR(TR_LABK))": {
            "key": "TR_BRKP",
            "label": "¦",
        }
        "S(ALGR(TR_V))": {
            "key": "TR_COPY",
            "label": "©",
        }
        "S(ALGR(TR_S))": {
            "key": "TR_MORD",
            "label": "º",
        }
    }
}