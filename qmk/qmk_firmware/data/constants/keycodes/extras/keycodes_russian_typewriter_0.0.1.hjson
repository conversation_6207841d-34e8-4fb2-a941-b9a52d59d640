{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ | │ № │ - │ / │ " │ : │ , │ . │ _ │ ? │ % │ ! │ ; │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Й │ Ц │ У │ К │ Е │ Н │ Г │ Ш │ Щ │ З │ Х │ Ъ │  )  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │ Ф │ Ы │ В │ А │ П │ Р │ О │ Л │ Д │ Ж │ Э │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │ Я │ Ч │ С │ М │ И │ Т │ Ь │ Б │ Ю │ Ё │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "RU_PIPE",
            "label": "|",
        }
        "KC_1": {
            "key": "RU_NUM",
            "label": "№",
        }
        "KC_2": {
            "key": "RU_MINS",
            "label": "-",
        }
        "KC_3": {
            "key": "RU_SLSH",
            "label": "/",
        }
        "KC_4": {
            "key": "RU_DQUO",
            "label": "\"",
        }
        "KC_5": {
            "key": "RU_COLN",
            "label": ":",
        }
        "KC_6": {
            "key": "RU_COMM",
            "label": ",",
        }
        "KC_7": {
            "key": "RU_DOT",
            "label": ".",
        }
        "KC_8": {
            "key": "RU_UNDS",
            "label": "_",
        }
        "KC_9": {
            "key": "RU_QUES",
            "label": "?",
        }
        "KC_0": {
            "key": "RU_PERC",
            "label": "%",
        }
        "KC_MINS": {
            "key": "RU_EXLM",
            "label": "!",
        }
        "KC_EQL": {
            "key": "RU_SCLN",
            "label": ";",
        }
        "KC_Q": {
            "key": "RU_SHTI",
            "label": "Й",
        }
        "KC_W": {
            "key": "RU_TSE",
            "label": "Ц",
        }
        "KC_E": {
            "key": "RU_U",
            "label": "У",
        }
        "KC_R": {
            "key": "RU_KA",
            "label": "К",
        }
        "KC_T": {
            "key": "RU_IE",
            "label": "Е",
        }
        "KC_Y": {
            "key": "RU_EN",
            "label": "Н",
        }
        "KC_U": {
            "key": "RU_GHE",
            "label": "Г",
        }
        "KC_I": {
            "key": "RU_SHA",
            "label": "Ш",
        }
        "KC_O": {
            "key": "RU_SHCH",
            "label": "Щ",
        }
        "KC_P": {
            "key": "RU_ZE",
            "label": "З",
        }
        "KC_LBRC": {
            "key": "RU_HA",
            "label": "Х",
        }
        "KC_RBRC": {
            "key": "RU_HARD",
            "label": "Ъ",
        }
        "KC_BSLS": {
            "key": "RU_RPRN",
            "label": ")",
        }
        "KC_A": {
            "key": "RU_EF",
            "label": "Ф",
        }
        "KC_S": {
            "key": "RU_YERU",
            "label": "Ы",
        }
        "KC_D": {
            "key": "RU_VE",
            "label": "В",
        }
        "KC_F": {
            "key": "RU_A",
            "label": "А",
        }
        "KC_G": {
            "key": "RU_PE",
            "label": "П",
        }
        "KC_H": {
            "key": "RU_ER",
            "label": "Р",
        }
        "KC_J": {
            "key": "RU_O",
            "label": "О",
        }
        "KC_K": {
            "key": "RU_EL",
            "label": "Л",
        }
        "KC_L": {
            "key": "RU_DE",
            "label": "Д",
        }
        "KC_SCLN": {
            "key": "RU_ZHE",
            "label": "Ж",
        }
        "KC_QUOT": {
            "key": "RU_E",
            "label": "Э",
        }
        "KC_Z": {
            "key": "RU_YA",
            "label": "Я",
        }
        "KC_X": {
            "key": "RU_CHE",
            "label": "Ч",
        }
        "KC_C": {
            "key": "RU_ES",
            "label": "С",
        }
        "KC_V": {
            "key": "RU_EM",
            "label": "М",
        }
        "KC_B": {
            "key": "RU_I",
            "label": "И",
        }
        "KC_N": {
            "key": "RU_TE",
            "label": "Т",
        }
        "KC_M": {
            "key": "RU_SOFT",
            "label": "Ь",
        }
        "KC_COMM": {
            "key": "RU_BE",
            "label": "Б",
        }
        "KC_DOT": {
            "key": "RU_YU",
            "label": "Ю",
        }
        "KC_SLSH": {
            "key": "RU_YO",
            "label": "Ё",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ + │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ = │ \ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │  (  │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(RU_PIPE)": {
            "key": "RU_PLUS",
            "label": "+",
        }
        "S(RU_NUM)": {
            "key": "RU_1",
            "label": "1",
        }
        "S(RU_MINS)": {
            "key": "RU_2",
            "label": "2",
        }
        "S(RU_SLSH)": {
            "key": "RU_3",
            "label": "3",
        }
        "S(RU_DQUO)": {
            "key": "RU_4",
            "label": "4",
        }
        "S(RU_COLN)": {
            "key": "RU_5",
            "label": "5",
        }
        "S(RU_COMM)": {
            "key": "RU_6",
            "label": "6",
        }
        "S(RU_DOT)": {
            "key": "RU_7",
            "label": "7",
        }
        "S(RU_UNDS)": {
            "key": "RU_8",
            "label": "8",
        }
        "S(RU_QUES)": {
            "key": "RU_9",
            "label": "9",
        }
        "S(RU_PERC)": {
            "key": "RU_0",
            "label": "0",
        }
        "S(RU_EXLM)": {
            "key": "RU_EQL",
            "label": "=",
        }
        "S(RU_SCLN)": {
            "key": "RU_BSLS",
            "label": "\\",
        }
        "S(RU_RPRN)": {
            "key": "RU_LPRN",
            "label": "(",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │   │   │   │   │   │   │ ₽ │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴─────┤
 * │      │   │   │   │   │   │   │   │   │   │   │   │        │
 * ├──────┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────────┤
 * │        │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┬───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(RU_UNDS)": {
            "key": "RU_RUBL",
            "label": "₽",
        }
    }
}
