{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ` │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ [ │ ] │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ ; │ ' │ # │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ \ │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ / │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "UK_GRV",
            "label": "`"
        },
        "KC_1": {
            "key": "UK_1",
            "label": "1"
        },
        "KC_2": {
            "key": "UK_2",
            "label": "2"
        },
        "KC_3": {
            "key": "UK_3",
            "label": "3"
        },
        "KC_4": {
            "key": "UK_4",
            "label": "4"
        },
        "KC_5": {
            "key": "UK_5",
            "label": "5"
        },
        "KC_6": {
            "key": "UK_6",
            "label": "6"
        },
        "KC_7": {
            "key": "UK_7",
            "label": "7"
        },
        "KC_8": {
            "key": "UK_8",
            "label": "8"
        },
        "KC_9": {
            "key": "UK_9",
            "label": "9"
        },
        "KC_0": {
            "key": "UK_0",
            "label": "0"
        },
        "KC_MINS": {
            "key": "UK_MINS",
            "label": "-"
        },
        "KC_EQL": {
            "key": "UK_EQL",
            "label": "="
        },
        "KC_Q": {
            "key": "UK_Q",
            "label": "Q"
        },
        "KC_W": {
            "key": "UK_W",
            "label": "W"
        },
        "KC_E": {
            "key": "UK_E",
            "label": "E"
        },
        "KC_R": {
            "key": "UK_R",
            "label": "R"
        },
        "KC_T": {
            "key": "UK_T",
            "label": "T"
        },
        "KC_Y": {
            "key": "UK_Y",
            "label": "Y"
        },
        "KC_U": {
            "key": "UK_U",
            "label": "U"
        },
        "KC_I": {
            "key": "UK_I",
            "label": "I"
        },
        "KC_O": {
            "key": "UK_O",
            "label": "O"
        },
        "KC_P": {
            "key": "UK_P",
            "label": "P"
        },
        "KC_LBRC": {
            "key": "UK_LBRC",
            "label": "["
        },
        "KC_RBRC": {
            "key": "UK_RBRC",
            "label": "]"
        },
        "KC_A": {
            "key": "UK_A",
            "label": "A"
        },
        "KC_S": {
            "key": "UK_S",
            "label": "S"
        },
        "KC_D": {
            "key": "UK_D",
            "label": "D"
        },
        "KC_F": {
            "key": "UK_F",
            "label": "F"
        },
        "KC_G": {
            "key": "UK_G",
            "label": "G"
        },
        "KC_H": {
            "key": "UK_H",
            "label": "H"
        },
        "KC_J": {
            "key": "UK_J",
            "label": "J"
        },
        "KC_K": {
            "key": "UK_K",
            "label": "K"
        },
        "KC_L": {
            "key": "UK_L",
            "label": "L"
        },
        "KC_SCLN": {
            "key": "UK_SCLN",
            "label": ";"
        },
        "KC_QUOT": {
            "key": "UK_QUOT",
            "label": "'"
        },
        "KC_NUHS": {
            "key": "UK_HASH",
            "label": "#"
        },
        "KC_NUBS": {
            "key": "UK_BSLS",
            "label": "\\"
        },
        "KC_Z": {
            "key": "UK_Z",
            "label": "Z"
        },
        "KC_X": {
            "key": "UK_X",
            "label": "X"
        },
        "KC_C": {
            "key": "UK_C",
            "label": "C"
        },
        "KC_V": {
            "key": "UK_V",
            "label": "V"
        },
        "KC_B": {
            "key": "UK_B",
            "label": "B"
        },
        "KC_N": {
            "key": "UK_N",
            "label": "N"
        },
        "KC_M": {
            "key": "UK_M",
            "label": "M"
        },
        "KC_COMM": {
            "key": "UK_COMM",
            "label": ","
        },
        "KC_DOT": {
            "key": "UK_DOT",
            "label": "."
        },
        "KC_SLSH": {
            "key": "UK_SLSH",
            "label": "/"
        },

/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¬ │ ! │ " │ £ │ $ │ % │ ^ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ { │ } │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │ : │ @ │ ~ │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │   │ < │ > │ ? │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(UK_GRV)": {
            "key": "UK_NOT",
            "label": "¬"
        },
        "S(UK_1)": {
            "key": "UK_EXLM",
            "label": "!"
        },
        "S(UK_2)": {
            "key": "UK_DQUO",
            "label": "\""
        },
        "S(UK_3)": {
            "key": "UK_PND",
            "label": "£"
        },
        "S(UK_4)": {
            "key": "UK_DLR",
            "label": "$"
        },
        "S(UK_5)": {
            "key": "UK_PERC",
            "label": "%"
        },
        "S(UK_6)": {
            "key": "UK_CIRC",
            "label": "^"
        },
        "S(UK_7)": {
            "key": "UK_AMPR",
            "label": "&"
        },
        "S(UK_8)": {
            "key": "UK_ASTR",
            "label": "*"
        },
        "S(UK_9)": {
            "key": "UK_LPRN",
            "label": "("
        },
        "S(UK_0)": {
            "key": "UK_RPRN",
            "label": ")"
        },
        "S(UK_MINS)": {
            "key": "UK_UNDS",
            "label": "_"
        },
        "S(UK_EQL)": {
            "key": "UK_PLUS",
            "label": "+"
        },
        "S(UK_LBRC)": {
            "key": "UK_LCBR",
            "label": "{"
        },
        "S(UK_RBRC)": {
            "key": "UK_RCBR",
            "label": "}"
        },
        "S(UK_SCLN)": {
            "key": "UK_COLN",
            "label": ":"
        },
        "S(UK_QUOT)": {
            "key": "UK_AT",
            "label": "@"
        },
        "S(UK_HASH)": {
            "key": "UK_TILD",
            "label": "~"
        },
        "S(UK_BSLS)": {
            "key": "UK_PIPE",
            "label": "|"
        },
        "S(UK_COMM)": {
            "key": "UK_LABK",
            "label": "<"
        },
        "S(UK_DOT)": {
            "key": "UK_RABK",
            "label": ">"
        },
        "S(UK_SLSH)": {
            "key": "UK_QUES",
            "label": "?"
        },

/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ¦ │   │   │   │ € │   │   │   │   │   │   │   │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ É │   │   │   │ Ú │ Í │ Ó │   │   │   │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ Á │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │   │   │   │   │   │   │   │   │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(UK_GRV)": {
            "key": "UK_BRKP",
            "label": "¦"
        },
        "ALGR(UK_4)": {
            "key": "UK_EURO",
            "label": "€"
        },
        "ALGR(KC_E)": {
            "key": "UK_EACU",
            "label": "É"
        },
        "ALGR(KC_U)": {
            "key": "UK_UACU",
            "label": "Ú"
        },
        "ALGR(KC_I)": {
            "key": "UK_IACU",
            "label": "Í"
        },
        "ALGR(KC_O)": {
            "key": "UK_OACU",
            "label": "Ó"
        },
        "ALGR(KC_A)": {
            "key": "UK_AACU",
            "label": "Á"
        }
    }
}