{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ' │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ - │ = │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Y │ U │ I │ O │ P │ ´ │ [ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ç │ ~ │ ] │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────┤
 * │    │ \ │ Z │ X │ C │ V │ B │ N │ M │ , │ . │ ; │ / │      │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬──┴─┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "BR_QUOT",
            "label": "'",
        }
        "KC_1": {
            "key": "BR_1",
            "label": "1",
        }
        "KC_2": {
            "key": "BR_2",
            "label": "2",
        }
        "KC_3": {
            "key": "BR_3",
            "label": "3",
        }
        "KC_4": {
            "key": "BR_4",
            "label": "4",
        }
        "KC_5": {
            "key": "BR_5",
            "label": "5",
        }
        "KC_6": {
            "key": "BR_6",
            "label": "6",
        }
        "KC_7": {
            "key": "BR_7",
            "label": "7",
        }
        "KC_8": {
            "key": "BR_8",
            "label": "8",
        }
        "KC_9": {
            "key": "BR_9",
            "label": "9",
        }
        "KC_0": {
            "key": "BR_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "BR_MINS",
            "label": "-",
        }
        "KC_EQL": {
            "key": "BR_EQL",
            "label": "=",
        }
        "KC_Q": {
            "key": "BR_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "BR_W",
            "label": "W",
        }
        "KC_E": {
            "key": "BR_E",
            "label": "E",
        }
        "KC_R": {
            "key": "BR_R",
            "label": "R",
        }
        "KC_T": {
            "key": "BR_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "BR_Y",
            "label": "Y",
        }
        "KC_U": {
            "key": "BR_U",
            "label": "U",
        }
        "KC_I": {
            "key": "BR_I",
            "label": "I",
        }
        "KC_O": {
            "key": "BR_O",
            "label": "O",
        }
        "KC_P": {
            "key": "BR_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "BR_ACUT",
            "label": "´ (dead)",
        }
        "KC_RBRC": {
            "key": "BR_LBRC",
            "label": "[",
        }
        "KC_A": {
            "key": "BR_A",
            "label": "A",
        }
        "KC_S": {
            "key": "BR_S",
            "label": "S",
        }
        "KC_D": {
            "key": "BR_D",
            "label": "D",
        }
        "KC_F": {
            "key": "BR_F",
            "label": "F",
        }
        "KC_G": {
            "key": "BR_G",
            "label": "G",
        }
        "KC_H": {
            "key": "BR_H",
            "label": "H",
        }
        "KC_J": {
            "key": "BR_J",
            "label": "J",
        }
        "KC_K": {
            "key": "BR_K",
            "label": "K",
        }
        "KC_L": {
            "key": "BR_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "BR_CCED",
            "label": "Ç",
        }
        "KC_QUOT": {
            "key": "BR_TILD",
            "label": "~ (dead)",
        }
        "KC_BSLS": {
            "key": "BR_RBRC",
            "label": "]",
        }
        "KC_NUBS": {
            "key": "BR_BSLS",
            "label": "\\",
        }
        "KC_Z": {
            "key": "BR_Z",
            "label": "Z",
        }
        "KC_X": {
            "key": "BR_X",
            "label": "X",
        }
        "KC_C": {
            "key": "BR_C",
            "label": "C",
        }
        "KC_V": {
            "key": "BR_V",
            "label": "V",
        }
        "KC_B": {
            "key": "BR_B",
            "label": "B",
        }
        "KC_N": {
            "key": "BR_N",
            "label": "N",
        }
        "KC_M": {
            "key": "BR_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "BR_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "BR_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "BR_SCLN",
            "label": ";",
        }
        "KC_INT1": {
            "key": "BR_SLSH",
            "label": "/",
        }
        "KC_PCMM": {
            "key": "BR_PDOT",
            "label": ".",
        }
        "KC_PDOT": {
            "key": "BR_PCMM",
            "label": ",",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ " │ ! │ @ │ # │ $ │ % │ ¨ │ & │ * │ ( │ ) │ _ │ + │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │ ` │ { │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │ ^ │ } │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────┤
 * │    │ | │   │   │   │   │   │   │   │ < │ > │ : │ ? │      │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬──┴─┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(BR_QUOT)": {
            "key": "BR_DQUO",
            "label": "\"",
        }
        "S(BR_1)": {
            "key": "BR_EXLM",
            "label": "!",
        }
        "S(BR_2)": {
            "key": "BR_AT",
            "label": "@",
        }
        "S(BR_3)": {
            "key": "BR_HASH",
            "label": "#",
        }
        "S(BR_4)": {
            "key": "BR_DLR",
            "label": "$",
        }
        "S(BR_5)": {
            "key": "BR_PERC",
            "label": "%",
        }
        "S(BR_6)": {
            "key": "BR_DIAE",
            "label": "¨ (dead)",
        }
        "S(BR_7)": {
            "key": "BR_AMPR",
            "label": "&",
        }
        "S(BR_8)": {
            "key": "BR_ASTR",
            "label": "*",
        }
        "S(BR_9)": {
            "key": "BR_LPRN",
            "label": "(",
        }
        "S(BR_0)": {
            "key": "BR_RPRN",
            "label": ")",
        }
        "S(BR_MINS)": {
            "key": "BR_UNDS",
            "label": "_",
        }
        "S(BR_EQL)": {
            "key": "BR_PLUS",
            "label": "+",
        }
        "S(BR_ACUT)": {
            "key": "BR_GRV",
            "label": "` (dead)",
        }
        "S(BR_LBRC)": {
            "key": "BR_LCBR",
            "label": "{",
        }
        "S(BR_TILD)": {
            "key": "BR_CIRC",
            "label": "^ (dead)",
        }
        "S(BR_RBRC)": {
            "key": "BR_RCBR",
            "label": "}",
        }
        "S(BR_BSLS)": {
            "key": "BR_PIPE",
            "label": "|",
        }
        "S(BR_COMM)": {
            "key": "BR_LABK",
            "label": "<",
        }
        "S(BR_DOT)": {
            "key": "BR_RABK",
            "label": ">",
        }
        "S(BR_SCLN)": {
            "key": "BR_COLN",
            "label": ":",
        }
        "S(BR_SLSH)": {
            "key": "BR_QUES",
            "label": "?",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │ ¹ │ ² │ ³ │ £ │ ¢ │ ¬ │   │   │   │   │   │ § │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │ ° │   │   │   │   │   │   │   │   │ ª │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ º │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴────┤
 * │    │   │   │   │ ₢ │   │   │   │   │   │   │   │   │      │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬──┴─┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(BR_1)": {
            "key": "BR_SUP1",
            "label": "¹",
        }
        "ALGR(BR_2)": {
            "key": "BR_SUP2",
            "label": "²",
        }
        "ALGR(BR_3)": {
            "key": "BR_SUP3",
            "label": "³",
        }
        "ALGR(BR_4)": {
            "key": "BR_PND",
            "label": "£",
        }
        "ALGR(BR_5)": {
            "key": "BR_CENT",
            "label": "¢",
        }
        "ALGR(BR_6)": {
            "key": "BR_NOT",
            "label": "¬",
        }
        "ALGR(BR_EQL)": {
            "key": "BR_SECT",
            "label": "§",
        }
        "ALGR(BR_E)": {
            "key": "BR_DEG",
            "label": "°",
        }
        "ALGR(BR_LBRC)": {
            "key": "BR_FORD",
            "label": "ª",
        }
        "ALGR(BR_RBRC)": {
            "key": "BR_MORD",
            "label": "º",
        }
        "ALGR(BR_C)": {
            "key": "BR_CRUZ",
            "label": "₢",
        }
    }
}