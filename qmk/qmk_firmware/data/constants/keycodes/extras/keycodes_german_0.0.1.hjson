{
    "aliases": {
/*
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ^ │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │ 7 │ 8 │ 9 │ 0 │ ß │ ´ │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ Q │ W │ E │ R │ T │ Z │ U │ I │ O │ P │ Ü │ + │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │ A │ S │ D │ F │ G │ H │ J │ K │ L │ Ö │ Ä │ # │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ < │ Y │ X │ C │ V │ B │ N │ M │ , │ . │ - │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "KC_GRV": {
            "key": "DE_CIRC",
            "label": "^ (dead)",
        }
        "KC_1": {
            "key": "DE_1",
            "label": "1",
        }
        "KC_2": {
            "key": "DE_2",
            "label": "2",
        }
        "KC_3": {
            "key": "DE_3",
            "label": "3",
        }
        "KC_4": {
            "key": "DE_4",
            "label": "4",
        }
        "KC_5": {
            "key": "DE_5",
            "label": "5",
        }
        "KC_6": {
            "key": "DE_6",
            "label": "6",
        }
        "KC_7": {
            "key": "DE_7",
            "label": "7",
        }
        "KC_8": {
            "key": "DE_8",
            "label": "8",
        }
        "KC_9": {
            "key": "DE_9",
            "label": "9",
        }
        "KC_0": {
            "key": "DE_0",
            "label": "0",
        }
        "KC_MINS": {
            "key": "DE_SS",
            "label": "ß",
        }
        "KC_EQL": {
            "key": "DE_ACUT",
            "label": "´ (dead)",
        }
        "KC_Q": {
            "key": "DE_Q",
            "label": "Q",
        }
        "KC_W": {
            "key": "DE_W",
            "label": "W",
        }
        "KC_E": {
            "key": "DE_E",
            "label": "E",
        }
        "KC_R": {
            "key": "DE_R",
            "label": "R",
        }
        "KC_T": {
            "key": "DE_T",
            "label": "T",
        }
        "KC_Y": {
            "key": "DE_Z",
            "label": "Z",
        }
        "KC_U": {
            "key": "DE_U",
            "label": "U",
        }
        "KC_I": {
            "key": "DE_I",
            "label": "I",
        }
        "KC_O": {
            "key": "DE_O",
            "label": "O",
        }
        "KC_P": {
            "key": "DE_P",
            "label": "P",
        }
        "KC_LBRC": {
            "key": "DE_UDIA",
            "label": "Ü",
        }
        "KC_RBRC": {
            "key": "DE_PLUS",
            "label": "+",
        }
        "KC_A": {
            "key": "DE_A",
            "label": "A",
        }
        "KC_S": {
            "key": "DE_S",
            "label": "S",
        }
        "KC_D": {
            "key": "DE_D",
            "label": "D",
        }
        "KC_F": {
            "key": "DE_F",
            "label": "F",
        }
        "KC_G": {
            "key": "DE_G",
            "label": "G",
        }
        "KC_H": {
            "key": "DE_H",
            "label": "H",
        }
        "KC_J": {
            "key": "DE_J",
            "label": "J",
        }
        "KC_K": {
            "key": "DE_K",
            "label": "K",
        }
        "KC_L": {
            "key": "DE_L",
            "label": "L",
        }
        "KC_SCLN": {
            "key": "DE_ODIA",
            "label": "Ö",
        }
        "KC_QUOT": {
            "key": "DE_ADIA",
            "label": "Ä",
        }
        "KC_NUHS": {
            "key": "DE_HASH",
            "label": "#",
        }
        "KC_NUBS": {
            "key": "DE_LABK",
            "label": "<",
        }
        "KC_Z": {
            "key": "DE_Y",
            "label": "Y",
        }
        "KC_X": {
            "key": "DE_X",
            "label": "X",
        }
        "KC_C": {
            "key": "DE_C",
            "label": "C",
        }
        "KC_V": {
            "key": "DE_V",
            "label": "V",
        }
        "KC_B": {
            "key": "DE_B",
            "label": "B",
        }
        "KC_N": {
            "key": "DE_N",
            "label": "N",
        }
        "KC_M": {
            "key": "DE_M",
            "label": "M",
        }
        "KC_COMM": {
            "key": "DE_COMM",
            "label": ",",
        }
        "KC_DOT": {
            "key": "DE_DOT",
            "label": ".",
        }
        "KC_SLSH": {
            "key": "DE_MINS",
            "label": "-",
        }
/* Shifted symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │ ° │ ! │ " │ § │ $ │ % │ & │ / │ ( │ ) │ = │ ? │ ` │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │   │   │   │   │   │   │   │   │   │   │   │ * │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │ ' │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ > │   │   │   │   │   │   │   │ ; │ : │ _ │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "S(DE_CIRC)": {
            "key": "DE_DEG",
            "label": "°",
        }
        "S(DE_1)": {
            "key": "DE_EXLM",
            "label": "!",
        }
        "S(DE_2)": {
            "key": "DE_DQUO",
            "label": "\"",
        }
        "S(DE_3)": {
            "key": "DE_SECT",
            "label": "§",
        }
        "S(DE_4)": {
            "key": "DE_DLR",
            "label": "$",
        }
        "S(DE_5)": {
            "key": "DE_PERC",
            "label": "%",
        }
        "S(DE_6)": {
            "key": "DE_AMPR",
            "label": "&",
        }
        "S(DE_7)": {
            "key": "DE_SLSH",
            "label": "/",
        }
        "S(DE_8)": {
            "key": "DE_LPRN",
            "label": "(",
        }
        "S(DE_9)": {
            "key": "DE_RPRN",
            "label": ")",
        }
        "S(DE_0)": {
            "key": "DE_EQL",
            "label": "=",
        }
        "S(DE_SS)": {
            "key": "DE_QUES",
            "label": "?",
        }
        "S(DE_ACUT)": {
            "key": "DE_GRV",
            "label": "` (dead)",
        }
        "S(DE_PLUS)": {
            "key": "DE_ASTR",
            "label": "*",
        }
        "S(DE_HASH)": {
            "key": "DE_QUOT",
            "label": "'",
        }
        "S(DE_LABK)": {
            "key": "DE_RABK",
            "label": ">",
        }
        "S(DE_COMM)": {
            "key": "DE_SCLN",
            "label": ";",
        }
        "S(DE_DOT)": {
            "key": "DE_COLN",
            "label": ":",
        }
        "S(DE_MINS)": {
            "key": "DE_UNDS",
            "label": "_",
        }
/* AltGr symbols
 * ┌───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───┬───────┐
 * │   │   │ ² │ ³ │   │   │   │ { │ [ │ ] │ } │ \ │   │       │
 * ├───┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─────┤
 * │     │ @ │   │ € │   │   │   │   │   │   │   │   │ ~ │     │
 * ├─────┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┬──┴┐    │
 * │      │   │   │   │   │   │   │   │   │   │   │   │   │    │
 * ├────┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴─┬─┴───┴────┤
 * │    │ | │   │   │   │   │   │   │ µ │   │   │   │          │
 * ├────┼───┴┬──┴─┬─┴───┴───┴───┴───┴───┴──┬┴───┼───┴┬────┬────┤
 * │    │    │    │                        │    │    │    │    │
 * └────┴────┴────┴────────────────────────┴────┴────┴────┴────┘
 */
        "ALGR(DE_2)": {
            "key": "DE_SUP2",
            "label": "²",
        }
        "ALGR(DE_3)": {
            "key": "DE_SUP3",
            "label": "³",
        }
        "ALGR(DE_7)": {
            "key": "DE_LCBR",
            "label": "{",
        }
        "ALGR(DE_8)": {
            "key": "DE_LBRC",
            "label": "[",
        }
        "ALGR(DE_9)": {
            "key": "DE_RBRC",
            "label": "]",
        }
        "ALGR(DE_0)": {
            "key": "DE_RCBR",
            "label": "}",
        }
        "ALGR(DE_SS)": {
            "key": "DE_BSLS",
            "label": "\\",
        }
        "ALGR(DE_Q)": {
            "key": "DE_AT",
            "label": "@",
        }
        "ALGR(DE_E)": {
            "key": "DE_EURO",
            "label": "€",
        }
        "ALGR(DE_PLUS)": {
            "key": "DE_TILD",
            "label": "~",
        }
        "ALGR(DE_LABK)": {
            "key": "DE_PIPE",
            "label": "|",
        }
        "ALGR(DE_M)": {
            "key": "DE_MICR",
            "label": "µ",
        }
    }
}