// This file maps keys between `rules.mk` and `info.json`. It is used by QMK
// to correctly and consistently map back and forth between the two systems.
{
    // Format:
    // <rules.mk key>: {"info_key": <info.json key>, ["value_type": <value_type>], ["to_json": <true/false>], ["to_c": <true/false>]}
    // value_type: one of "array", "array.int", "bool", "int", "list", "hex", "mapping", "str", "raw"
    // to_json: Default `true`. Set to `false` to exclude this mapping from info.json
    // to_c: Default `true`. Set to `false` to exclude this mapping from rules.mk
    // warn_duplicate: Default `true`. Set to `false` to turn off warning when a value exists in both places
    // deprecated: Default `false`. Set to `true` to turn on warning when a value exists
    // invalid: Default `false`. Set to `true` to generate errors when a value exists
    // replace_with: use with a key marked deprecated or invalid to designate a replacement

    "AUDIO_DRIVER": {"info_key": "audio.driver"},
    "BACKLIGHT_DRIVER": {"info_key": "backlight.driver"},
    "BLUETOOTH_DRIVER": {"info_key": "bluetooth.driver"},
    "BOARD": {"info_key": "board"},
    "BOOTLOADER": {"info_key": "bootloader", "warn_duplicate": false},
    "BOOTMAGIC_ENABLE": {"info_key": "bootmagic.enabled", "value_type": "bool"},
    "CAPS_WORD_ENABLE": {"info_key": "caps_word.enabled", "value_type": "bool"},
    "DIP_SWITCH_ENABLE": {"info_key": "dip_switch.enabled", "value_type": "bool"},
    "DEBOUNCE_TYPE": {"info_key": "build.debounce_type"},
    "EEPROM_DRIVER": {"info_key": "eeprom.driver"},
    "ENCODER_ENABLE": {"info_key": "encoder.enabled", "value_type": "bool"},
    "ENCODER_DRIVER": {"info_key": "encoder.driver"},
    "FIRMWARE_FORMAT": {"info_key": "build.firmware_format"},
    "HAPTIC_DRIVER": {"info_key": "haptic.driver"},
    "JOYSTICK_DRIVER": {"info_key": "joystick.driver"},
    "JOYSTICK_ENABLE": {"info_key": "joystick.enabled", "value_type": "bool"},
    "KEYBOARD_SHARED_EP": {"info_key": "usb.shared_endpoint.keyboard", "value_type": "bool"},
    "LAYOUTS": {"info_key": "community_layouts", "value_type": "list"},
    "LED_MATRIX_DRIVER": {"info_key": "led_matrix.driver"},
    "LTO_ENABLE": {"info_key": "build.lto", "value_type": "bool"},
    "MCU": {"info_key": "processor", "warn_duplicate": false},
    "MOUSE_SHARED_EP": {"info_key": "usb.shared_endpoint.mouse", "value_type": "bool"},
    "MOUSEKEY_ENABLE": {"info_key": "mouse_key.enabled", "value_type": "bool"},
    "NO_USB_STARTUP_CHECK": {"info_key": "usb.no_startup_check", "value_type": "bool"},
    "PIN_COMPATIBLE": {"info_key": "pin_compatible"},
    "PLATFORM_KEY": {"info_key": "platform_key", "to_json": false},
    "PS2_DRIVER": {"info_key": "ps2.driver"},
    "PS2_ENABLE": {"info_key": "ps2.enabled", "value_type": "bool"},
    "PS2_MOUSE_ENABLE": {"info_key": "ps2.mouse_enabled", "value_type": "bool"},
    "RGB_MATRIX_DRIVER": {"info_key": "rgb_matrix.driver"},
    "RGBLIGHT_DRIVER": {"info_key": "rgblight.driver"},
    "SECURE_ENABLE": {"info_key": "secure.enabled", "value_type": "bool"},
    "SERIAL_DRIVER": {"info_key": "split.serial.driver"},
    "SPLIT_KEYBOARD": {"info_key": "split.enabled", "value_type": "bool"},
    "SPLIT_TRANSPORT": {"info_key": "split.transport.protocol", "to_c": false},
    "STENO_ENABLE": {"info_key": "stenography.enabled", "value_type": "bool"},
    "STENO_PROTOCOL": {"info_key": "stenography.protocol"},
    "USB_WAIT_FOR_ENUMERATION": {"info_key": "usb.wait_for_enumeration", "value_type": "bool"},
    "WEAR_LEVELING_DRIVER": {"info_key": "eeprom.wear_leveling.driver"},
    "WS2812_DRIVER": {"info_key": "ws2812.driver"},

    // Items we want flagged in lint
    "CTPC": {"info_key": "_deprecated.ctpc", "deprecated": true, "replace_with": "CONVERT_TO=proton_c"},
    "CONVERT_TO_PROTON_C": {"info_key": "_deprecated.ctpc", "deprecated": true, "replace_with": "CONVERT_TO=proton_c"},
    "DEFAULT_FOLDER": {"info_key": "_deprecated.default_folder", "deprecated": true},
    "VIAL_ENABLE": {"info_key": "_invalid.vial", "invalid": true}
}
