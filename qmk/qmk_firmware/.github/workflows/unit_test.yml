name: Unit Tests

permissions:
  contents: read

on:
  push:
    branches:
    - master
    - develop
  pull_request:
    paths:
    - 'builddefs/**'
    - 'quantum/**'
    - 'platforms/**'
    - 'tmk_core/**'
    - 'tests/**'
    - '*.mk'
    - 'Makefile'
    - '.github/workflows/unit_test.yml'

jobs:
  test:
    runs-on: ubuntu-latest

    container: ghcr.io/qmk/qmk_cli

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - name: Install dependencies
      run: pip3 install -r requirements-dev.txt
    - name: Run tests
      run: qmk test-c
