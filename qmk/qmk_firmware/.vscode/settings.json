// Place your settings in this file to overwrite default and user settings.
{
    // Unofficially, QMK uses spaces for indentation
    "editor.insertSpaces": true,
    // Configure glob patterns for excluding files and folders.
    "files.exclude": {
        "**/.build": true,
        "**/*.hex": true,
        "**/*.bin": true,
        "**/*.uf2": true
    },
    "files.associations": {
        "*.h": "c",
        "*.c": "c",
        "*.inc": "c",
        "*.cpp": "cpp",
        "*.hpp": "cpp",
        "xstddef": "c",
        "type_traits": "c",
        "utility": "c",
        "ranges": "c"
    },
    "[markdown]": {
        "editor.trimAutoWhitespace": false,
        "files.trimTrailingWhitespace": false
    },
    "python.formatting.provider": "yapf",
    "[json]": {
        "editor.formatOnSave": false
    },
    "clangd.arguments": [
        "--header-insertion=never"
    ]
}
