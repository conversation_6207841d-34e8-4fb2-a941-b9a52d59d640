{"manufacturer": "Onspry", "keyboard_name": "Zero", "maintainer": "Onspry", "build": {"lto": true}, "bootloader": "rp2040", "bootmagic": {"enabled": true, "matrix": [0, 1]}, "diode_direction": "COL2ROW", "features": {"bootmagic": true, "command": false, "console": false, "extrakey": true, "mousekey": true, "nkro": true, "oled": true, "rgb_matrix": true, "rgblight": false}, "matrix_pins": {"direct": [["GP22", "GP20", "GP23", "GP26", "GP29", "GP0", "GP12"], ["GP19", "GP18", "GP24", "GP27", "GP1", "GP2", "GP8"], ["GP17", "GP16", "GP25", "GP28", "GP3", "GP9", null], [null, null, null, "GP14", "GP15", "GP11", null]]}, "processor": "RP2040", "url": "https://github.com/onspry/kbd_firmware", "usb": {"device_version": "1.0.0", "pid": "0x0001", "vid": "0xFEED"}, "layouts": {"LAYOUT_split_3x6_3": {"layout": [{"matrix": [0, 0], "x": 0, "y": 0.3}, {"matrix": [0, 1], "x": 1, "y": 0.3}, {"matrix": [0, 2], "x": 2, "y": 0.1}, {"matrix": [0, 3], "x": 3, "y": 0}, {"matrix": [0, 4], "x": 4, "y": 0.1}, {"matrix": [0, 5], "x": 5, "y": 0.2}, {"matrix": [4, 5], "x": 9, "y": 0.2}, {"matrix": [4, 4], "x": 10, "y": 0.1}, {"matrix": [4, 3], "x": 11, "y": 0}, {"matrix": [4, 2], "x": 12, "y": 0.1}, {"matrix": [4, 1], "x": 13, "y": 0.3}, {"matrix": [4, 0], "x": 14, "y": 0.3}, {"matrix": [1, 0], "x": 0, "y": 1.3}, {"matrix": [1, 1], "x": 1, "y": 1.3}, {"matrix": [1, 2], "x": 2, "y": 1.1}, {"matrix": [1, 3], "x": 3, "y": 1}, {"matrix": [1, 4], "x": 4, "y": 1.1}, {"matrix": [1, 5], "x": 5, "y": 1.2}, {"matrix": [5, 5], "x": 9, "y": 1.2}, {"matrix": [5, 4], "x": 10, "y": 1.1}, {"matrix": [5, 3], "x": 11, "y": 1}, {"matrix": [5, 2], "x": 12, "y": 1.1}, {"matrix": [5, 1], "x": 13, "y": 1.3}, {"matrix": [5, 0], "x": 14, "y": 1.3}, {"matrix": [2, 0], "x": 0, "y": 2.3}, {"matrix": [2, 1], "x": 1, "y": 2.3}, {"matrix": [2, 2], "x": 2, "y": 2.1}, {"matrix": [2, 3], "x": 3, "y": 2}, {"matrix": [2, 4], "x": 4, "y": 2.1}, {"matrix": [2, 5], "x": 5, "y": 2.2}, {"matrix": [6, 5], "x": 9, "y": 2.2}, {"matrix": [6, 4], "x": 10, "y": 2.1}, {"matrix": [6, 3], "x": 11, "y": 2}, {"matrix": [6, 2], "x": 12, "y": 2.1}, {"matrix": [6, 1], "x": 13, "y": 2.3}, {"matrix": [6, 0], "x": 14, "y": 2.3}, {"matrix": [3, 3], "x": 4, "y": 3.7}, {"matrix": [3, 4], "x": 5, "y": 3.7}, {"matrix": [3, 5], "x": 6, "y": 3.2, "h": 1.5}, {"matrix": [7, 5], "x": 8, "y": 3.2, "h": 1.5}, {"matrix": [7, 4], "x": 9, "y": 3.7}, {"matrix": [7, 3], "x": 10, "y": 3.7}]}}, "rgb_matrix": {"driver": "ws2812", "max_brightness": 50, "animations": {"alphas_mods": true, "gradient_up_down": true, "gradient_left_right": true, "breathing": true, "band_sat": true, "band_val": true, "band_pinwheel_sat": true, "band_pinwheel_val": true, "band_spiral_sat": true, "band_spiral_val": true, "cycle_all": true, "cycle_left_right": true, "cycle_up_down": true, "rainbow_moving_chevron": true, "cycle_out_in": true, "cycle_out_in_dual": true, "cycle_pinwheel": true, "cycle_spiral": true, "dual_beacon": true, "rainbow_beacon": true, "rainbow_pinwheels": true, "raindrops": true, "jellybean_raindrops": true, "hue_breathing": true, "hue_pendulum": true, "hue_wave": true, "pixel_fractal": true, "pixel_flow": true, "pixel_rain": true, "typing_heatmap": true, "digital_rain": true, "solid_reactive_simple": true, "solid_reactive": true, "solid_reactive_wide": true, "solid_reactive_multiwide": true, "solid_reactive_cross": true, "solid_reactive_multicross": true, "solid_reactive_nexus": true, "solid_reactive_multinexus": true, "splash": true, "multisplash": true, "solid_splash": true, "solid_multisplash": true}, "split_count": [23, 23], "layout": [{"matrix": [3, 5], "x": 95, "y": 63, "flags": 4}, {"matrix": [2, 5], "x": 85, "y": 39, "flags": 4}, {"matrix": [1, 5], "x": 85, "y": 21, "flags": 4}, {"matrix": [0, 5], "x": 85, "y": 4, "flags": 4}, {"matrix": [0, 4], "x": 68, "y": 2, "flags": 4}, {"matrix": [1, 4], "x": 68, "y": 19, "flags": 4}, {"matrix": [2, 4], "x": 68, "y": 37, "flags": 4}, {"matrix": [3, 4], "x": 80, "y": 58, "flags": 4}, {"matrix": [3, 3], "x": 60, "y": 55, "flags": 4}, {"matrix": [2, 3], "x": 50, "y": 35, "flags": 4}, {"matrix": [1, 3], "x": 50, "y": 13, "flags": 4}, {"matrix": [0, 3], "x": 50, "y": 0, "flags": 4}, {"matrix": [0, 2], "x": 33, "y": 3, "flags": 4}, {"matrix": [1, 2], "x": 33, "y": 20, "flags": 4}, {"matrix": [2, 2], "x": 33, "y": 37, "flags": 4}, {"matrix": [2, 1], "x": 16, "y": 42, "flags": 4}, {"matrix": [1, 1], "x": 16, "y": 24, "flags": 4}, {"matrix": [0, 1], "x": 16, "y": 7, "flags": 4}, {"matrix": [0, 0], "x": 0, "y": 7, "flags": 4}, {"matrix": [1, 0], "x": 0, "y": 24, "flags": 4}, {"matrix": [2, 0], "x": 0, "y": 41, "flags": 4}, {"matrix": [0, 6], "x": 103, "y": 17, "flags": 4}, {"matrix": [1, 6], "x": 103, "y": 24, "flags": 4}, {"matrix": [7, 5], "x": 129, "y": 63, "flags": 4}, {"matrix": [6, 5], "x": 139, "y": 39, "flags": 4}, {"matrix": [5, 5], "x": 139, "y": 21, "flags": 4}, {"matrix": [4, 5], "x": 139, "y": 4, "flags": 4}, {"matrix": [4, 4], "x": 156, "y": 2, "flags": 4}, {"matrix": [5, 4], "x": 156, "y": 19, "flags": 4}, {"matrix": [6, 4], "x": 156, "y": 37, "flags": 4}, {"matrix": [7, 4], "x": 144, "y": 58, "flags": 4}, {"matrix": [7, 3], "x": 164, "y": 55, "flags": 4}, {"matrix": [6, 3], "x": 174, "y": 35, "flags": 4}, {"matrix": [5, 3], "x": 174, "y": 13, "flags": 4}, {"matrix": [4, 3], "x": 174, "y": 0, "flags": 4}, {"matrix": [4, 2], "x": 191, "y": 3, "flags": 4}, {"matrix": [5, 2], "x": 191, "y": 20, "flags": 4}, {"matrix": [6, 2], "x": 191, "y": 37, "flags": 4}, {"matrix": [6, 1], "x": 208, "y": 42, "flags": 4}, {"matrix": [5, 1], "x": 208, "y": 24, "flags": 4}, {"matrix": [4, 1], "x": 208, "y": 7, "flags": 4}, {"matrix": [4, 0], "x": 224, "y": 7, "flags": 4}, {"matrix": [5, 0], "x": 224, "y": 24, "flags": 4}, {"matrix": [6, 0], "x": 224, "y": 41, "flags": 4}, {"matrix": [4, 6], "x": 122, "y": 17, "flags": 4}, {"matrix": [5, 6], "x": 122, "y": 24, "flags": 4}]}, "rgblight": {"led_count": 46, "split": true, "split_count": [23, 23], "max_brightness": 50, "animations": {"alternating": true, "breathing": true, "christmas": true, "knight": true, "rainbow_mood": true, "rainbow_swirl": true, "rgb_test": true, "snake": true, "static_gradient": true, "twinkle": true}}, "split": {"enabled": true, "bootmagic": {"matrix": [4, 1]}, "matrix_pins": {"right": {"direct": [["GP8", "GP9", "GP3", "GP2", "GP1", "GP27", "GP12"], ["GP11", "GP14", "GP4", "GP0", "GP28", "GP26", "GP23"], ["GP15", "GP18", "GP5", "GP29", "GP20", "GP22", null], [null, null, null, "GP16", "GP17", "GP19", null]]}}, "transport": {"protocol": "serial", "sync": {"matrix_state": true, "layer_state": true}, "watchdog": true}}, "ws2812": {"driver": "vendor", "pin": "GP10"}}